#!/bin/bash

# Test script for graceful shutdown
echo "Building flyshadow_mac_tun..."
cd flyshadow_mac_tun
cargo build --release

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Starting flyshadow_mac_tun in background..."
./target/release/flyshadow_mac_tun &
PID=$!

echo "Process started with PID: $PID"
sleep 2

echo "Sending SIGTERM to process..."
kill -TERM $PID

echo "Waiting for graceful shutdown..."
wait $PID
EXIT_CODE=$?

echo "Process exited with code: $EXIT_CODE"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Graceful shutdown test PASSED"
else
    echo "❌ Graceful shutdown test FAILED"
fi
