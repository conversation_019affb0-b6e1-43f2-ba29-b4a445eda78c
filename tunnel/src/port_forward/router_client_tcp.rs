use crate::port_forward::route_table::RouteTable;
use crate::port_forward::router::Route;
use crate::tunnel::tunnel::Tunnel;
use flyshadow_common::tunnel::tunnel_package::{PackageCmd, PackageProtocol, TunnelPackage};
use std::sync::Arc;
use tokio::io::AsyncWriteExt;
use tokio::net::TcpStream;
use tokio::spawn;
use tokio::sync::mpsc::unbounded_channel;
use tokio::sync::RwLock;
use tokio::time::Instant;

impl Route {
    /// 创建一个新的路由，指定协议、源地址、目标地址、隧道和路由表。
    pub async fn new_client_tcp(
        tcp_stream: TcpStream,
        target_addr: String,
        source_addr: String,
        tunnel: Arc<Tunnel>,
        route_table: Arc<RouteTable>,
        uuid: String,
    ) -> Arc<Route> {
        // 初始化活跃时间和其他路由参数。
        let active_time = Arc::new(RwLock::new(Instant::now()));

        // 为 TCP 协议创建一个通道用于发送数据。
        let channel = unbounded_channel::<Vec<u8>>();

        // 发送连接请求
        let tunnel_package = TunnelPackage::new(
            PackageCmd::CNewConnect,
            PackageProtocol::TCP,
            Some(source_addr.clone()),
            Some(target_addr.clone()),
            Some(uuid.as_bytes().to_vec()),
        );
        let _ = tunnel.write_to_tunnel(tunnel_package).await;

        let route = Arc::new(Route {
            protocol: PackageProtocol::TCP,
            source_addr,
            target_addr,
            tunnel: RwLock::new(Some(tunnel)),
            send_data_sender: channel.0,
            send_data_receiver: RwLock::new(Some(channel.1)),
            route_table,
            tcp_read_job: RwLock::new(None),
            tcp_write_job: Arc::new(RwLock::new(None)),
            udp_read_job: None,
            udp_socket: RwLock::new(None),
            active_time,
            uuid,
        });
        route.clone().tcp_join_handle(tcp_stream).await;
        route
    }

    async fn tcp_join_handle(self: Arc<Self>, tcp_stream: TcpStream) {
        let route = self.clone();
        let _ = self.tcp_read_job.write().await.insert(spawn(async move {
            route.start_tcp_transmit(tcp_stream).await;
        }));
    }
}