use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

use crate::port_forward::route_table::RouteTable;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tokio::time::sleep;

pub struct RouteContext {
    route_map: Arc<RwLock<HashMap<String, Arc<RouteTable>>>>,
    timeout_job: <PERSON><PERSON><PERSON><PERSON><PERSON><()>,
}

impl RouteContext {
    pub fn new() -> Self {
        let route_map = Arc::new(RwLock::new(HashMap::<String, Arc<RouteTable>>::new()));

        // 启动超时检查任务
        let route_table_map_arc = route_map.clone();
        let timeout_job = spawn(async move {
            loop {
                sleep(Duration::from_secs(60 * 10)).await;

                // 收集需要检查的路由表，避免在持有锁时调用异步方法
                let route_tables_to_check = {
                    let read_guard = route_table_map_arc.read().await;
                    read_guard.iter().map(|(key, route_table)| (key.clone(), route_table.clone())).collect::<Vec<_>>()
                };

                // 在锁外检查超时状态
                let mut timeout_route_tables = Vec::new();
                for (key, route_table) in route_tables_to_check {
                    if route_table.is_timeout().await {
                        timeout_route_tables.push((key, route_table));
                    }
                }

                // 移除超时的路由表并关闭连接
                if !timeout_route_tables.is_empty() {
                    let mut write_guard = route_table_map_arc.write().await;
                    for (key, route_table) in timeout_route_tables {
                        if write_guard.remove(&key).is_some() {
                            // 在锁外关闭连接，避免死锁
                            drop(write_guard);
                            route_table.remove_all().await;
                            write_guard = route_table_map_arc.write().await;
                        }
                    }
                }
            }
        });

        RouteContext {
            route_map,
            timeout_job,
        }
    }

    /// 查询并新建
    pub async fn get_or_create(&self, uuid: &String) -> Arc<RouteTable> {
        let mut write_guard = self.route_map.write().await;
        if let Some(route_table) = write_guard.get(uuid) {
            route_table.clone()
        } else {
            let route_table_arc = Arc::new(RouteTable::new());
            write_guard.insert(uuid.clone(), route_table_arc.clone());
            route_table_arc
        }
    }
}