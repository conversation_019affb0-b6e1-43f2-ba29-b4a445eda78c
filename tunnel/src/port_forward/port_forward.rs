use crate::port_forward::route_context::RouteContext;
use crate::port_forward::route_table::RouteTable;
use crate::port_forward::router::Route;
use crate::tunnel::tunnel::Tunnel;
use flyshadow_common::tunnel::tunnel_package::{PackageCmd, PackageProtocol, TunnelPackage};
use log::info;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream, UdpSocket};
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::Join<PERSON>andle;
use tokio::time::Instant;
use uuid::uuid;

pub struct PortForward {
    route_context: RouteContext,
    tunnel: RwLock<Option<Arc<Tunnel>>>,
    listener_map: RwLock<HashMap<u16, Vec<JoinHandle<()>>>>,
}

impl PortForward {
    pub fn new() -> Arc<Self> {
        Arc::new(PortForward {
            route_context: RouteContext::new(),
            tunnel: Default::default(),
            listener_map: Default::default(),
        })
    }

    /// 设置tunnel
    pub async fn set_tunnel(&self, tunnel: Arc<Tunnel>) {
        let _ = self.tunnel.write().await.insert(tunnel);
    }

    /// 添加本地监听
    pub async fn add_port_forwarding(
        self: Arc<Self>,
        port: u16,
        target_address: String,
        target_client_uuid: String,
    ) -> Result<(), String> {
        let tunnel = if let Some(tunnel) = self.tunnel.read().await.clone() {
            tunnel
        } else {
            return Err("tunnel is none".to_string());
        };
        self.remove_port_forwarding(port).await;

        let udp_socket = Arc::new(
            UdpSocket::bind(format!("0.0.0.0:{}", port))
                .await
                .map_err(|e| format!("Bind 0.0.0.0 udp socket error: {}", e))?,
        );
        let tcp_listener = TcpListener::bind(("0.0.0.0", port))
            .await
            .map_err(|e| format!("Bind 0.0.0.0 tcp socket error: {}", e))?;

        let mut join_handle = vec![];

        // UDP监听
        join_handle.push(Self::udp_handle(
            udp_socket,
            target_address.clone(),
            target_client_uuid.clone(),
            tunnel.clone(),
            self.clone(),
        ));

        // TCP监听
        join_handle.push(Self::tcp_handle(
            tcp_listener,
            target_address.clone(),
            target_client_uuid.clone(),
            tunnel.clone(),
            self.clone(),
        ));

        #[cfg(target_os = "ios")]
        {
            let tcp_listener = TcpListener::bind(("127.0.0.1", port))
                .await
                .map_err(|e| format!("Bind 127.0.0.1 tcp socket error: {}", e))?;
            // UDP监听

            // TCP监听
            join_handle.push(Self::tcp_handle(
                tcp_listener,
                target_address.clone(),
                target_client_uuid.clone(),
                tunnel.clone(),
                self.clone(),
            ));
        }

        self.listener_map.write().await.insert(port, join_handle);

        Ok(())
    }

    fn tcp_handle(
        tcp_listener: TcpListener,
        target_address: String,
        target_client_uuid: String,
        tunnel: Arc<Tunnel>,
        port_forward: Arc<PortForward>,
    ) -> JoinHandle<()> {
        spawn(async move {
            loop {
                match tcp_listener.accept().await {
                    Ok((tcp_stream, socket_addr)) => {
                        let route = Route::new_client_tcp(
                            tcp_stream,
                            target_address.clone(),
                            socket_addr.to_string(),
                            tunnel.clone(),
                            port_forward.get_route_table(&target_client_uuid).await,
                            target_client_uuid.clone(),
                        )
                            .await;
                        port_forward
                            .get_route_table(&target_client_uuid)
                            .await
                            .add_route(route)
                            .await;
                    }
                    Err(e) => {
                        log::error!("accept err {}", e);
                    }
                }
            }
        })
    }

    fn udp_handle(
        udp_socket: Arc<UdpSocket>,
        target_address: String,
        target_client_uuid: String,
        tunnel: Arc<Tunnel>,
        port_forward: Arc<PortForward>,
    ) -> JoinHandle<()> {
        spawn(async move {
            let uuid_byte = target_client_uuid.as_bytes();
            let mut buf = [0u8; 2048];
            while let Ok((n, source_addr)) = udp_socket.recv_from(&mut buf).await {
                if n == 0 {
                    break;
                }

                // 判断路由是否存在
                let source_addr = source_addr.to_string();
                if !port_forward
                    .get_route_table(&target_client_uuid)
                    .await
                    .exist_route(&source_addr, &target_address, &PackageProtocol::UDP)
                    .await
                {
                    port_forward
                        .get_route_table(&target_client_uuid)
                        .await
                        .add_route(
                            Route::new_client_udp(
                                udp_socket.clone(),
                                target_address.clone(),
                                source_addr.clone(),
                                tunnel.clone(),
                                port_forward.get_route_table(&target_client_uuid).await,
                                target_client_uuid.clone(),
                            )
                                .await,
                        )
                        .await;
                }

                let mut data = vec![];
                data.append(&mut uuid_byte.to_vec());
                data.append(&mut buf[..n].to_vec());
                let tunnel_package = TunnelPackage::new(
                    PackageCmd::CTdata,
                    PackageProtocol::UDP,
                    Some(source_addr),
                    Some(target_address.clone()),
                    Some(data),
                );
                if tunnel.write_to_tunnel(tunnel_package).await.is_err() {
                    break;
                }
            }
        })
    }

    /// 删除本地监听
    pub async fn remove_port_forwarding(&self, port: u16) {
        if let Some(vec) = self.listener_map.write().await.remove(&port) {
            for join_handle in vec {
                join_handle.abort();
            }
        }
    }

    /// 清空本地监听
    pub async fn clear_port_forwarding(&self) {
        for (_port, mut vec) in self.listener_map.write().await.drain() {
            for join_handle in vec.drain(..) {
                join_handle.abort();
            }
        }
    }

    /// 获取路由表
    async fn get_route_table(&self, uuid: &String) -> Arc<RouteTable> {
        self.route_context.get_or_create(uuid).await
    }

    /// 新建连接
    pub async fn new_connect(self: Arc<Self>, mut tunnel_package: TunnelPackage) {
        let tunnel = if let Some(tunnel) = self.tunnel.read().await.clone() {
            tunnel
        } else {
            return;
        };
        let uuid = if let Some(data) = tunnel_package.data.as_ref() {
            if data.len() < 32 {
                return;
            }
            String::from_utf8_lossy(data).to_string()
        } else {
            return;
        };

        if tunnel_package.target_address.is_none()
            || tunnel_package.target_address.as_deref() == Some("")
        {
            tunnel_package.cmd = PackageCmd::CCloseConnect;
            tunnel_package.data = Some(uuid.as_bytes().to_vec());
            let _ = tunnel.write_to_tunnel(tunnel_package).await;
            return;
        }

        let route = Route::new_server(
            tunnel_package.protocol,
            tunnel_package.target_address.clone().unwrap(),
            tunnel_package.source_address.unwrap(),
            tunnel,
            self.get_route_table(&uuid).await,
            uuid.clone(),
        )
            .await;
        if tunnel_package.protocol == PackageProtocol::TCP {
            route.clone().connect().await;
        }
        self.get_route_table(&uuid).await.add_route(route).await;
    }

    /// 关闭连接
    pub async fn close_connect(&self, tunnel_package: TunnelPackage) {
        let uuid = if let Some(data) = tunnel_package.data.as_ref() {
            if data.len() < 32 {
                return;
            }
            String::from_utf8_lossy(data).to_string()
        } else {
            return;
        };

        if let Some(source_address) = tunnel_package.source_address {
            if let Some(target_address) = tunnel_package.target_address {
                self.get_route_table(&uuid)
                    .await
                    .remove_route(&source_address, &target_address, tunnel_package.protocol)
                    .await;
            }
        }
    }

    /// 转发数据
    pub async fn transmit_data(self: Arc<Self>, mut tunnel_package: TunnelPackage) {
        let tunnel = if let Some(tunnel) = self.tunnel.read().await.clone() {
            tunnel
        } else {
            return;
        };
        let uuid = if let Some(data) = tunnel_package.data.as_mut() {
            if data.len() < 32 {
                return;
            }
            String::from_utf8_lossy(&data[..32]).to_string()
        } else {
            return;
        };

        if let Some(data) = tunnel_package.data {
            if let Some(source_address) = tunnel_package.source_address.clone() {
                if let Some(target_address) = tunnel_package.target_address.clone() {
                    // 路由为空
                    if !self
                        .get_route_table(&uuid)
                        .await
                        .send_data_to_target(
                            source_address.clone(),
                            target_address.clone(),
                            tunnel_package.protocol,
                            data.clone(),
                        )
                        .await
                    {
                        // 判断是否UDP
                        if tunnel_package.protocol == PackageProtocol::UDP {
                            let route = Route::new_server(
                                tunnel_package.protocol,
                                target_address.clone(),
                                source_address,
                                tunnel.clone(),
                                self.get_route_table(&uuid).await,
                                uuid.clone(),
                            )
                                .await;
                            route.send_udp_data_to_target(target_address, data).await;
                            self.get_route_table(&uuid).await.add_route(route).await;
                        } else {
                            // 如果是TCP并且没路由的话发送断开命令
                            tunnel_package.cmd = PackageCmd::CCloseConnect;
                            tunnel_package.data = Some(uuid.as_bytes().to_vec());
                            let _ = tunnel.write_to_tunnel(tunnel_package).await;
                        }
                    }
                }
            }
        }
    }
}
