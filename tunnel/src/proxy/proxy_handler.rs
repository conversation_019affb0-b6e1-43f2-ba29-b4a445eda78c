use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};

use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::tcp::{OwnedReadHalf, OwnedWriteHalf};
use tokio::net::TcpStream;
use tokio::spawn;

use flyshadow_common::tunnel::tunnel_package::PackageProtocol;

use crate::context::context::TunnelContext;
use crate::context::proxy_type::ProxyType;
use crate::mapper::tunnel_mapper_info::TunnelMapperInfo;
use crate::util::process_util::get_process_name_by_port;
use crate::util::socks5_util::resolve_socks5_addr;
use flyshadow_common::util::uri_util::{remove_proxy_characteristic, resolve_uri, HttpMethod};

// 常量定义
/// 数据缓冲区大小
const BUFFER_SIZE: usize = 16384;
/// SOCKS5协议版本号
const SOCKS5_VERSION: u8 = 0x05;
/// SOCKS5无认证方法
const SOCKS5_NO_AUTH: u8 = 0x00;
/// SOCKS5认证失败响应
const SOCKS5_AUTH_FAILED: u8 = 0xff;
/// SOCKS5 TCP连接命令
const SOCKS5_CMD_CONNECT: u8 = 0x01;
/// SOCKS5 UDP关联命令
const SOCKS5_CMD_UDP_ASSOCIATE: u8 = 0x03;
/// HTTP CONNECT响应
const HTTP_CONNECT_RESPONSE: &[u8] = b"HTTP/1.1 200 Connection Established\r\n\r\n";
/// SOCKS5 TCP连接成功响应
const SOCKS5_TCP_SUCCESS_RESPONSE: &[u8] = &[0x05, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00];
/// 最小数据包长度
const MIN_PACKET_LENGTH: usize = 3;

/// 连接状态枚举
///
/// 表示当前代理连接的协议状态
#[derive(PartialEq, Debug)]
enum ConnectStatus {
    /// 初始状态
    Init,
    /// HTTP代理模式
    Http,
    /// SOCKS5 TCP代理模式
    Socks5,
    /// SOCKS5 UDP关联模式
    Socks5Udp,
}

/// 处理代理客户端连接
///
/// # 参数
/// * `context` - 隧道上下文，包含代理配置和连接管理
/// * `tcp_stream` - 客户端TCP连接流
/// * `socket_addr` - 客户端socket地址
///
/// # 说明
/// 该函数是代理服务器的核心处理逻辑，支持HTTP和SOCKS5两种代理协议。
/// 会根据客户端发送的第一个数据包来判断协议类型，然后进行相应的处理。
pub async fn proxy_client_handle(context: Arc<TunnelContext>, tcp_stream: TcpStream, socket_addr: SocketAddr) {
    let (mut client_reader, mut client_writer) = tcp_stream.into_split();
    let source_addr = socket_addr.to_string();

    let mut status = ConnectStatus::Init;
    let mut http_method = HttpMethod::Unknown;
    let mut buffer = [0u8; BUFFER_SIZE];

    log::debug!("开始处理来自 {} 的代理连接", source_addr);

    // 读取客户端的第一个数据包以判断协议类型
    match client_reader.read(&mut buffer).await {
        Ok(0) => {
            log::debug!("客户端 {} 连接已关闭", source_addr);
            return;
        }
        Ok(n) => {
            let mut data = &buffer[..n];

            // 检查数据包长度是否足够
            if n < MIN_PACKET_LENGTH {
                log::error!("来自 {} 的数据包长度不足: {}", source_addr, n);
                return;
            }

            // 检测协议类型
            if data[0] == SOCKS5_VERSION {
                // SOCKS5协议检测
                status = match handle_socks5_handshake(&mut client_reader, &mut client_writer, data).await {
                    Ok(socks5_status) => socks5_status,
                    Err(e) => {
                        log::error!("SOCKS5握手失败，来自 {}: {}", source_addr, e);
                        return;
                    }
                };

                // 如果是SOCKS5，需要读取下一个数据包（连接请求）
                if status == ConnectStatus::Socks5 {
                    match client_reader.read(&mut buffer).await {
                        Ok(0) => {
                            log::debug!("SOCKS5客户端 {} 连接已关闭", source_addr);
                            return;
                        }
                        Ok(n) => {
                            data = &buffer[..n];
                        }
                        Err(e) => {
                            log::error!("读取SOCKS5连接请求失败，来自 {}: {}", source_addr, e);
                            return;
                        }
                    }
                }
            } else {
                // HTTP代理协议
                status = ConnectStatus::Http;
                log::debug!("检测到HTTP代理连接，来自 {}", source_addr);
            }
            // 根据协议类型处理连接请求
            if status == ConnectStatus::Socks5 || status == ConnectStatus::Http {
                if status == ConnectStatus::Socks5 {
                    // 处理SOCKS5连接请求
                    match handle_socks5_connect_request(&context, client_writer, data, socket_addr).await {
                        Ok(connect_status) => {
                            status = connect_status;
                        }
                        Err(e) => {
                            log::error!("处理SOCKS5连接请求失败，来自 {}: {}", source_addr, e);
                            return;
                        }
                    }
                }
                // 处理HTTP代理请求
                else {
                    match handle_http_request(&context, client_writer, data, socket_addr, &source_addr).await {
                        Ok(_) => {}
                        Err(e) => {
                            log::error!("处理HTTP请求失败，来自 {}: {}", source_addr, e);
                            return;
                        }
                    }
                }
            }
        }
        Err(e) => {
            log::error!("读取客户端数据失败，来自 {}: {}", source_addr, e);
            return;
        }
    }

    // 根据连接状态处理后续数据传输
    if status == ConnectStatus::Socks5Udp {
        // SOCKS5 UDP关联模式：保持连接直到客户端断开
        handle_socks5_udp_association(&mut client_reader, &mut buffer, &source_addr).await;
    } else {
        // TCP数据传输模式
        handle_tcp_data_transfer(&context, &mut client_reader, &mut buffer, &source_addr, http_method).await;
    }

    /// 处理SOCKS5握手过程
    ///
    /// # 参数
    /// * `client_reader` - 客户端读取器
    /// * `client_writer` - 客户端写入器
    /// * `data` - 握手数据
    ///
    /// # 返回值
    /// * `Ok(ConnectStatus)` - 握手成功，返回连接状态
    /// * `Err(String)` - 握手失败，返回错误信息
    async fn handle_socks5_handshake(
        _client_reader: &mut OwnedReadHalf,
        client_writer: &mut OwnedWriteHalf,
        data: &[u8],
    ) -> Result<ConnectStatus, String> {
        // 检查是否支持无认证方法
        if data.len() >= 3 && data[2] == SOCKS5_NO_AUTH {
            // 发送认证成功响应
            if let Err(e) = client_writer.write_all(&[SOCKS5_VERSION, SOCKS5_NO_AUTH]).await {
                return Err(format!("发送SOCKS5认证响应失败: {}", e));
            }
            log::debug!("SOCKS5握手成功，无需认证");
            Ok(ConnectStatus::Socks5)
        } else {
            // 发送认证失败响应
            let _ = client_writer.write_all(&[SOCKS5_VERSION, SOCKS5_AUTH_FAILED]).await;
            Err("SOCKS5需要认证，但不支持".to_string())
        }
    }

    /// 处理SOCKS5连接请求
    ///
    /// # 参数
    /// * `context` - 隧道上下文
    /// * `client_writer` - 客户端写入器
    /// * `data` - 连接请求数据
    /// * `socket_addr` - 客户端地址
    ///
    /// # 返回值
    /// * `Ok(ConnectStatus)` - 处理成功
    /// * `Err(String)` - 处理失败
    async fn handle_socks5_connect_request(
        context: &Arc<TunnelContext>,
        client_writer: OwnedWriteHalf,
        data: &[u8],
        socket_addr: SocketAddr,
    ) -> Result<ConnectStatus, String> {
        let (host, port) = resolve_socks5_addr(data)
            .map_err(|e| format!("解析SOCKS5地址失败: {}", e))?;

        let command = data[1];

        match command {
            SOCKS5_CMD_CONNECT => {
                // TCP连接请求
                log::debug!("SOCKS5 TCP连接请求: {}:{}", host, port);

                // 创建隧道映射信息
                let tunnel_mapper_info = create_tunnel_mapper_info(
                    socket_addr, &host, port, PackageProtocol::TCP,
                );

                // 发送TCP连接成功响应
                let mut writer = client_writer;
                if let Err(e) = writer.write_all(SOCKS5_TCP_SUCCESS_RESPONSE).await {
                    return Err(format!("发送SOCKS5 TCP响应失败: {}", e));
                }

                // 连接目标服务器
                context.proxy_connect_server(tunnel_mapper_info, writer).await
                    .map_err(|e| format!("连接目标服务器失败: {}", e))?;

                Ok(ConnectStatus::Socks5)
            }
            SOCKS5_CMD_UDP_ASSOCIATE => {
                // UDP关联请求
                log::debug!("SOCKS5 UDP关联请求");

                let udp_port = context.get_socks5_udp_addr()
                    .ok_or("UDP代理未启用")?
                    .port();

                // 发送UDP关联响应
                let udp_response = [
                    SOCKS5_VERSION, 0x00, 0x00, 0x01, // 成功响应，IPv4
                    127, 0, 0, 1, // 127.0.0.1
                    (udp_port >> 8) as u8, udp_port as u8 // 端口号
                ];

                let mut writer = client_writer;
                if let Err(e) = writer.write_all(&udp_response).await {
                    return Err(format!("发送SOCKS5 UDP响应失败: {}", e));
                }

                Ok(ConnectStatus::Socks5Udp)
            }
            _ => {
                Err(format!("不支持的SOCKS5命令: {}", command))
            }
        }
    }

    /// 处理HTTP代理请求
    ///
    /// # 参数
    /// * `context` - 隧道上下文
    /// * `client_writer` - 客户端写入器
    /// * `data` - HTTP请求数据
    /// * `socket_addr` - 客户端地址
    /// * `source_addr` - 源地址字符串
    ///
    /// # 返回值
    /// * `Ok(HttpMethod)` - 处理成功，返回HTTP方法
    /// * `Err(String)` - 处理失败
    async fn handle_http_request(
        context: &Arc<TunnelContext>,
        client_writer: OwnedWriteHalf,
        data: &[u8],
        socket_addr: SocketAddr,
        source_addr: &str,
    ) -> Result<(), String> {
        let (host, port, method) = resolve_uri(data);
        log::debug!("HTTP代理请求: {} {}:{}",
        match method {
            HttpMethod::Connect => "CONNECT",
            HttpMethod::Http => "HTTP",
            _ => "UNKNOWN"
        }, host, port);

        // 创建隧道映射信息
        let tunnel_mapper_info = create_tunnel_mapper_info(
            socket_addr, &host, port, PackageProtocol::TCP,
        );

        match method {
            HttpMethod::Connect => {
                // HTTP CONNECT方法
                let mut writer = client_writer;
                if let Err(e) = writer.write_all(HTTP_CONNECT_RESPONSE).await {
                    return Err(format!("发送HTTP CONNECT响应失败: {}", e));
                }

                // 连接目标服务器
                context.proxy_connect_server(tunnel_mapper_info, writer).await
                    .map_err(|e| format!("连接目标服务器 [{}:{}] 失败: {}", host, port, e))?;

                Ok(())
            }
            HttpMethod::Http => {
                // 普通HTTP请求
                context.proxy_connect_server(tunnel_mapper_info, client_writer).await
                    .map_err(|e| format!("连接目标服务器 [{}:{}] 失败: {}", host, port, e))?;

                // 处理HTTP请求数据，移除代理特征
                let mut write_data = vec![];
                let data_to_send = if remove_proxy_characteristic(data, &mut write_data) {
                    write_data.as_slice()
                } else {
                    data
                };

                // 发送处理后的数据到目标服务器
                let source_addr_string = source_addr.to_string();
                if !context.proxy_write_to_server(&source_addr_string, data_to_send).await {
                    return Err("发送数据到目标服务器失败".to_string());
                }

                Ok(())
            }
            _ => {
                Err(format!("不支持的HTTP方法: {}", String::from_utf8_lossy(data)))
            }
        }
    }

    /// 创建隧道映射信息
    ///
    /// # 参数
    /// * `socket_addr` - 客户端地址
    /// * `target_host` - 目标主机
    /// * `target_port` - 目标端口
    /// * `protocol` - 协议类型
    ///
    /// # 返回值
    /// 返回配置好的TunnelMapperInfo实例
    fn create_tunnel_mapper_info(
        socket_addr: SocketAddr,
        target_host: &str,
        target_port: u16,
        protocol: PackageProtocol,
    ) -> TunnelMapperInfo {
        TunnelMapperInfo {
            protocol,
            source_addr: socket_addr.ip().to_string(),
            source_port: socket_addr.port(),
            target_addr: target_host.to_string(),
            target_port,
            fake_target_addr: String::new(),
            fake_target_port: 0,
            process_name: get_process_name_by_port(socket_addr.port(), protocol),
            matcher_name: String::new(),
            matcher_rule: String::new(),
            proxy_type: ProxyType::Proxy,
            active_time: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_millis(),
            temp_data: Vec::new(),
            direct_conn_priority: true,
            traffic_info: Default::default(),
        }
    }

    /// 处理SOCKS5 UDP关联
    ///
    /// # 参数
    /// * `client_reader` - 客户端读取器
    /// * `buffer` - 数据缓冲区
    /// * `source_addr` - 源地址
    ///
    /// # 说明
    /// 在UDP关联模式下，需要保持TCP连接直到客户端断开
    async fn handle_socks5_udp_association(
        client_reader: &mut OwnedReadHalf,
        buffer: &mut [u8],
        source_addr: &str,
    ) {
        log::debug!("开始处理SOCKS5 UDP关联，来自 {}", source_addr);

        // 保持连接直到客户端断开
        loop {
            match client_reader.read(buffer).await {
                Ok(0) => {
                    log::debug!("SOCKS5 UDP关联连接已断开，来自 {}", source_addr);
                    break;
                }
                Ok(_n) => {
                    // UDP关联模式下，TCP连接只用于保持状态，不处理数据
                }
                Err(e) => {
                    log::debug!("SOCKS5 UDP关联连接错误，来自 {}: {}", source_addr, e);
                    break;
                }
            }
        }
    }

    /// 处理TCP数据传输
    ///
    /// # 参数
    /// * `context` - 隧道上下文
    /// * `client_reader` - 客户端读取器
    /// * `buffer` - 数据缓冲区
    /// * `source_addr` - 源地址
    /// * `http_method` - HTTP方法类型
    ///
    /// # 说明
    /// 持续读取客户端数据并转发到目标服务器
    async fn handle_tcp_data_transfer(
        context: &Arc<TunnelContext>,
        client_reader: &mut OwnedReadHalf,
        buffer: &mut [u8],
        source_addr: &str,
        http_method: HttpMethod,
    ) {
        log::debug!("开始TCP数据传输，来自 {}", source_addr);

        loop {
            match client_reader.read(buffer).await {
                Ok(0) => {
                    log::debug!("客户端连接已关闭，来自 {}", source_addr);
                    break;
                }
                Ok(n) => {
                    let data = &buffer[..n];

                    // 根据HTTP方法类型处理数据
                    let source_addr_string = source_addr.to_string();
                    let success = if http_method == HttpMethod::Http {
                        // HTTP请求需要移除代理特征
                        let mut write_data = Vec::new();
                        let data_to_send = if remove_proxy_characteristic(data, &mut write_data) {
                            write_data.as_slice()
                        } else {
                            data
                        };
                        context.proxy_write_to_server(&source_addr_string, data_to_send).await
                    } else {
                        // 其他情况直接转发数据
                        context.proxy_write_to_server(&source_addr_string, data).await
                    };

                    if !success {
                        log::debug!("向目标服务器写入数据失败，来自 {}", source_addr);
                        break;
                    }
                }
                Err(e) => {
                    log::debug!("读取客户端数据失败，来自 {}: {}", source_addr, e);
                    break;
                }
            }
        }

        // 关闭与目标服务器的连接
        let source_addr_string = source_addr.to_string();
        context.close_server_connect(&source_addr_string).await;
        log::debug!("TCP数据传输结束，来自 {}", source_addr);
    }
}