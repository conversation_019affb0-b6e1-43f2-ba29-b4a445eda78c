use std::io::Error;
use std::process::Output;
use std::sync::Arc;

use tokio::net::TcpListener;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::Join<PERSON><PERSON>le;

use crate::context::context::TunnelContext;
use crate::proxy::proxy_handler::proxy_client_handle;

/// 代理服务器结构体
///
/// 负责管理HTTP和SOCKS5代理服务器的生命周期，包括启动、停止和连接处理
pub struct Proxy {
    /// 当前监听的端口号，使用RwLock保证线程安全
    port: RwLock<u16>,
    /// 隧道上下文，包含代理配置和连接管理
    context: Arc<TunnelContext>,
    /// TCP监听器的任务句柄列表，用于管理异步任务
    tcp_listener_join_handler: RwLock<Vec<JoinHandle<Output>>>,
}


impl Proxy {
    /// 创建新的代理实例
    ///
    /// # 参数
    /// * `context` - 隧道上下文的Arc引用
    ///
    /// # 返回值
    /// 返回新创建的Proxy实例
    pub fn new(context: Arc<TunnelContext>) -> Proxy {
        Proxy {
            port: RwLock::new(0),
            context,
            tcp_listener_join_handler: RwLock::new(Vec::new()),
        }
    }

    /// 启动代理服务器（监听所有网络接口）
    ///
    /// # 参数
    /// * `port` - 要监听的端口号
    ///
    /// # 返回值
    /// * `Ok(())` - 启动成功
    /// * `Err(Error)` - 启动失败，包含错误信息
    ///
    /// # 说明
    /// 如果端口号与当前监听的端口不同，会先停止当前服务再启动新服务
    pub async fn start(&self, port: u16) -> Result<(), Error> {
        let current_port = *self.port.read().await;

        // 如果端口号不同，需要重新启动服务
        if current_port != port {
            self.stop().await;
            *self.port.write().await = port;
            self.start_local_all(port).await
        } else {
            // 端口相同，服务已在运行
            log::info!("代理服务已在端口 {} 上运行", port);
            Ok(())
        }
    }

    /// 在所有网络接口上启动代理服务
    ///
    /// # 参数
    /// * `port` - 监听端口号
    ///
    /// # 返回值
    /// * `Ok(())` - 启动成功
    /// * `Err(Error)` - 绑定端口失败
    async fn start_local_all(&self, port: u16) -> Result<(), Error> {
        let context = self.context.clone();
        log::info!("代理服务启动，监听地址: 0.0.0.0:{}", port);

        match TcpListener::bind(("0.0.0.0", port)).await {
            Ok(listener) => {
                self.start_accept_client(listener, context).await;
                log::info!("代理服务成功启动在端口 {}", port);
                Ok(())
            }
            Err(e) => {
                log::error!("绑定端口 {} 失败: {}", port, e);
                Err(e)
            }
        }
    }

    /// 启动代理服务器（监听指定IP地址）
    ///
    /// # 参数
    /// * `ip` - 要绑定的IP地址
    /// * `port` - 要监听的端口号
    ///
    /// # 返回值
    /// * `Ok(())` - 启动成功
    /// * `Err(Error)` - 启动失败，包含错误信息
    ///
    /// # 说明
    /// 该方法会在指定的IP地址和端口上启动代理服务
    /// 注意：此方法不应该同时调用 start_local_all，这会导致端口冲突
    pub async fn start_ip(&self, ip: String, port: u16) -> Result<(), Error> {
        let current_port = *self.port.read().await;

        // 如果端口号不同，需要重新启动服务
        if current_port != port {
            self.stop().await;
            *self.port.write().await = port;
        } else {
            log::info!("代理服务已在 {}:{} 上运行", ip, port);
            return Ok(());
        }

        let context = self.context.clone();
        log::info!("代理服务启动，监听地址: {}:{}", ip, port);

        match TcpListener::bind((ip.as_str(), port)).await {
            Ok(listener) => {
                self.start_accept_client(listener, context).await;
                log::info!("代理服务成功启动在 {}:{}", ip, port);
                Ok(())
            }
            Err(e) => {
                log::error!("绑定地址 {}:{} 失败: {}", ip, port, e);
                Err(e)
            }
        }
    }

    /// 开始接收客户端连接
    ///
    /// # 参数
    /// * `tcp_listener` - TCP监听器
    /// * `context` - 隧道上下文
    ///
    /// # 说明
    /// 该方法会启动一个异步任务来持续接收客户端连接，
    /// 每个新连接都会在独立的异步任务中处理
    async fn start_accept_client(&self, tcp_listener: TcpListener, context: Arc<TunnelContext>) {
        let context_clone = context.clone();

        let accept_task = spawn(async move {
            log::info!("开始接收客户端连接");

            loop {
                let context = context_clone.clone();

                match tcp_listener.accept().await {
                    Ok((tcp_stream, socket_addr)) => {
                        log::debug!("接收到来自 {} 的新连接", socket_addr);
                        // 为每个客户端连接启动独立的处理任务
                        spawn(proxy_client_handle(context, tcp_stream, socket_addr));
                    }
                    Err(e) => {
                        log::error!("接收客户端连接失败: {}", e);
                        // 可以考虑在这里添加重试逻辑或者退出循环
                    }
                }
            }
        });

        // 将任务句柄保存到列表中，用于后续管理
        self.tcp_listener_join_handler.write().await.push(accept_task);
    }

    /// 停止代理服务
    ///
    /// # 说明
    /// 该方法会中止所有正在运行的监听任务，
    /// 有效地停止代理服务器的运行
    pub async fn stop(&self) {
        let mut handlers = self.tcp_listener_join_handler.write().await;
        let handler_count = handlers.len();

        if handler_count > 0 {
            log::info!("正在停止 {} 个监听任务", handler_count);

            // 中止所有监听任务
            for job in handlers.drain(..) {
                job.abort();
            }

            // 重置端口号
            *self.port.write().await = 0;
            log::info!("代理服务已停止");
        } else {
            log::info!("代理服务未在运行");
        }
    }
}
