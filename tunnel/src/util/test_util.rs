//! # 工具模块测试
//!
//! 用于测试util模块中各个工具函数的功能

#[cfg(test)]
mod tests {
    use crate::util::node_encrypt_util::node_url_encrypt;
    use crate::util::sni_util::SniUtil;
    use crate::util::socks5_util::resolve_socks5_addr;
    use flyshadow_common::tunnel::tunnel_package::PackageProtocol;

    #[cfg(target_os = "windows")]
    use crate::util::process_util::get_process_name_by_port;

    /// 测试节点URL加密功能
    #[test]
    fn test_node_encrypt() {
        println!("=== 测试节点URL加密功能 ===");

        let test_urls = vec![
            "***********:8080",
            "example.com:443",
            "127.0.0.1:3000",
            "multiple,nodes,test:8080",
        ];

        for url in test_urls {
            let encrypted = node_url_encrypt(url);
            println!("URL: '{}' -> 加密结果: {}", url, encrypted);

            // 验证相同输入产生相同输出
            assert_eq!(encrypted, node_url_encrypt(url));
        }
    }

    /// 测试SOCKS5地址解析功能
    #[test]
    fn test_socks5_parsing() {
        println!("=== 测试SOCKS5地址解析功能 ===");

        // 测试IPv4地址解析
        let ipv4_data = vec![
            0x05, 0x01, 0x00, 0x01,  // VER, CMD, RSV, ATYP(IPv4)
            192, 168, 1, 1,          // IPv4地址: ***********
            0x00, 0x50               // 端口: 80
        ];

        match resolve_socks5_addr(&ipv4_data) {
            Ok((host, port)) => {
                println!("IPv4解析成功: {}:{}", host, port);
                assert_eq!(host, "***********");
                assert_eq!(port, 80);
            }
            Err(e) => panic!("IPv4解析失败: {}", e),
        }

        // 测试域名地址解析
        let domain = "test.com";
        let mut domain_data = vec![
            0x05, 0x01, 0x00, 0x03,  // VER, CMD, RSV, ATYP(Domain)
            domain.len() as u8       // 域名长度
        ];
        domain_data.extend_from_slice(domain.as_bytes()); // 域名
        domain_data.extend_from_slice(&[0x01, 0xBB]);     // 端口: 443

        match resolve_socks5_addr(&domain_data) {
            Ok((host, port)) => {
                println!("域名解析成功: {}:{}", host, port);
                assert_eq!(host, "test.com");
                assert_eq!(port, 443);
            }
            Err(e) => panic!("域名解析失败: {}", e),
        }
    }

    /// 测试SNI解析功能（边界情况）
    #[test]
    fn test_sni_parsing() {
        println!("=== 测试SNI解析功能 ===");

        // 测试空数据
        let empty_data = vec![];
        assert_eq!(SniUtil::resolve_sni(&empty_data), None);
        println!("空数据测试通过");

        // 测试长度不足的数据
        let short_data = vec![0u8; 5];
        assert_eq!(SniUtil::resolve_sni(&short_data), None);
        println!("短数据测试通过");

        // 测试最小长度数据
        let min_data = vec![0u8; 10];
        assert_eq!(SniUtil::resolve_sni(&min_data), None);
        println!("最小长度数据测试通过");
    }

    /// 测试进程查询功能（仅Windows）
    #[cfg(target_os = "windows")]
    #[test]
    fn test_process_query() {
        println!("=== 测试进程查询功能 ===");

        // 测试常见端口
        let test_cases = vec![
            (80, PackageProtocol::TCP, "HTTP"),
            (443, PackageProtocol::TCP, "HTTPS"),
            (53, PackageProtocol::UDP, "DNS"),
        ];

        for (port, protocol, description) in test_cases {
            let process_name = get_process_name_by_port(port, protocol);
            if process_name.is_empty() {
                println!("{} 端口 {} 未被占用", description, port);
            } else {
                println!("{} 端口 {} 被进程 '{}' 占用", description, port, process_name);
            }
        }
    }

    /// 综合测试
    #[test]
    fn test_integration() {
        println!("=== 综合测试 ===");

        // 测试多个URL的加密
        let urls = vec![
            "server1.example.com:8080",
            "server2.example.com:8080",
            "***********00:3000",
        ];

        let mut encrypted_results = Vec::new();
        for url in &urls {
            let encrypted = node_url_encrypt(url);
            encrypted_results.push(encrypted);
            println!("URL: {} -> ID: {}", url, encrypted);
        }

        // 验证不同URL产生不同的加密结果
        for i in 0..encrypted_results.len() {
            for j in i + 1..encrypted_results.len() {
                if encrypted_results[i] == encrypted_results[j] {
                    println!("警告: URL '{}' 和 '{}' 产生了相同的加密结果",
                             urls[i], urls[j]);
                }
            }
        }

        println!("综合测试完成");
    }
}
