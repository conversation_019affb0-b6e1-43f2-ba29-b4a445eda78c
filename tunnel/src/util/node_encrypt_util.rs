//! # 节点URL加密工具
//!
//! 提供节点URL的加密和哈希功能，用于生成节点的唯一标识符

use base64::{engine::general_purpose, Engine};
use crc32fast::Hasher;

/// 对节点URL进行加密并生成16位哈希值
///
/// # 功能说明
/// 1. 将输入的URL字符串进行Base64编码
/// 2. 对编码后的字符串计算CRC32校验和
/// 3. 将校验和转换为16位无符号整数作为节点标识
///
/// # 参数
/// * `url` - 需要加密的节点URL字符串
///
/// # 返回值
/// 返回一个16位无符号整数，作为节点的唯一标识符
///
/// # 示例
/// ```rust
/// let node_id = node_url_encrypt(&"192.168.1.1:8080".to_string());
/// println!("节点ID: {}", node_id);
/// ```
pub fn node_url_encrypt(url: &str) -> u16 {
    // 对URL进行Base64编码，增加数据的随机性
    let encoded = general_purpose::STANDARD.encode(url.as_bytes());

    // 使用CRC32算法计算哈希值
    let mut hasher = Hasher::new();
    hasher.update(encoded.as_bytes());
    let checksum = hasher.finalize();

    // 将32位校验和转换为16位，并确保结果为正数
    let checksum_as_i64 = checksum as i64;
    (checksum_as_i64 as i16).unsigned_abs()
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试节点URL加密功能
    #[test]
    fn test_node_url_encrypt() {
        // 测试单个节点URL
        let single_url = "192.168.1.1:8080";
        let result1 = node_url_encrypt(single_url);
        println!("单个节点URL '{}' 的加密结果: {}", single_url, result1);

        // 测试多个节点URL组合
        let multi_urls = "47.243.19.222:6205,47.243.93.95:6205,8.218.96.2:6205,8.218.45.113:6205,8.210.77.141:6205,47.242.150.30:6205,8.210.120.140:6205,47.242.6.116:6205,47.243.41.53:6205,149.129.99.213:6205,149.129.81.183:6205,47.242.21.10:6205";
        let result2 = node_url_encrypt(multi_urls);
        println!("多节点URL组合的加密结果: {}", result2);

        // 验证相同输入产生相同输出
        assert_eq!(node_url_encrypt(single_url), node_url_encrypt(single_url));

        // 验证不同输入产生不同输出（大概率）
        let different_url = "192.168.1.2:8080";
        assert_ne!(node_url_encrypt(single_url), node_url_encrypt(different_url));
    }

    /// 测试空字符串和边界情况
    #[test]
    fn test_edge_cases() {
        // 测试空字符串
        let empty_result = node_url_encrypt("");
        println!("空字符串的加密结果: {}", empty_result);

        // 测试很长的字符串
        let long_url = "a".repeat(1000);
        let long_result = node_url_encrypt(&long_url);
        println!("长字符串的加密结果: {}", long_result);

        // 验证结果都在u16范围内
        assert!(empty_result <= u16::MAX);
        assert!(long_result <= u16::MAX);
    }
}
