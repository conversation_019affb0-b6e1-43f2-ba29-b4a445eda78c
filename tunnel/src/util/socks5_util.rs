//! # SOCKS5协议工具
//!
//! 提供SOCKS5协议相关的解析和处理功能，主要用于：
//! - 解析SOCKS5连接请求中的目标地址和端口
//! - 支持IPv4、IPv6和域名三种地址类型
//! - 为SOCKS5代理服务器提供基础工具函数

use std::net::{Ipv4Addr, Ipv6Addr};

/// SOCKS5地址类型常量
const ADDR_TYPE_IPV4: u8 = 0x01;
const ADDR_TYPE_DOMAIN: u8 = 0x03;
const ADDR_TYPE_IPV6: u8 = 0x04;

/// 解析SOCKS5协议中的目标主机地址和端口
///
/// # 功能说明
/// 根据SOCKS5协议规范，解析连接请求数据包中的目标地址信息。
/// 支持三种地址类型：
/// - IPv4地址 (0x01): 4字节IP地址
/// - 域名 (0x03): 长度前缀 + 域名字符串
/// - IPv6地址 (0x04): 16字节IP地址
///
/// # 参数
/// * `data` - SOCKS5连接请求的数据包
///
/// # 返回值
/// 成功时返回Ok((主机地址, 端口号))，失败时返回Err(错误信息)
///
/// # 数据包格式
/// ```text
/// +----+-----+-------+------+----------+----------+
/// |VER | CMD |  RSV  | ATYP | DST.ADDR | DST.PORT |
/// +----+-----+-------+------+----------+----------+
/// | 1  |  1  | X'00' |  1   | Variable |    2     |
/// +----+-----+-------+------+----------+----------+
/// ```
///
/// # 示例
/// ```rust
/// let socks5_data = &[0x05, 0x01, 0x00, 0x01, 192, 168, 1, 1, 0x00, 0x50];
/// match resolve_socks5_addr(socks5_data) {
///     Ok((host, port)) => println!("目标: {}:{}", host, port),
///     Err(e) => println!("解析失败: {}", e),
/// }
/// ```
pub fn resolve_socks5_addr(data: &[u8]) -> Result<(String, u16), String> {
    // 检查数据包最小长度
    if data.len() < 6 {
        return Err("数据包长度不足，无法解析SOCKS5地址".to_string());
    }

    let data_len = data.len();

    // 根据地址类型解析主机地址
    let host = match data[3] {
        ADDR_TYPE_IPV4 => {
            // IPv4地址：4个字节
            if data.len() < 10 {
                return Err("IPv4地址数据不完整".to_string());
            }
            Ipv4Addr::new(data[4], data[5], data[6], data[7]).to_string()
        }

        ADDR_TYPE_DOMAIN => {
            // 域名：长度字节 + 域名字符串
            if data.len() < 5 {
                return Err("域名长度字段缺失".to_string());
            }

            let domain_len = data[4] as usize;
            if data.len() < 5 + domain_len + 2 {
                return Err("域名数据不完整".to_string());
            }

            // 提取域名字节并转换为字符串
            let domain_bytes = &data[5..5 + domain_len];
            String::from_utf8_lossy(domain_bytes).to_string()
        }

        ADDR_TYPE_IPV6 => {
            // IPv6地址：16个字节
            if data.len() < 22 {
                return Err("IPv6地址数据不完整".to_string());
            }

            let mut ipv6_bytes = [0u8; 16];
            ipv6_bytes.copy_from_slice(&data[4..20]);
            Ipv6Addr::from(ipv6_bytes).to_string()
        }

        unknown_type => {
            return Err(format!("不支持的地址类型: 0x{:02X}", unknown_type));
        }
    };

    // 解析端口号（网络字节序，大端序）
    let port = ((data[data_len - 2] as u16) << 8) | (data[data_len - 1] as u16);

    Ok((host, port))
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试IPv4地址解析
    #[test]
    fn test_ipv4_address_parsing() {
        // 构造IPv4 SOCKS5请求: ***********:80
        let ipv4_data = vec![
            0x05, 0x01, 0x00, 0x01,  // VER, CMD, RSV, ATYP(IPv4)
            192, 168, 1, 1,          // IPv4地址: ***********
            0x00, 0x50               // 端口: 80
        ];

        match resolve_socks5_addr(&ipv4_data) {
            Ok((host, port)) => {
                assert_eq!(host, "***********");
                assert_eq!(port, 80);
                println!("IPv4解析成功: {}:{}", host, port);
            }
            Err(e) => panic!("IPv4解析失败: {}", e),
        }
    }

    /// 测试域名地址解析
    #[test]
    fn test_domain_address_parsing() {
        // 构造域名 SOCKS5请求: example.com:443
        let domain = "example.com";
        let mut domain_data = vec![
            0x05, 0x01, 0x00, 0x03,  // VER, CMD, RSV, ATYP(Domain)
            domain.len() as u8       // 域名长度
        ];
        domain_data.extend_from_slice(domain.as_bytes()); // 域名
        domain_data.extend_from_slice(&[0x01, 0xBB]);     // 端口: 443

        match resolve_socks5_addr(&domain_data) {
            Ok((host, port)) => {
                assert_eq!(host, "example.com");
                assert_eq!(port, 443);
                println!("域名解析成功: {}:{}", host, port);
            }
            Err(e) => panic!("域名解析失败: {}", e),
        }
    }

    /// 测试IPv6地址解析
    #[test]
    fn test_ipv6_address_parsing() {
        // 构造IPv6 SOCKS5请求: ::1:8080
        let ipv6_data = vec![
            0x05, 0x01, 0x00, 0x04,  // VER, CMD, RSV, ATYP(IPv6)
            // IPv6地址: ::1 (0000:0000:0000:0000:0000:0000:0000:0001)
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
            0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
            0x1F, 0x90               // 端口: 8080
        ];

        match resolve_socks5_addr(&ipv6_data) {
            Ok((host, port)) => {
                assert_eq!(host, "::1");
                assert_eq!(port, 8080);
                println!("IPv6解析成功: {}:{}", host, port);
            }
            Err(e) => panic!("IPv6解析失败: {}", e),
        }
    }

    /// 测试错误情况
    #[test]
    fn test_error_cases() {
        // 测试数据包过短
        let short_data = vec![0x05, 0x01];
        assert!(resolve_socks5_addr(&short_data).is_err());

        // 测试不支持的地址类型
        let invalid_type_data = vec![
            0x05, 0x01, 0x00, 0xFF,  // 无效的地址类型
            0x00, 0x00, 0x00, 0x00, 0x00, 0x50
        ];
        assert!(resolve_socks5_addr(&invalid_type_data).is_err());

        // 测试IPv4数据不完整
        let incomplete_ipv4 = vec![0x05, 0x01, 0x00, 0x01, 192, 168];
        assert!(resolve_socks5_addr(&incomplete_ipv4).is_err());

        println!("错误情况测试完成");
    }

    /// 测试边界情况
    #[test]
    fn test_edge_cases() {
        // 测试最长域名（255字节）
        let long_domain = "a".repeat(255);
        let mut long_domain_data = vec![
            0x05, 0x01, 0x00, 0x03,  // VER, CMD, RSV, ATYP(Domain)
            255                      // 域名长度
        ];
        long_domain_data.extend_from_slice(long_domain.as_bytes());
        long_domain_data.extend_from_slice(&[0xFF, 0xFF]); // 端口: 65535

        match resolve_socks5_addr(&long_domain_data) {
            Ok((host, port)) => {
                assert_eq!(host.len(), 255);
                assert_eq!(port, 65535);
                println!("长域名解析成功，长度: {}, 端口: {}", host.len(), port);
            }
            Err(e) => panic!("长域名解析失败: {}", e),
        }

        // 测试空域名
        let empty_domain_data = vec![
            0x05, 0x01, 0x00, 0x03,  // VER, CMD, RSV, ATYP(Domain)
            0,                       // 域名长度为0
            0x00, 0x01               // 端口: 1
        ];

        match resolve_socks5_addr(&empty_domain_data) {
            Ok((host, port)) => {
                assert_eq!(host, "");
                assert_eq!(port, 1);
                println!("空域名解析成功: '{}', 端口: {}", host, port);
            }
            Err(e) => panic!("空域名解析失败: {}", e),
        }
    }
}