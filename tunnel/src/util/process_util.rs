//! # 进程管理工具
//!
//! 提供进程管理和网络端口查询相关的工具函数，主要用于：
//! - 根据网络端口查找对应的进程名称
//! - 获取进程的详细信息
//! - 网络连接状态监控

use flyshadow_common::tunnel::tunnel_package::PackageProtocol;
use sysinfo::{Pid, ProcessesToUpdate};

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试网络连接信息获取功能（仅Windows系统）
    #[cfg(target_os = "windows")]
    #[tokio::test]
    async fn test_network_connections() {
        use netstat2::{get_sockets_info, AddressFamilyFlags, ProtocolFlags, ProtocolSocketInfo};

        let af_flags = AddressFamilyFlags::IPV4;
        let proto_flags = ProtocolFlags::TCP | ProtocolFlags::UDP;

        match get_sockets_info(af_flags, proto_flags) {
            Ok(sockets_info) => {
                println!("成功获取到 {} 个网络连接", sockets_info.len());

                // 只显示前5个连接作为示例
                for (index, socket_info) in sockets_info.iter().take(5).enumerate() {
                    match socket_info.protocol_socket_info {
                        ProtocolSocketInfo::Tcp(ref tcp_info) => println!(
                            "[{}] TCP {}:{} -> {}:{} 进程ID: {:?} 状态: {}",
                            index + 1,
                            tcp_info.local_addr,
                            tcp_info.local_port,
                            tcp_info.remote_addr,
                            tcp_info.remote_port,
                            socket_info.associated_pids,
                            tcp_info.state
                        ),
                        ProtocolSocketInfo::Udp(ref udp_info) => println!(
                            "[{}] UDP {}:{} -> *:* 进程ID: {:?}",
                            index + 1,
                            udp_info.local_addr,
                            udp_info.local_port,
                            socket_info.associated_pids
                        ),
                    }
                }
            }
            Err(e) => println!("获取网络连接信息失败: {:?}", e),
        }
    }

    /// 测试macOS系统上的lsof命令功能
    #[cfg(target_os = "macos")]
    #[tokio::test]
    async fn test_macos_lsof_command() {
        use std::process::Command;

        // 测试lsof命令是否可用
        let output = Command::new("lsof")
            .arg("-v")
            .output();

        match output {
            Ok(result) => {
                if result.status.success() {
                    println!("lsof命令可用，版本信息:");
                    if let Ok(version_info) = String::from_utf8(result.stdout) {
                        println!("{}", version_info.lines().take(3).collect::<Vec<_>>().join("\n"));
                    }
                } else {
                    println!("lsof命令执行失败");
                }
            }
            Err(e) => {
                println!("lsof命令不可用: {:?}", e);
            }
        }

        // 测试查询一些常见端口
        let test_ports = vec![22, 80, 443, 53];
        for port in test_ports {
            let tcp_result = Command::new("lsof")
                .arg("-n")
                .arg("-P")
                .arg("-i")
                .arg(format!("tcp:{}", port))
                .output();

            if let Ok(output) = tcp_result {
                if output.status.success() {
                    if let Ok(output_str) = String::from_utf8(output.stdout) {
                        let lines: Vec<&str> = output_str.lines().collect();
                        if lines.len() > 1 {
                            println!("端口 {} (TCP) 有进程占用:", port);
                            for line in lines.iter().take(3) {
                                println!("  {}", line);
                            }
                        }
                    }
                }
            }
        }
    }

    /// 测试根据端口获取进程名称功能
    #[tokio::test]
    async fn test_get_process_by_port() {
        // 测试常见的系统端口
        let test_ports = vec![
            (80, PackageProtocol::TCP, "HTTP端口"),
            (443, PackageProtocol::TCP, "HTTPS端口"),
            (53, PackageProtocol::UDP, "DNS端口"),
        ];

        for (port, protocol, description) in test_ports {
            let process_name = get_process_name_by_port(port, protocol);
            if process_name.is_empty() {
                println!("{} ({}) 未被占用", description, port);
            } else {
                println!("{} ({}) 被进程 '{}' 占用", description, port, process_name);
            }
        }

        // 测试一个不太可能被占用的高端口
        let high_port_process = get_process_name_by_port(61563, PackageProtocol::TCP);
        println!("高端口61563的占用情况: '{}'", high_port_process);
    }
}

/// 根据端口号和协议类型获取对应的进程名称
///
/// # 功能说明
/// 通过查询系统网络连接信息，找到监听指定端口的进程，并返回进程名称。
/// 支持Windows和macOS系统。
///
/// # 参数
/// * `port` - 要查询的端口号
/// * `package_protocol` - 网络协议类型（TCP/UDP等）
///
/// # 返回值
/// 返回进程名称字符串，如果未找到则返回空字符串
///
/// # 示例
/// ```rust
/// let process_name = get_process_name_by_port(80, PackageProtocol::TCP);
/// println!("端口80被进程 '{}' 占用", process_name);
/// ```
pub fn get_process_name_by_port(port: u16, package_protocol: PackageProtocol) -> String {
    #[cfg(target_os = "windows")]
    {
        use netstat2::ProtocolFlags;
        use netstat2::{get_sockets_info, AddressFamilyFlags, ProtocolSocketInfo};

        // 设置地址族为IPv4
        let af_flags = AddressFamilyFlags::IPV4;

        // 根据协议类型设置相应的标志
        let proto_flags = match package_protocol {
            PackageProtocol::TCP => ProtocolFlags::TCP,
            PackageProtocol::UDP | PackageProtocol::NativeUdp => ProtocolFlags::UDP,
            PackageProtocol::NONE => return String::new(),
        };

        // 获取系统网络连接信息
        let sockets_info = match get_sockets_info(af_flags, proto_flags) {
            Ok(info) => info,
            Err(_) => return String::new(),
        };

        // 遍历所有网络连接，查找匹配的端口
        for socket_info in sockets_info {
            // 跳过没有关联进程的连接
            if socket_info.associated_pids.is_empty() {
                continue;
            }

            // 检查端口是否匹配
            let port_matches = match socket_info.protocol_socket_info {
                ProtocolSocketInfo::Tcp(ref tcp_info) => port == tcp_info.local_port,
                ProtocolSocketInfo::Udp(ref udp_info) => port == udp_info.local_port,
            };

            if port_matches {
                // 获取第一个关联进程的名称
                if let Some(&first_pid) = socket_info.associated_pids.first() {
                    return get_process_name_by_id(&first_pid);
                }
            }
        }
    }

    #[cfg(target_os = "macos")]
    {
        use std::process::Command;

        // 根据协议类型设置相应的协议字符串
        let protocol_str = match package_protocol {
            PackageProtocol::TCP => "tcp",
            PackageProtocol::UDP | PackageProtocol::NativeUdp => "udp",
            PackageProtocol::NONE => return String::new(),
        };

        // 执行lsof命令查询指定端口的进程信息
        // 使用 -n 参数避免DNS解析，提高执行速度
        // 使用 -P 参数显示端口号而不是服务名
        let output = match Command::new("lsof")
            .arg("-n")
            .arg("-P")
            .arg("-i")
            .arg(format!("{}:{}", protocol_str, port))
            .output()
        {
            Ok(output) => output,
            Err(_) => return String::new(),
        };

        // 检查命令是否执行成功
        if !output.status.success() {
            return String::new();
        }

        // 将输出转换为字符串，使用lossy转换处理非UTF-8字符
        let output_str = String::from_utf8_lossy(&output.stdout);

        // 解析lsof输出，查找第一个匹配的进程
        // lsof输出格式：COMMAND PID USER FD TYPE DEVICE SIZE/OFF NODE NAME
        for line in output_str.lines().skip(1) {
            // 跳过标题行
            if line.trim().is_empty() {
                continue;
            }

            // 分割行内容，获取各个字段
            let fields: Vec<&str> = line.split_whitespace().collect();
            if fields.len() >= 1 {
                // 第一个字段是COMMAND（进程名称）
                let process_name = fields[0].to_string();

                // 过滤掉一些系统进程或无效的进程名
                if !process_name.is_empty() && process_name != "COMMAND" {
                    return process_name;
                }
            }
        }
    }

    // 非支持系统或未找到匹配进程时返回空字符串
    String::new()
}

/// 根据进程ID获取进程名称
///
/// # 功能说明
/// 通过系统API查询指定进程ID对应的进程名称
///
/// # 参数
/// * `id` - 进程ID
///
/// # 返回值
/// 返回进程名称字符串，如果进程不存在或无法获取名称则返回空字符串
fn get_process_name_by_id(id: &u32) -> String {
    let mut system = sysinfo::System::new();
    let pid = Pid::from_u32(*id);

    // 刷新指定进程的信息
    system.refresh_processes(ProcessesToUpdate::Some(&[pid]), true);

    // 获取进程名称，如果失败则返回空字符串
    system
        .process(pid)
        .and_then(|process| process.name().to_str())
        .unwrap_or("")
        .to_string()
}