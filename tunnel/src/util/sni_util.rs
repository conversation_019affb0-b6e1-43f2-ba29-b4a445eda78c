//! # SNI (Server Name Indication) 解析工具
//!
//! 提供从TLS握手数据中解析SNI信息的功能，用于：
//! - 从TLS ClientHello消息中提取服务器名称
//! - 支持域名路由和流量分析
//! - 实现基于SNI的代理功能

use regex::Regex;
use rustls::internal::msgs::codec::{Codec, Reader};
use rustls::internal::msgs::handshake::ClientExtension::ServerName;
use rustls::internal::msgs::handshake::ClientHelloPayload;

/// SNI解析工具结构体
///
/// 提供静态方法来解析TLS握手数据中的SNI信息
pub struct SniUtil;

impl SniUtil {
    /// 从TLS握手数据中解析SNI服务器名称
    ///
    /// # 功能说明
    /// 1. 解析TLS ClientHello消息
    /// 2. 提取ServerName扩展中的域名信息
    /// 3. 使用正则表达式匹配DNS名称
    ///
    /// # 参数
    /// * `buffer` - TLS握手数据缓冲区
    ///
    /// # 返回值
    /// 成功时返回Some(域名字符串)，失败时返回None
    ///
    /// # 示例
    /// ```rust
    /// let sni_domain = SniUtil::resolve_sni(&tls_handshake_data);
    /// if let Some(domain) = sni_domain {
    ///     println!("检测到SNI域名: {}", domain);
    /// }
    /// ```
    pub fn resolve_sni(buffer: &[u8]) -> Option<String> {
        // 检查缓冲区最小长度要求
        if buffer.len() < 10 {
            return None;
        }

        // 解析ClientHello载荷，跳过前9个字节的TLS记录头
        let payload = match ClientHelloPayload::read(&mut Reader::init(&buffer[9..])) {
            Ok(payload) => payload,
            Err(_) => return None,
        };

        // 筛选出ServerName扩展
        let server_name_extensions: Vec<_> = payload
            .extensions
            .iter()
            .filter(|extension| matches!(extension, ServerName { .. }))
            .collect();

        // 遍历所有ServerName扩展
        for extension in server_name_extensions {
            if let ServerName(server_name_list) = extension {
                // 将服务器名称转换为调试字符串格式
                let server_str = format!("{:?}", server_name_list);

                // 使用正则表达式提取DNS名称
                // 匹配格式: DnsName("example.com")
                if let Ok(regex) = Regex::new(r#"DnsName\("([^"]+)"\)"#) {
                    if let Some(captures) = regex.captures(&server_str) {
                        if let Some(domain_match) = captures.get(1) {
                            return Some(domain_match.as_str().to_string());
                        }
                    }
                }
            }
        }

        None
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试SNI解析功能的边界情况
    #[test]
    fn test_sni_edge_cases() {
        // 测试空缓冲区
        assert_eq!(SniUtil::resolve_sni(&[]), None);

        // 测试长度不足的缓冲区
        let short_buffer = vec![0u8; 5];
        assert_eq!(SniUtil::resolve_sni(&short_buffer), None);

        // 测试最小长度缓冲区
        let min_buffer = vec![0u8; 10];
        assert_eq!(SniUtil::resolve_sni(&min_buffer), None);

        println!("SNI边界情况测试完成");
    }

    /// 测试无效的TLS数据
    #[test]
    fn test_invalid_tls_data() {
        // 创建一个看起来像TLS但实际无效的数据包
        let mut invalid_data = vec![0x16, 0x03, 0x01]; // TLS记录头
        invalid_data.extend_from_slice(&[0x00, 0x20]); // 长度
        invalid_data.extend_from_slice(&vec![0xFF; 50]); // 无效数据

        let result = SniUtil::resolve_sni(&invalid_data);
        assert_eq!(result, None);

        println!("无效TLS数据测试完成");
    }

    /// 演示SNI解析的预期用法
    #[test]
    fn test_sni_usage_example() {
        // 注意：这里只是演示用法，实际的TLS握手数据需要真实的客户端生成
        println!("SNI解析工具使用示例:");
        println!("1. 从网络数据包中捕获TLS握手数据");
        println!("2. 调用 SniUtil::resolve_sni(&handshake_data)");
        println!("3. 根据返回的域名进行路由决策");

        // 模拟处理流程
        let mock_data = vec![0u8; 15]; // 模拟数据
        match SniUtil::resolve_sni(&mock_data) {
            Some(domain) => println!("解析到域名: {}", domain),
            None => println!("未能解析出有效的SNI域名"),
        }
    }
}
