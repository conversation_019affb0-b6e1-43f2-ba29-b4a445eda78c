use log::Level;
use serde_json::Value;
use std::fs::File;
use std::io::Read;
use std::string::ToString;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use tunnel::context::context::TunnelContext;
use tunnel::proxy::proxy::Proxy;
use tunnel::tunnel::tunnel::Tunnel;

#[tokio::main]
async fn main() {
    let _ = simple_logger::init_with_level(Level::Info);

    // let ping = Tunnel::test_ping(vec![format!("{}:{}:{}", "47.242.6.116", "6001", "855ddy1sg2nczhxh4vgl").to_string()]).await;
    //
    // println!("{:?}", ping);

    let tunnel_context = Arc::new(TunnelContext::new().await);

    let mut rule = "".to_string();
    File::open("C:/Users/<USER>/Downloads/FlyShadowRule.json").unwrap().read_to_string(&mut rule).unwrap();
    let v: Value = serde_json::from_str(&rule).unwrap();

    if let Value::Array(items) = v {
        // 遍历数组中的每个元素
        for item in items {
            // 每个元素也是一个 JSON 对象，可以使用 .get() 方法访问字段
            if let Value::Object(map) = item {
                tunnel_context.set_domain_rule_obj(&map.get("domain").unwrap().as_str().unwrap().to_string(),
                                                   map.get("matching").unwrap().as_i64().unwrap() as i32,
                                                   map.get("proxyType").unwrap().as_i64().unwrap() as i32, 0).await;
            }
        }
    }

    tunnel_context.set_proxy_type(3).await;
    let uuid = "e0ccb15f59bc400c807268469d322dfd".to_string();
    // let uuid = Uuid::new_v4().simple().encode_lower(&mut Uuid::encode_buffer()).to_string();
    log::info!("uuid :{}",uuid);
    tunnel_context.set_client_uuid(uuid.clone()).await;

    tunnel_context.add_port_forwarding(8099, "172.16.50.166:80".to_string(), uuid).await.expect("TODO: panic message");

    let proxy = Proxy::new(tunnel_context.clone());
    match proxy.start(6551).await {
        Ok(_) => {}
        Err(e) => {
            log::error!("start proxy error: {}", e);
        }
    };

    tunnel_context.connect_tunnel("hk16.up-up-up.com".to_string(), 6000, "yocKPCNRRV5EdeMN4EQu".to_string()).await;


    let tunnel_context1 = tunnel_context.clone();

    // spawn(async move {
    //     sleep(Duration::from_secs(3)).await;
    //     loop {
    //         sleep(Duration::from_secs(1)).await;
    //         match tunnel_context1.get_tunnel_status().await {
    //             0 => {
    //                 println!("upload: {}", tunnel_context1.get_tunnel_upload().await as f32 / 1000.0 / 1000.0);
    //                 println!("download: {}", tunnel_context1.get_tunnel_download().await as f32 / 1000.0 / 1000.0);
    //                 println!("status: {}", tunnel_context1.get_tunnel_status().await);
    //             }
    //             _ => { break; }
    //         }
    //     }
    // });

    loop {
        sleep(Duration::from_secs(20000)).await;
    }
}

