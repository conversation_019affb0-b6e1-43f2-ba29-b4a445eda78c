use crate::context::context::TunnelContext;
use crate::proxy::proxy::Proxy;
use log::{info, Level};
use serde_json::Value;
use std::fs::File;
use std::io::Read;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;

#[tokio::test]
async fn test() {
    let _ = simple_logger::init_with_level(Level::Info);

    let tunnel_context = Arc::new(TunnelContext::new().await);

    let mut rule = "".to_string();
    File::open("C:/Users/<USER>/Downloads/FlyShadowRule.json").unwrap().read_to_string(&mut rule).unwrap();
    let v: Value = serde_json::from_str(&rule).unwrap();

    if let Value::Array(items) = v {
        // 遍历数组中的每个元素
        for item in items {
            // 每个元素也是一个 JSON 对象，可以使用 .get() 方法访问字段
            if let Value::Object(map) = item {
                tunnel_context.set_domain_rule_obj(&map.get("domain").unwrap().as_str().unwrap().to_string(),
                                                   map.get("matching").unwrap().as_i64().unwrap() as i32,
                                                   map.get("proxyType").unwrap().as_i64().unwrap() as i32, 0).await;
            }
        }
    }

    tunnel_context.set_proxy_type(3).await;
    let uuid = "e0ccb15f59bc400c807268469d322dfd".to_string();
    log::info!("uuid :{}",uuid);
    tunnel_context.set_client_uuid(uuid.clone()).await;

    tunnel_context.add_port_forwarding(8099, "*************:80".to_string(), "abccb15f59bc400c807268469d322dfd".to_string()).await.expect("TODO: panic message");

    let proxy = Proxy::new(tunnel_context.clone());
    match proxy.start(6551).await {
        Ok(_) => {}
        Err(e) => {
            log::error!("start proxy error: {}", e);
        }
    };

    tunnel_context.connect_tunnel("127.0.0.1".to_string(), 6701, "rjTqbIp0qdCoSom2TFTj".to_string()).await;

    run_server().await;
}

async fn run_server() {
    let tunnel_context = Arc::new(TunnelContext::new().await);

    let mut rule = "".to_string();
    File::open("C:/Users/<USER>/Downloads/FlyShadowRule.json").unwrap().read_to_string(&mut rule).unwrap();
    let v: Value = serde_json::from_str(&rule).unwrap();

    if let Value::Array(items) = v {
        // 遍历数组中的每个元素
        for item in items {
            // 每个元素也是一个 JSON 对象，可以使用 .get() 方法访问字段
            if let Value::Object(map) = item {
                tunnel_context.set_domain_rule_obj(&map.get("domain").unwrap().as_str().unwrap().to_string(),
                                                   map.get("matching").unwrap().as_i64().unwrap() as i32,
                                                   map.get("proxyType").unwrap().as_i64().unwrap() as i32, 0).await;
            }
        }
    }

    tunnel_context.set_proxy_type(3).await;
    let uuid = "abccb15f59bc400c807268469d322dfd".to_string();
    // let uuid = Uuid::new_v4().simple().encode_lower(&mut Uuid::encode_buffer()).to_string();
    log::info!("uuid :{}",uuid);
    tunnel_context.set_client_uuid(uuid.clone()).await;

    let proxy = Proxy::new(tunnel_context.clone());
    match proxy.start(6552).await {
        Ok(_) => {}
        Err(e) => {
            log::error!("start proxy error: {}", e);
        }
    };

    tunnel_context.connect_tunnel("127.0.0.1".to_string(), 6701, "rjTqbIp0qdCoSom2TFTj".to_string()).await;


    loop {
        sleep(Duration::from_secs(20000)).await;
    }
}