//! # 隧道映射器模块
//!
//! 本模块提供了隧道映射的核心功能，包括：
//! - 隧道映射器：负责处理不同类型的网络连接映射
//! - 映射器上下文：管理映射器的生命周期和配置
//! - 映射器信息：存储连接的元数据和流量统计
//! - TProxy UDP 缓存：优化 UDP 连接的性能
//!
//! ## 主要组件
//!
//! ### TunnelMapper
//! 核心映射器，支持多种连接类型：
//! - TCP 直连/隧道连接
//! - UDP 直连/隧道连接
//! - SOCKS5 UDP 隧道
//! - TProxy UDP 连接
//! - 原生 UDP 隧道
//!
//! ### TunnelMapperContext
//! 映射器上下文管理器，提供：
//! - 映射器生命周期管理
//! - 域名规则匹配
//! - Fake IP 缓存
//! - 网络接口选择
//!
//! ### TunnelMapperInfo
//! 连接信息存储，包含：
//! - 连接元数据（源地址、目标地址等）
//! - 流量统计信息
//! - 代理类型和匹配规则
//!
//! ### TProxyUdpCache
//! UDP 连接缓存，用于：
//! - 复用 UDP 套接字
//! - 管理连接超时
//! - 优化性能

pub mod tunnel_mapper;
pub mod tunnel_mapper_context;
pub mod tunnel_mapper_info;
pub mod tproxy_udp_cache;

pub use tproxy_udp_cache::TProxyUdpCache;
// 重新导出主要类型，方便外部使用
pub use tunnel_mapper::{TunnelMapper, TunnelMapperType};
pub use tunnel_mapper_context::TunnelMapperContext;
pub use tunnel_mapper_info::{TunnelMapperInfo, TunnelMapperInfoSerialize, TunnelMapperTrafficInfo};
