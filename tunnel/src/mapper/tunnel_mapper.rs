//! # 隧道映射器模块
//!
//! 本模块实现了核心的隧道映射功能，支持多种网络协议和连接类型的映射。
//! 提供了 TCP/UDP 直连、隧道连接、SOCKS5 代理、TProxy 等多种连接方式。

use crate::context::proxy_type::ProxyType;
use crate::mapper::tunnel_mapper_context::TunnelMapperContext;
use crate::mapper::tunnel_mapper_info::TunnelMapperInfo;
use crate::tunnel::tunnel::Tunnel;
use flyshadow_common::interface::interface_selector::InterfaceSelector;
use flyshadow_common::tunnel::tunnel_package::{PackageCmd, PackageProtocol, TunnelPackage};
use flyshadow_common::util::address_util::AddressUtil;
use futures::future::select_ok;
pub use futures::SinkExt;
use futures_util::FutureExt;
use log::{debug, error};
use netstack_lwip::SendHalf;
use std::net::{IpAddr, SocketAddr};
use std::sync::Arc;
use std::time::Duration;
use tokio::io::{AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};
use tokio::net::tcp::OwnedWriteHalf;
use tokio::net::{TcpSocket, TcpStream, UdpSocket};
use tokio::spawn;
use tokio::sync::oneshot::channel;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time::{sleep, timeout, Instant};

/// 隧道映射器类型枚举
///
/// 定义了支持的各种隧道映射类型，包括直连、隧道、代理等不同的连接方式。
#[derive(PartialEq, Clone, Debug)]
pub enum TunnelMapperType {
    /// 代理直连模式
    ProxyDirect,
    /// 代理隧道模式
    ProxyTunnel,
    /// SOCKS5 UDP 隧道
    Socks5UDPTunnel,
    /// TUN UDP 直连
    TunUDPDirect,
    /// TUN UDP 隧道
    TunUDPTunnel,
    /// TProxy UDP 隧道
    TProxyUDPTunnel,
    /// TUN 原生 UDP 隧道
    TunNativeUDPTunnel,
    /// TProxy 原生 UDP 隧道
    TProxyNativeUDPTunnel,
    /// TUN TCP 直连
    TunTcpDirect,
    /// TUN TCP 隧道
    TunTcpTunnel,
}

/// 隧道映射器结构体
///
/// 核心的隧道映射器，负责管理单个网络连接的映射关系。
/// 支持多种连接类型和协议，提供统一的数据传输接口。
pub struct TunnelMapper {
    /// 隧道映射器信息（连接元数据、流量统计等）
    pub tunnel_mapper_info: RwLock<TunnelMapperInfo>,
    /// 隧道映射器上下文的共享引用
    tunnel_mapper_context: Arc<TunnelMapperContext>,
    /// 隧道映射器类型
    pub tunnel_mapper_type: TunnelMapperType,
    /// 服务器端写入器（用于直连模式）
    server_writer: Option<RwLock<OwnedWriteHalf>>,
    /// 客户端写入器（用于代理模式）
    client_writer: Option<RwLock<OwnedWriteHalf>>,
    /// TUN 流写入器（用于 TUN 模式）
    tun_stream_writer: Option<RwLock<Box<dyn AsyncWrite + Send + Unpin + Sync>>>,
    /// 服务器读取处理任务句柄
    server_read_handler: Option<JoinHandle<()>>,
    /// 隧道连接的共享引用
    tunnel: Option<Arc<Tunnel>>,
    /// 包协议类型
    pub package_protocol: PackageProtocol,
    /// UDP 套接字（用于 UDP 连接）
    udp_socket: Option<Arc<UdpSocket>>,
    /// 最后活跃时间（用于超时检测）
    pub active_time: Arc<RwLock<Instant>>,
    /// TUN 写入器（用于 TUN 模式的 UDP）
    tun_writer: Option<Arc<SendHalf>>,
    /// 原生 UDP 是否已创建的标志
    native_udp_created: RwLock<bool>,
}

impl TunnelMapper {
    /// 生成 UDP 连接的唯一键值
    ///
    /// # 参数
    /// * `addr` - 源地址
    /// * `port` - 源端口
    ///
    /// # 返回值
    /// 返回格式化的键值字符串
    fn generate_udp_key(addr: &str, port: u16) -> String {
        format!("{}:{}==={:?}", addr, port, PackageProtocol::UDP)
    }

    /// 生成 TCP 连接的唯一键值
    ///
    /// # 参数
    /// * `addr` - 源地址
    /// * `port` - 源端口
    ///
    /// # 返回值
    /// 返回格式化的键值字符串
    fn generate_tcp_key(addr: &str, port: u16) -> String {
        format!("{}:{}==={:?}", addr, port, PackageProtocol::TCP)
    }

    /// 生成原生 UDP 连接的唯一键值
    ///
    /// # 参数
    /// * `addr` - 源地址
    /// * `port` - 源端口
    ///
    /// # 返回值
    /// 返回格式化的键值字符串
    fn generate_native_udp_key(addr: &str, port: u16) -> String {
        format!("{}:{}==={:?}", addr, port, PackageProtocol::NativeUdp)
    }

    /// 从 SOCKS5 UDP 连接创建隧道映射
    ///
    /// 为 SOCKS5 UDP 连接创建隧道映射器，将 UDP 流量通过隧道转发。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    /// * `udp_socket` - UDP 套接字
    /// * `tunnel` - 隧道连接
    pub async fn create_from_socks5_udp_to_tunnel(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_mapper_info: TunnelMapperInfo,
        udp_socket: Arc<UdpSocket>,
        tunnel: Arc<Tunnel>,
    ) {
        let key = Self::generate_udp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        // 检查映射是否已存在，避免重复创建
        if !tunnel_mapper_context.exist(&key).await {
            let mapper = TunnelMapper {
                tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
                tunnel_mapper_context: tunnel_mapper_context.clone(),
                tunnel_mapper_type: TunnelMapperType::Socks5UDPTunnel,
                server_writer: None,
                client_writer: None,
                tun_stream_writer: None,
                server_read_handler: None,
                tunnel: Some(tunnel),
                package_protocol: PackageProtocol::UDP,
                udp_socket: Some(udp_socket),
                active_time: Arc::new(RwLock::new(Instant::now())),
                tun_writer: None,
                native_udp_created: RwLock::new(false),
            };

            tunnel_mapper_context.add(key, mapper).await;
        }
    }

    /// 从 TUN UDP 连接创建隧道映射
    ///
    /// 为 TUN 模式的 UDP 连接创建隧道映射器，将 UDP 流量通过隧道转发。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    /// * `writer` - TUN 写入器
    /// * `tunnel` - 隧道连接
    pub async fn create_from_tun_udp_to_tunnel(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_mapper_info: TunnelMapperInfo,
        writer: Arc<SendHalf>,
        tunnel: Arc<Tunnel>,
    ) {
        let key = Self::generate_udp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        if !tunnel_mapper_context.exist(&key).await {
            let mapper = TunnelMapper {
                tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
                tunnel_mapper_context: tunnel_mapper_context.clone(),
                tunnel_mapper_type: TunnelMapperType::TunUDPTunnel,
                server_writer: None,
                client_writer: None,
                tun_stream_writer: None,
                server_read_handler: None,
                tunnel: Some(tunnel),
                package_protocol: PackageProtocol::UDP,
                udp_socket: None,
                active_time: Arc::new(RwLock::new(Instant::now())),
                tun_writer: Some(writer),
                native_udp_created: RwLock::new(false),
            };

            tunnel_mapper_context.add(key, mapper).await;
        }
    }

    /// 从 TUN 原生 UDP 连接创建隧道映射
    ///
    /// 为 TUN 模式的原生 UDP 连接创建隧道映射器，支持原生 UDP 协议的隧道转发。
    /// 原生 UDP 模式下，数据包会被封装为特定格式通过隧道传输。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    /// * `writer` - TUN 写入器
    /// * `tunnel` - 隧道连接
    pub async fn create_from_tun_native_udp_to_tunnel(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_mapper_info: TunnelMapperInfo,
        writer: Arc<SendHalf>,
        tunnel: Arc<Tunnel>,
    ) {
        let source_addr_str = format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);
        let target_addr_str = format!("{}:{}", tunnel_mapper_info.target_addr, tunnel_mapper_info.target_port);
        let key = Self::generate_native_udp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        // 检查映射是否已存在
        if tunnel_mapper_context.exist(&key).await {
            return;
        }

        // 创建 UDP 套接字
        let sock = match Self::create_udp_socket(tunnel_mapper_context.clone()).await {
            Ok(sock) => sock,
            Err(e) => {
                error!("创建 UDP 套接字失败: {}", e);
                return;
            }
        };

        // 解析源地址
        let source_addr = match format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port)
            .parse::<SocketAddr>()
        {
            Ok(addr) => addr,
            Err(e) => {
                error!("解析源地址失败: {}", e);
                return;
            }
        };

        // 创建活跃时间跟踪
        let active_time = Arc::new(RwLock::new(Instant::now()));
        let active_time_clone = active_time.clone();
        let tunnel_clone = tunnel.clone();
        let traffic_info = tunnel_mapper_info.traffic_info.clone();
        let sock_clone = sock.clone();

        // 创建隧道映射器
        tunnel_mapper_context.clone().add(key, TunnelMapper {
            tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
            tunnel_mapper_context,
            tunnel_mapper_type: TunnelMapperType::TunNativeUDPTunnel,
            server_writer: None,
            client_writer: None,
            tun_stream_writer: None,
            server_read_handler: Some(spawn(async move {
                Self::handle_native_udp_read(
                    sock_clone,
                    writer,
                    source_addr,
                    tunnel_clone,
                    traffic_info,
                    active_time_clone,
                ).await;
            })),
            tunnel: Some(tunnel.clone()),
            package_protocol: PackageProtocol::NativeUdp,
            udp_socket: Some(sock),
            active_time,
            tun_writer: None,
            native_udp_created: RwLock::new(false),
        }).await;

        // 发送新连接命令到隧道
        let tunnel_package = TunnelPackage {
            cmd: PackageCmd::NewConnect,
            protocol: PackageProtocol::NativeUdp,
            source_address: Some(source_addr_str),
            target_address: Some(target_addr_str),
            data: None,
        };

        if let Err(e) = tunnel.write_to_tunnel(tunnel_package).await {
            error!("发送新连接命令失败: {}", e);
        }
    }

    /// 处理原生 UDP 数据读取
    ///
    /// 从 UDP 套接字读取数据，解析隧道包格式，并转发到 TUN 接口。
    ///
    /// # 参数
    /// * `sock` - UDP 套接字
    /// * `writer` - TUN 写入器
    /// * `source_addr` - 源地址
    /// * `tunnel` - 隧道连接（用于流量统计）
    /// * `traffic_info` - 流量统计信息
    /// * `active_time` - 活跃时间跟踪
    async fn handle_native_udp_read(
        sock: Arc<UdpSocket>,
        writer: Arc<SendHalf>,
        source_addr: SocketAddr,
        tunnel: Arc<Tunnel>,
        traffic_info: Arc<crate::mapper::tunnel_mapper_info::TunnelMapperTrafficInfo>,
        active_time: Arc<RwLock<Instant>>,
    ) {
        const BUFFER_SIZE: usize = 2048;
        const PACKAGE_HEADER: [u8; 2] = [0x0f, 0x2f];

        let mut buf = [0; BUFFER_SIZE];
        let mut package_buf = Vec::new();

        while let Ok((len, _addr)) = sock.recv_from(&mut buf).await {
            if len == 0 {
                break;
            }

            // 更新流量统计
            tunnel.add_download_traffic(len as i64).await;
            traffic_info.add_download_traffic(len as u128).await;

            // 处理包数据
            if len > 2 && buf[..2] == PACKAGE_HEADER {
                package_buf.clear();
            }
            package_buf.extend_from_slice(&buf[..len]);

            // 尝试解析隧道包
            match TunnelPackage::from_byte_array(&mut package_buf) {
                Ok(Some(package)) => {
                    if let Err(e) = Self::process_native_udp_package(package, &writer, &source_addr).await {
                        error!("处理原生 UDP 包失败: {}", e);
                        break;
                    }
                }
                Ok(None) => {
                    // 包不完整，继续读取
                    continue;
                }
                Err(e) => {
                    error!("解析隧道包失败: {}", e);
                }
            }

            // 更新活跃时间
            *active_time.write().await = Instant::now();
        }
    }

    /// 处理原生 UDP 隧道包
    ///
    /// 解析隧道包并将数据转发到 TUN 接口。
    ///
    /// # 参数
    /// * `package` - 隧道包
    /// * `writer` - TUN 写入器
    /// * `source_addr` - 源地址
    ///
    /// # 返回值
    /// 成功返回 Ok(())，失败返回错误信息
    async fn process_native_udp_package(
        package: TunnelPackage,
        writer: &Arc<SendHalf>,
        source_addr: &SocketAddr,
    ) -> Result<(), String> {
        let package_source_addr = package.source_address
            .ok_or("包中缺少源地址")?
            .parse::<SocketAddr>()
            .map_err(|e| format!("解析包源地址失败: {}", e))?;

        let data = package.data
            .ok_or("包中缺少数据")?;

        writer.send_to(&data, &package_source_addr, source_addr)
            .map_err(|e| format!("发送数据到 TUN 失败: {}", e))?;

        Ok(())
    }

    /// 创建 UDP 套接字
    ///
    /// 根据上下文配置创建 UDP 套接字，支持自动绑定网卡 IP 或网卡名称。
    /// 在 TUN 模式下会根据接口选择器配置绑定特定的网络接口。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    ///
    /// # 返回值
    /// 返回创建的 UDP 套接字的共享引用
    ///
    /// # 错误
    /// 如果套接字创建或绑定失败，返回相应的错误
    pub async fn create_udp_socket(tunnel_mapper_context: Arc<TunnelMapperContext>) -> anyhow::Result<Arc<UdpSocket>> {
        // 确定绑定接口配置
        let (bind_addr, device_name) = if tunnel_mapper_context.get_tun_mode_enable().await {
            let interface_selector = tunnel_mapper_context.get_local_interface_selector().await;

            if let Some(ipv4_device) = interface_selector.ipv4_device {
                // 绑定到指定设备
                ("0.0.0.0:0".parse::<SocketAddr>()?, Some(ipv4_device))
            } else if let Some(ipv4_addr) = interface_selector.ipv4 {
                // 绑定到指定 IP 地址
                (SocketAddr::new(IpAddr::V4(ipv4_addr), 0), None)
            } else {
                // 默认绑定
                ("0.0.0.0:0".parse::<SocketAddr>()?, None)
            }
        } else {
            // 非 TUN 模式，使用默认绑定
            ("0.0.0.0:0".parse::<SocketAddr>()?, None)
        };

        // 创建并绑定套接字
        let sock = UdpSocket::bind(bind_addr).await?;

        // 在 Linux 系统上绑定到指定网络设备
        #[cfg(any(target_os = "linux"))]
        {
            if let Some(device_name) = device_name {
                if let Err(e) = sock.bind_device(Some(device_name.as_bytes())) {
                    error!("套接字绑定设备失败: {}", e);
                }
            }
        }

        Ok(Arc::new(sock))
    }

    /// 从 TUN UDP 直连创建映射
    ///
    /// 为 TUN 模式的 UDP 直连创建隧道映射器，不通过隧道，直接转发 UDP 流量。
    /// 适用于需要直接访问目标服务器的 UDP 连接。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    /// * `writer` - TUN 写入器
    pub async fn create_from_tun_udp_to_direct(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_mapper_info: TunnelMapperInfo,
        writer: Arc<SendHalf>,
    ) {
        let key = Self::generate_udp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        // 检查映射是否已存在
        if tunnel_mapper_context.exist(&key).await {
            return;
        }

        // 创建 UDP 套接字
        let sock = match Self::create_udp_socket(tunnel_mapper_context.clone()).await {
            Ok(sock) => sock,
            Err(e) => {
                error!("创建 UDP 套接字失败: {}", e);
                return;
            }
        };

        // 解析源地址
        let source_addr = match format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port)
            .parse::<SocketAddr>()
        {
            Ok(addr) => addr,
            Err(e) => {
                error!("解析源地址失败: {}", e);
                return;
            }
        };

        // 创建活跃时间跟踪
        let active_time = Arc::new(RwLock::new(Instant::now()));
        let active_time_clone = active_time.clone();
        let traffic_info = tunnel_mapper_info.traffic_info.clone();
        let sock_clone = sock.clone();

        // 创建隧道映射器
        tunnel_mapper_context.clone().add(key, TunnelMapper {
            tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
            tunnel_mapper_context,
            tunnel_mapper_type: TunnelMapperType::TunUDPDirect,
            server_writer: None,
            client_writer: None,
            tun_stream_writer: None,
            server_read_handler: Some(spawn(async move {
                Self::handle_udp_direct_read(sock_clone, writer, source_addr, traffic_info, active_time_clone).await;
            })),
            tunnel: None,
            package_protocol: PackageProtocol::UDP,
            udp_socket: Some(sock),
            active_time,
            tun_writer: None,
            native_udp_created: RwLock::new(false),
        }).await;
    }

    /// 处理 UDP 直连数据读取
    ///
    /// 从 UDP 套接字读取数据并直接转发到 TUN 接口，不经过隧道处理。
    ///
    /// # 参数
    /// * `sock` - UDP 套接字
    /// * `writer` - TUN 写入器
    /// * `source_addr` - 源地址
    /// * `traffic_info` - 流量统计信息
    /// * `active_time` - 活跃时间跟踪
    async fn handle_udp_direct_read(
        sock: Arc<UdpSocket>,
        writer: Arc<SendHalf>,
        source_addr: SocketAddr,
        traffic_info: Arc<crate::mapper::tunnel_mapper_info::TunnelMapperTrafficInfo>,
        active_time: Arc<RwLock<Instant>>,
    ) {
        const BUFFER_SIZE: usize = 2048;
        let mut buf = [0; BUFFER_SIZE];

        while let Ok((len, addr)) = sock.recv_from(&mut buf).await {
            if len == 0 {
                break;
            }

            // 更新流量统计
            traffic_info.add_download_traffic(len as u128).await;

            // 转发数据到 TUN 接口
            let data = buf[..len].to_vec();
            if writer.send_to(&data, &addr, &source_addr).is_err() {
                error!("发送数据到 TUN 接口失败");
                break;
            }

            // 更新活跃时间
            *active_time.write().await = Instant::now();
        }
    }

    /// 从 TUN TCP 连接创建隧道映射
    ///
    /// 为 TUN 模式的 TCP 连接创建隧道映射器，将 TCP 流量通过隧道转发。
    /// 支持初始数据缓存和双向数据传输。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息（包含临时数据）
    /// * `tcp_stream` - TCP 流
    /// * `tunnel` - 隧道连接
    ///
    /// # 返回值
    /// 成功返回 Ok(())，失败返回错误信息
    pub async fn create_from_tun_to_tunnel<S>(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        mut tunnel_mapper_info: TunnelMapperInfo,
        tcp_stream: S,
        tunnel: Arc<Tunnel>,
    ) -> Result<(), String>
    where
        S: AsyncRead + AsyncWrite + Unpin + Send + 'static + Sync,
    {
        let (reader, writer) = tokio::io::split(tcp_stream);
        let key = Self::generate_tcp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        // 创建新连接命令
        let tunnel_package = TunnelPackage {
            cmd: PackageCmd::NewConnect,
            protocol: PackageProtocol::TCP,
            source_address: Some(format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port)),
            target_address: Some(format!("{}:{}", tunnel_mapper_info.target_addr, tunnel_mapper_info.target_port)),
            data: None,
        };

        // 发送新连接命令到隧道
        match tunnel.write_to_tunnel(tunnel_package).await {
            Ok(_) => {
                // 提取临时数据
                let temp_data: Vec<u8> = tunnel_mapper_info.temp_data.drain(..).collect();
                let traffic_info = tunnel_mapper_info.traffic_info.clone();
                let tunnel_clone = tunnel.clone();
                let source_addr = format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);
                let target_addr = format!("{}:{}", tunnel_mapper_info.target_addr, tunnel_mapper_info.target_port);


                // 创建隧道映射器
                tunnel_mapper_context.clone().add(key.clone(), TunnelMapper {
                    tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
                    tunnel_mapper_context: tunnel_mapper_context.clone(),
                    tunnel_mapper_type: TunnelMapperType::TunTcpTunnel,
                    server_writer: None,
                    client_writer: None,
                    tun_stream_writer: Some(RwLock::new(Box::new(writer))),
                    server_read_handler: Some(spawn(async move {
                        Self::handle_tun_tcp_read(
                            reader,
                            source_addr,
                            target_addr,
                            tunnel,
                            tunnel_mapper_context,
                            key,
                            temp_data,
                            traffic_info,
                        ).await;
                    })),
                    tunnel: Some(tunnel_clone),
                    package_protocol: PackageProtocol::TCP,
                    udp_socket: None,
                    active_time: Arc::new(RwLock::new(Instant::now())),
                    tun_writer: None,
                    native_udp_created: RwLock::new(false),
                }).await;
                Ok(())
            }
            Err(e) => {
                tunnel_mapper_context.close(&key).await;
                Err(e)
            }
        }
    }

    /// 处理 TUN TCP 数据读取
    ///
    /// 从 TCP 流读取数据并通过隧道转发，包括处理初始临时数据。
    ///
    /// # 参数
    /// * `reader` - TCP 读取器
    /// * `tunnel` - 隧道连接
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `key` - 映射器键值
    /// * `temp_data` - 初始临时数据
    /// * `traffic_info` - 流量统计信息
    async fn handle_tun_tcp_read<R>(
        mut reader: R,
        source_addr: String,
        target_addr: String,
        tunnel: Arc<Tunnel>,
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        key: String,
        temp_data: Vec<u8>,
        traffic_info: Arc<crate::mapper::tunnel_mapper_info::TunnelMapperTrafficInfo>,
    )
    where
        R: AsyncRead + Unpin,
    {
        // 首先发送临时数据（如果有）
        if !temp_data.is_empty() {
            let tunnel_package = TunnelPackage {
                cmd: PackageCmd::TData,
                protocol: PackageProtocol::TCP,
                source_address: Some(source_addr.clone()),
                target_address: Some(target_addr.clone()),
                data: Some(temp_data),
            };
            let _ = tunnel.write_to_tunnel(tunnel_package).await;
        }

        // 持续读取并转发数据
        let mut buffer = Vec::new();
        loop {
            match reader.read_buf(&mut buffer).await {
                Ok(0) => {
                    // 连接关闭
                    break;
                }
                Ok(_) => {
                    // 更新流量统计
                    traffic_info.add_upload_traffic(buffer.len() as u128).await;

                    // 创建数据包并发送
                    let tunnel_package = TunnelPackage {
                        cmd: PackageCmd::TData,
                        protocol: PackageProtocol::TCP,
                        source_address: Some(source_addr.clone()),
                        target_address: Some(target_addr.clone()),
                        data: Some(buffer.drain(..).collect()),
                    };

                    if tunnel.write_to_tunnel(tunnel_package).await.is_err() {
                        error!("发送数据到隧道失败");
                        break;
                    }
                }
                Err(e) => {
                    error!("读取 TCP 数据失败: {}", e);
                    break;
                }
            }
        }

        // 清理映射器
        tunnel_mapper_context.close(&key).await;
    }

    /// 查询 DNS 并建立 TCP 连接
    ///
    /// 根据配置决定是否使用内置 DNS 解析，支持 TUN 模式的网络接口绑定。
    /// 在 TUN 模式下会使用指定的网络接口进行连接。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    ///
    /// # 返回值
    /// 成功返回 TCP 流，失败返回错误信息
    pub(crate) async fn query_dns_and_connect(
        tunnel_mapper_context: &Arc<TunnelMapperContext>,
        tunnel_mapper_info: &TunnelMapperInfo,
    ) -> Result<TcpStream, String> {
        let target_addr = &tunnel_mapper_info.target_addr;
        let port = tunnel_mapper_info.target_port;

        // 确定是否需要使用内置 DNS 和接口选择器
        let interface_selector = if tunnel_mapper_context.get_tun_mode_enable().await {
            Some(tunnel_mapper_context.get_local_interface_selector().await)
        } else if !tunnel_mapper_context.get_use_build_in_dns().await {
            // 非 TUN 模式且不使用内置 DNS，直接连接
            return Self::direct_tcp_connect(target_addr, port).await;
        } else {
            None
        };

        // 使用内置 DNS 解析
        let mut ip_addresses = match tunnel_mapper_context
            .fake_ip_cache_context
            .query_ip(interface_selector.clone(), target_addr)
            .await
        {
            Ok(addresses) => addresses,
            Err(e) => {
                error!("DNS 解析失败 {} : {}", target_addr, e);

                // TUN 模式下解析失败直接返回错误
                if tunnel_mapper_context.get_tun_mode_enable().await {
                    return Err(e.to_string());
                }

                // 非 TUN 模式下回退到直接连接
                return Self::direct_tcp_connect(target_addr, port).await;
            }
        };

        Self::connect_for_ip_addr_vec(target_addr, port, interface_selector, &mut ip_addresses).await
    }

    /// 直接 TCP 连接
    ///
    /// 不经过 DNS 解析，直接使用系统默认方式连接目标地址。
    ///
    /// # 参数
    /// * `target_addr` - 目标地址
    /// * `port` - 目标端口
    ///
    /// # 返回值
    /// 成功返回 TCP 流，失败返回错误信息
    async fn direct_tcp_connect(target_addr: &str, port: u16) -> Result<TcpStream, String> {
        match TcpStream::connect(format!("{}:{}", target_addr, port)).await {
            Ok(tcp_stream) => {
                debug!("直接连接成功: {}:{}", target_addr, port);
                Ok(tcp_stream)
            }
            Err(e) => {
                error!("直接连接失败 {}:{} : {}", target_addr, port, e);
                Err(e.to_string())
            }
        }
    }

    /// 从 IP 地址列表建立连接
    ///
    /// 支持 IPv4 和 IPv6 双栈连接，优先尝试 IPv6，如果失败则回退到 IPv4。
    /// 支持网络接口绑定，适用于 TUN 模式下的特定接口连接。
    ///
    /// # 参数
    /// * `target_addr` - 目标地址（用于日志）
    /// * `port` - 目标端口
    /// * `interface_selector` - 网络接口选择器（可选）
    /// * `ip_addresses` - IP 地址列表
    ///
    /// # 返回值
    /// 成功返回 TCP 流，失败返回错误信息
    async fn connect_for_ip_addr_vec(
        target_addr: &str,
        port: u16,
        interface_selector: Option<InterfaceSelector>,
        ip_addresses: &mut Vec<IpAddr>,
    ) -> Result<TcpStream, String> {
        let mut connect_futures = Vec::new();
        let (ipv6_ready_tx, ipv6_ready_rx) = channel::<()>();

        // 尝试 IPv6 连接
        if let Some(ipv6_addr) = ip_addresses.iter().find(|addr| addr.is_ipv6()).copied() {
            let interface_selector_clone = interface_selector.clone();
            let target_addr_clone = target_addr.to_string();

            if let Ok(tcp_socket) = TcpSocket::new_v6() {
                connect_futures.push(async move {
                    // 绑定网络接口（如果指定）
                    if let Some(iface) = interface_selector_clone {
                        if let Err(e) = InterfaceSelector::tcp_bind_ipv6(iface, &tcp_socket) {
                            error!("IPv6 接口绑定失败: {}", e);
                            let _ = ipv6_ready_tx.send(());
                            return Err(e);
                        }
                    }

                    let socket_addr = SocketAddr::new(ipv6_addr, port);
                    let start = Instant::now();
                    debug!("尝试 IPv6 连接: {} -> {}", target_addr_clone, socket_addr);

                    match tcp_socket.connect(socket_addr).await {
                        Ok(tcp_stream) => {
                            debug!("IPv6 连接成功: {} -> {} 耗时: {:?}", target_addr_clone, socket_addr, start.elapsed());
                            let _ = ipv6_ready_tx.send(());
                            Ok(tcp_stream)
                        }
                        Err(e) => {
                            debug!("IPv6 连接失败: {} -> {} 错误: {}", target_addr_clone, socket_addr, e);
                            let _ = ipv6_ready_tx.send(());
                            Err(e)
                        }
                    }
                }.boxed());
            } else {
                let _ = ipv6_ready_tx.send(());
            }
        } else {
            let _ = ipv6_ready_tx.send(());
        }

        // 尝试 IPv4 连接（等待 IPv6 尝试完成或超时）
        if let Some(ipv4_addr) = ip_addresses.iter().find(|addr| addr.is_ipv4()).copied() {
            let interface_selector_clone = interface_selector;
            let target_addr_clone = target_addr.to_string();

            connect_futures.push(async move {
                // 等待 IPv6 连接尝试完成或超时（300ms）
                let _ = timeout(Duration::from_millis(300), ipv6_ready_rx).await;

                let tcp_socket = match TcpSocket::new_v4() {
                    Ok(socket) => socket,
                    Err(e) => return Err(std::io::Error::new(std::io::ErrorKind::Other, format!("创建 IPv4 套接字失败: {}", e))),
                };

                // 绑定网络接口（如果指定）
                if let Some(iface) = interface_selector_clone {
                    if let Err(e) = InterfaceSelector::tcp_bind_ipv4(iface, &tcp_socket) {
                        return Err(std::io::Error::new(std::io::ErrorKind::Other, format!("IPv4 接口绑定失败: {}", e)));
                    }
                }

                let socket_addr = SocketAddr::new(ipv4_addr, port);
                let start = Instant::now();
                debug!("尝试 IPv4 连接: {} -> {}", target_addr_clone, socket_addr);

                match tcp_socket.connect(socket_addr).await {
                    Ok(tcp_stream) => {
                        debug!("IPv4 连接成功: {} -> {} 耗时: {:?}", target_addr_clone, socket_addr, start.elapsed());
                        Ok(tcp_stream)
                    }
                    Err(e) => {
                        debug!("IPv4 连接失败: {} -> {} 错误: {}", target_addr_clone, socket_addr, e);
                        Err(e)
                    }
                }
            }.boxed());
        }

        // 等待第一个成功的连接
        match select_ok(connect_futures).await {
            Ok((tcp_stream, _)) => Ok(tcp_stream),
            Err(e) => Err(format!("连接 [{}:{}] 失败: {}", target_addr, port, e)),
        }
    }

    /// 检查直连优先级
    ///
    /// 测试目标地址是否可以直连，如果可以则将代理类型设置为直连。
    /// 这个功能用于智能路由，优先使用直连以提高性能。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息（会被修改）
    /// * `timeout` - 连接超时时间（毫秒）
    pub(crate) async fn check_direct_connect_priority(
        tunnel_mapper_context: &Arc<TunnelMapperContext>,
        tunnel_mapper_info: &mut TunnelMapperInfo,
        timeout: u64,
    ) {
        let target_addr = &tunnel_mapper_info.target_addr;
        let port = tunnel_mapper_info.target_port;

        // 确定网络接口选择器配置
        let interface_selector = if tunnel_mapper_context.get_tun_mode_enable().await {
            // TUN 模式下使用指定的网络接口
            Some(tunnel_mapper_context.get_local_interface_selector().await)
        } else if tunnel_mapper_context.get_use_build_in_dns().await {
            // 使用内置 DNS 但非 TUN 模式
            None
        } else {
            // 不使用内置 DNS，直接尝试连接
            if Self::test_direct_connection(target_addr, port, timeout).await {
                Self::set_direct_connection_info(tunnel_mapper_info);
            }
            return;
        };

        // 使用内置 DNS 解析地址
        let mut ip_addresses = match tunnel_mapper_context
            .fake_ip_cache_context
            .query_ip(interface_selector.clone(), target_addr)
            .await
        {
            Ok(addresses) => addresses,
            Err(e) => {
                debug!("DNS 解析失败，尝试直连: {} - {}", target_addr, e);

                // TUN 模式下解析失败不尝试直连
                if tunnel_mapper_context.get_tun_mode_enable().await {
                    return;
                }

                // 非 TUN 模式下回退到直连测试
                if Self::test_direct_connection(target_addr, port, timeout).await {
                    Self::set_direct_connection_info(tunnel_mapper_info);
                }
                return;
            }
        };

        // 测试通过解析的 IP 地址连接
        if Self::test_ip_connection(target_addr, port, interface_selector, &mut ip_addresses, timeout).await {
            Self::set_direct_connection_info(tunnel_mapper_info);
        }
    }

    /// 测试直接连接
    ///
    /// 尝试直接连接目标地址，不经过 DNS 解析。
    ///
    /// # 参数
    /// * `target_addr` - 目标地址
    /// * `port` - 目标端口
    /// * `timeout` - 超时时间（毫秒）
    ///
    /// # 返回值
    /// 连接成功返回 true，失败返回 false
    async fn test_direct_connection(target_addr: &str, port: u16, timeout: u64) -> bool {
        let connect_future = TcpStream::connect(format!("{}:{}", target_addr, port));

        match tokio::time::timeout(Duration::from_millis(timeout), connect_future).await {
            Ok(Ok(_)) => {
                debug!("直连测试成功: {}:{}", target_addr, port);
                true
            }
            Ok(Err(e)) => {
                debug!("直连测试失败: {}:{} - {}", target_addr, port, e);
                false
            }
            Err(_) => {
                debug!("直连测试超时: {}:{}", target_addr, port);
                false
            }
        }
    }

    /// 测试 IP 地址连接
    ///
    /// 通过解析的 IP 地址列表测试连接。
    ///
    /// # 参数
    /// * `target_addr` - 目标地址（用于日志）
    /// * `port` - 目标端口
    /// * `interface_selector` - 网络接口选择器
    /// * `ip_addresses` - IP 地址列表
    /// * `timeout` - 超时时间（毫秒）
    ///
    /// # 返回值
    /// 连接成功返回 true，失败返回 false
    async fn test_ip_connection(
        target_addr: &str,
        port: u16,
        interface_selector: Option<InterfaceSelector>,
        ip_addresses: &mut Vec<IpAddr>,
        timeout: u64,
    ) -> bool {
        let connect_future = Self::connect_for_ip_addr_vec(target_addr, port, interface_selector, ip_addresses);

        match tokio::time::timeout(Duration::from_millis(timeout), connect_future).await {
            Ok(Ok(_)) => {
                debug!("IP 连接测试成功: {}:{}", target_addr, port);
                true
            }
            Ok(Err(e)) => {
                debug!("IP 连接测试失败: {}:{} - {}", target_addr, port, e);
                false
            }
            Err(_) => {
                debug!("IP 连接测试超时: {}:{}", target_addr, port);
                false
            }
        }
    }

    /// 设置直连信息
    ///
    /// 将隧道映射器信息设置为直连模式。
    ///
    /// # 参数
    /// * `tunnel_mapper_info` - 要修改的隧道映射器信息
    fn set_direct_connection_info(tunnel_mapper_info: &mut TunnelMapperInfo) {
        tunnel_mapper_info.proxy_type = ProxyType::Direct;
        tunnel_mapper_info.matcher_name = "direct_connect_priority".to_string();
        tunnel_mapper_info.matcher_rule = "".to_string();
        debug!("设置为直连模式: {}:{}", tunnel_mapper_info.target_addr, tunnel_mapper_info.target_port);
    }

    /// 从 TUN TCP 直连创建映射
    ///
    /// 为 TUN 模式的 TCP 直连创建隧道映射器，不通过隧道，直接转发 TCP 流量。
    /// 支持初始数据缓存和双向数据传输。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息（包含临时数据）
    /// * `tcp_stream` - 客户端 TCP 流
    ///
    /// # 返回值
    /// 成功返回 Ok(())，失败返回错误信息
    pub async fn create_from_tun_to_direct<S>(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        mut tunnel_mapper_info: TunnelMapperInfo,
        tcp_stream: S,
    ) -> Result<(), String>
    where
        S: AsyncRead + AsyncWrite + Unpin + Send + 'static + Sync,
    {
        // 建立到目标服务器的连接
        let server_stream = Self::query_dns_and_connect(&tunnel_mapper_context, &tunnel_mapper_info).await
            .map_err(|e| format!("连接目标服务器失败: {}", e))?;

        let key = Self::generate_tcp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);
        let temp_data: Vec<u8> = tunnel_mapper_info.temp_data.drain(..).collect();
        let traffic_info = tunnel_mapper_info.traffic_info.clone();
        // 创建隧道映射器
        tunnel_mapper_context.clone().add(key.clone(), TunnelMapper {
            tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
            tunnel_mapper_context: tunnel_mapper_context.clone(),
            tunnel_mapper_type: TunnelMapperType::TunTcpDirect,
            server_writer: None,
            client_writer: None,
            tun_stream_writer: None,
            server_read_handler: Some(spawn(async move {
                Self::handle_tun_tcp_direct_relay(
                    server_stream,
                    tcp_stream,
                    temp_data,
                    traffic_info,
                    tunnel_mapper_context,
                    key,
                ).await;
            })),
            tunnel: None,
            package_protocol: PackageProtocol::TCP,
            udp_socket: None,
            active_time: Arc::new(RwLock::new(Instant::now())),
            tun_writer: None,
            native_udp_created: RwLock::new(false),
        }).await;

        Ok(())
    }

    /// 处理 TUN TCP 直连数据中继
    ///
    /// 在客户端和服务器之间建立双向数据中继，包括处理初始临时数据。
    ///
    /// # 参数
    /// * `server_stream` - 服务器 TCP 流
    /// * `client_stream` - 客户端 TCP 流
    /// * `temp_data` - 初始临时数据
    /// * `traffic_info` - 流量统计信息
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `key` - 映射器键值
    async fn handle_tun_tcp_direct_relay<S>(
        mut server_stream: TcpStream,
        client_stream: S,
        temp_data: Vec<u8>,
        traffic_info: Arc<crate::mapper::tunnel_mapper_info::TunnelMapperTrafficInfo>,
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        key: String,
    )
    where
        S: AsyncRead + AsyncWrite + Unpin + Send + 'static,
    {
        // 首先发送临时数据到服务器（如果有）
        if !temp_data.is_empty() {
            if let Err(e) = server_stream.write_all(&temp_data).await {
                error!("发送临时数据到服务器失败: {}", e);
                tunnel_mapper_context.close(&key).await;
                return;
            }
        }

        // 分离读写流
        let (server_reader, server_writer) = server_stream.into_split();
        let (client_reader, client_writer) = tokio::io::split(client_stream);

        // 启动服务器到客户端的数据转发任务
        let server_to_client_task = Self::relay_server_to_client(
            server_reader,
            client_writer,
            traffic_info.clone(),
            tunnel_mapper_context.clone(),
            key.clone(),
        );

        // 启动客户端到服务器的数据转发任务
        let client_to_server_task = Self::relay_client_to_server(
            client_reader,
            server_writer,
            traffic_info,
        );

        // 等待任意一个方向的传输完成
        tokio::select! {
            _ = server_to_client_task => {
                debug!("服务器到客户端传输完成");
            }
            _ = client_to_server_task => {
                debug!("客户端到服务器传输完成");
            }
        }

        // 清理映射器
        tunnel_mapper_context.close(&key).await;
    }

    /// 服务器到客户端数据中继
    ///
    /// 从服务器读取数据并转发给客户端。
    ///
    /// # 参数
    /// * `server_reader` - 服务器读取器
    /// * `client_writer` - 客户端写入器
    /// * `traffic_info` - 流量统计信息
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `key` - 映射器键值
    async fn relay_server_to_client<R, W>(
        mut server_reader: R,
        mut client_writer: W,
        traffic_info: Arc<crate::mapper::tunnel_mapper_info::TunnelMapperTrafficInfo>,
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        key: String,
    )
    where
        R: AsyncRead + Unpin,
        W: AsyncWrite + Unpin,
    {
        const BUFFER_SIZE: usize = 16384;
        let mut buffer = [0u8; BUFFER_SIZE];

        loop {
            match server_reader.read(&mut buffer).await {
                Ok(0) => {
                    // 服务器关闭连接
                    debug!("服务器关闭连接");
                    break;
                }
                Ok(n) => {
                    // 更新下载流量统计
                    traffic_info.add_download_traffic(n as u128).await;

                    // 转发数据到客户端
                    if let Err(e) = client_writer.write_all(&buffer[..n]).await {
                        error!("写入客户端失败: {}", e);
                        tunnel_mapper_context.close(&key).await;
                        break;
                    }
                }
                Err(e) => {
                    error!("从服务器读取数据失败: {}", e);
                    break;
                }
            }
        }

        // 关闭客户端写入流
        let _ = client_writer.shutdown().await;
    }

    /// 客户端到服务器数据中继
    ///
    /// 从客户端读取数据并转发给服务器。
    ///
    /// # 参数
    /// * `client_reader` - 客户端读取器
    /// * `server_writer` - 服务器写入器
    /// * `traffic_info` - 流量统计信息
    async fn relay_client_to_server<R, W>(
        mut client_reader: R,
        mut server_writer: W,
        traffic_info: Arc<crate::mapper::tunnel_mapper_info::TunnelMapperTrafficInfo>,
    )
    where
        R: AsyncRead + Unpin,
        W: AsyncWrite + Unpin,
    {
        const BUFFER_SIZE: usize = 16384;
        let mut buffer = [0u8; BUFFER_SIZE];

        loop {
            match client_reader.read(&mut buffer).await {
                Ok(0) => {
                    // 客户端关闭连接
                    debug!("客户端关闭连接");
                    break;
                }
                Ok(n) => {
                    // 更新上传流量统计
                    traffic_info.add_upload_traffic(n as u128).await;

                    // 转发数据到服务器
                    if let Err(e) = server_writer.write_all(&buffer[..n]).await {
                        error!("写入服务器失败: {}", e);
                        break;
                    }
                }
                Err(e) => {
                    error!("从客户端读取数据失败: {}", e);
                    break;
                }
            }
        }

        // 关闭服务器写入流
        let _ = server_writer.shutdown().await;
    }

    /// 从代理连接创建隧道映射
    ///
    /// 为代理模式的连接创建隧道映射器，将代理流量通过隧道转发。
    /// 适用于 SOCKS5、HTTP 等代理协议。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    /// * `client_writer` - 客户端写入器
    /// * `tunnel` - 隧道连接
    ///
    /// # 返回值
    /// 成功返回 Ok(())，失败返回错误信息
    pub async fn create_from_proxy_to_tunnel(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_mapper_info: TunnelMapperInfo,
        client_writer: OwnedWriteHalf,
        tunnel: Arc<Tunnel>,
    ) -> Result<(), String> {
        let key = Self::generate_tcp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        // 创建新连接命令
        let tunnel_package = TunnelPackage {
            cmd: PackageCmd::NewConnect,
            protocol: PackageProtocol::TCP,
            source_address: Some(format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port)),
            target_address: Some(format!("{}:{}", tunnel_mapper_info.target_addr, tunnel_mapper_info.target_port)),
            data: None,
        };

        // 先创建隧道映射器
        tunnel_mapper_context.add(key.clone(), TunnelMapper {
            tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
            tunnel_mapper_context: tunnel_mapper_context.clone(),
            tunnel_mapper_type: TunnelMapperType::ProxyTunnel,
            server_writer: None,
            client_writer: Some(RwLock::new(client_writer)),
            tun_stream_writer: None,
            server_read_handler: None,
            tunnel: Some(tunnel.clone()),
            package_protocol: PackageProtocol::TCP,
            udp_socket: None,
            active_time: Arc::new(RwLock::new(Instant::now())),
            tun_writer: None,
            native_udp_created: RwLock::new(false),
        }).await;

        // 发送新连接命令到隧道
        match tunnel.write_to_tunnel(tunnel_package).await {
            Ok(_) => {
                debug!("代理隧道连接建立成功: {}", key);
                Ok(())
            }
            Err(e) => {
                error!("代理隧道连接建立失败: {} - {}", key, e);
                tunnel_mapper_context.close(&key).await;
                Err(e)
            }
        }
    }

    /// 从代理直连创建映射
    ///
    /// 为代理模式的直连创建隧道映射器，不通过隧道，直接转发代理流量。
    /// 适用于需要直接访问目标服务器的代理连接。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    /// * `client_writer` - 客户端写入器
    ///
    /// # 返回值
    /// 成功返回 Ok(())，失败返回错误信息
    pub async fn create_from_proxy_to_direct(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_mapper_info: TunnelMapperInfo,
        client_writer: OwnedWriteHalf,
    ) -> Result<(), String> {
        // 建立到目标服务器的连接
        let server_stream = Self::query_dns_and_connect(&tunnel_mapper_context, &tunnel_mapper_info).await
            .map_err(|e| format!("连接目标服务器失败: {}", e))?;

        let (server_reader, server_writer) = server_stream.into_split();
        let key = Self::generate_tcp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        // 创建活跃时间跟踪
        let active_time = Arc::new(RwLock::new(Instant::now()));
        let active_time_clone = active_time.clone();

        // 获取流量统计信息
        let traffic_info = tunnel_mapper_info.traffic_info.clone();
        let tunnel_mapper_context_clone = tunnel_mapper_context.clone();
        let key_clone = key.clone();

        // 启动服务器到客户端的数据转发任务
        let server_to_client_handle = spawn(async move {
            Self::handle_proxy_server_to_client(
                server_reader,
                client_writer,
                traffic_info,
                tunnel_mapper_context_clone,
                key_clone,
                active_time_clone,
            ).await;
        });

        // 创建隧道映射器
        tunnel_mapper_context.clone().add(key, TunnelMapper {
            tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
            tunnel_mapper_context,
            tunnel_mapper_type: TunnelMapperType::ProxyDirect,
            server_writer: Some(RwLock::new(server_writer)),
            client_writer: None,
            tun_stream_writer: None,
            server_read_handler: Some(server_to_client_handle),
            tunnel: None,
            package_protocol: PackageProtocol::TCP,
            udp_socket: None,
            active_time,
            tun_writer: None,
            native_udp_created: RwLock::new(false),
        }).await;

        Ok(())
    }

    /// 处理代理服务器到客户端的数据转发
    ///
    /// 从服务器读取数据并转发给代理客户端。
    ///
    /// # 参数
    /// * `server_reader` - 服务器读取器
    /// * `client_writer` - 客户端写入器
    /// * `traffic_info` - 流量统计信息
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `key` - 映射器键值
    /// * `active_time` - 活跃时间跟踪
    async fn handle_proxy_server_to_client(
        mut server_reader: tokio::net::tcp::OwnedReadHalf,
        mut client_writer: OwnedWriteHalf,
        traffic_info: Arc<crate::mapper::tunnel_mapper_info::TunnelMapperTrafficInfo>,
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        key: String,
        active_time: Arc<RwLock<Instant>>,
    ) {
        const BUFFER_SIZE: usize = 16384;
        let mut buffer = [0u8; BUFFER_SIZE];

        loop {
            match server_reader.read(&mut buffer).await {
                Ok(0) => {
                    // 服务器关闭连接
                    debug!("代理服务器关闭连接: {}", key);
                    break;
                }
                Ok(n) => {
                    // 更新下载流量统计和活跃时间
                    traffic_info.add_download_traffic(n as u128).await;
                    *active_time.write().await = Instant::now();

                    // 转发数据到客户端
                    if let Err(e) = client_writer.write_all(&buffer[..n]).await {
                        error!("代理写入客户端失败: {} - {}", key, e);
                        tunnel_mapper_context.close(&key).await;
                        break;
                    }
                }
                Err(e) => {
                    error!("代理从服务器读取数据失败: {} - {}", key, e);
                    break;
                }
            }
        }

        // 关闭客户端写入流
        if let Err(e) = client_writer.shutdown().await {
            error!("代理关闭客户端写入流失败: {} - {}", key, e);
        }
    }

    /// 从 TProxy UDP 直连创建映射
    ///
    /// 为 TProxy 模式的 UDP 直连创建隧道映射器，不通过隧道，直接转发 UDP 流量。
    /// TProxy 模式允许透明代理，客户端无需配置代理设置。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    pub async fn create_from_tproxy_udp_to_direct(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_mapper_info: TunnelMapperInfo,
    ) {
        let key = Self::generate_udp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        // 检查映射是否已存在
        if tunnel_mapper_context.exist(&key).await {
            return;
        }

        // 创建 UDP 套接字
        let sock = match Self::create_udp_socket(tunnel_mapper_context.clone()).await {
            Ok(sock) => sock,
            Err(e) => {
                error!("创建 UDP 套接字失败: {}", e);
                return;
            }
        };

        // 解析源地址
        let source_addr = match format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port)
            .parse::<SocketAddr>()
        {
            Ok(addr) => addr,
            Err(e) => {
                error!("解析源地址失败: {}", e);
                return;
            }
        };

        // 获取 TProxy UDP 套接字
        let tproxy_socket = match tunnel_mapper_context
            .get_tproxy_udp_socket(tunnel_mapper_info.fake_target_addr.clone(), tunnel_mapper_info.fake_target_port)
            .await
        {
            Ok(socket) => socket,
            Err(e) => {
                error!("创建 TProxy UDP 套接字失败: {}", e);
                return;
            }
        };

        // 创建活跃时间跟踪
        let active_time = Arc::new(RwLock::new(Instant::now()));
        let active_time_clone = active_time.clone();
        let traffic_info = tunnel_mapper_info.traffic_info.clone();
        let sock_clone = sock.clone();

        // 创建隧道映射器
        tunnel_mapper_context.clone().add(key, TunnelMapper {
            tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
            tunnel_mapper_context,
            tunnel_mapper_type: TunnelMapperType::TunUDPDirect, // 注意：这里使用 TunUDPDirect 类型
            server_writer: None,
            client_writer: None,
            tun_stream_writer: None,
            server_read_handler: Some(spawn(async move {
                Self::handle_tproxy_udp_direct_read(
                    sock_clone,
                    tproxy_socket,
                    source_addr,
                    traffic_info,
                    active_time_clone,
                ).await;
            })),
            tunnel: None,
            package_protocol: PackageProtocol::UDP,
            udp_socket: Some(sock),
            active_time,
            tun_writer: None,
            native_udp_created: RwLock::new(false),
        }).await;
    }

    /// 处理 TProxy UDP 直连数据读取
    ///
    /// 从 UDP 套接字读取数据并通过 TProxy 套接字转发。
    ///
    /// # 参数
    /// * `sock` - UDP 套接字
    /// * `tproxy_socket` - TProxy UDP 套接字
    /// * `source_addr` - 源地址
    /// * `traffic_info` - 流量统计信息
    /// * `active_time` - 活跃时间跟踪
    async fn handle_tproxy_udp_direct_read(
        sock: Arc<UdpSocket>,
        tproxy_socket: Arc<UdpSocket>,
        source_addr: SocketAddr,
        traffic_info: Arc<crate::mapper::tunnel_mapper_info::TunnelMapperTrafficInfo>,
        active_time: Arc<RwLock<Instant>>,
    ) {
        const BUFFER_SIZE: usize = 2048;
        let mut buffer = [0; BUFFER_SIZE];

        while let Ok((len, _src_addr)) = sock.recv_from(&mut buffer).await {
            if len == 0 {
                break;
            }

            // 更新流量统计
            traffic_info.add_download_traffic(len as u128).await;

            // 转发数据到 TProxy 套接字
            let data = buffer[..len].to_vec();
            if let Err(e) = tproxy_socket.send_to(&data, source_addr).await {
                error!("TProxy UDP 数据转发失败: {} - {}", source_addr, e);
                break;
            }

            // 更新活跃时间
            *active_time.write().await = Instant::now();
        }
    }

    /// 从 TProxy UDP 隧道创建映射
    ///
    /// 为 TProxy 模式的 UDP 隧道创建隧道映射器，将 UDP 流量通过隧道转发。
    /// TProxy 模式允许透明代理，客户端无需配置代理设置。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    /// * `tunnel` - 隧道连接
    pub async fn create_from_tproxy_udp_to_tunnel(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_mapper_info: TunnelMapperInfo,
        tunnel: Arc<Tunnel>,
    ) {
        let key = Self::generate_udp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        // 检查映射是否已存在
        if tunnel_mapper_context.exist(&key).await {
            return;
        }

        // 创建隧道映射器
        tunnel_mapper_context.clone().add(key, TunnelMapper {
            tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
            tunnel_mapper_context,
            tunnel_mapper_type: TunnelMapperType::TProxyUDPTunnel,
            server_writer: None,
            client_writer: None,
            tun_stream_writer: None,
            server_read_handler: None,
            tunnel: Some(tunnel),
            package_protocol: PackageProtocol::UDP,
            udp_socket: None,
            active_time: Arc::new(RwLock::new(Instant::now())),
            tun_writer: None,
            native_udp_created: RwLock::new(false),
        }).await;
    }

    /// 从 TProxy 原生 UDP 隧道创建映射
    ///
    /// 为 TProxy 模式的原生 UDP 连接创建隧道映射器，支持原生 UDP 协议的隧道转发。
    /// TProxy 模式允许透明代理，客户端无需配置代理设置。
    ///
    /// # 参数
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `tunnel_mapper_info` - 连接信息
    /// * `tunnel` - 隧道连接
    pub async fn create_from_tproxy_native_udp_to_tunnel(
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        tunnel_mapper_info: TunnelMapperInfo,
        tunnel: Arc<Tunnel>,
    ) {
        let source_addr_str = format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);
        let target_addr_str = format!("{}:{}", tunnel_mapper_info.target_addr, tunnel_mapper_info.target_port);
        let key = Self::generate_native_udp_key(&tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port);

        // 检查映射是否已存在
        if tunnel_mapper_context.exist(&key).await {
            return;
        }

        // 创建 UDP 套接字
        let sock = match Self::create_udp_socket(tunnel_mapper_context.clone()).await {
            Ok(sock) => sock,
            Err(e) => {
                error!("创建 UDP 套接字失败: {}", e);
                return;
            }
        };

        // 解析源地址
        let source_addr = match format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port)
            .parse::<SocketAddr>()
        {
            Ok(addr) => addr,
            Err(e) => {
                error!("解析源地址失败: {}", e);
                return;
            }
        };

        // 创建活跃时间跟踪
        let active_time = Arc::new(RwLock::new(Instant::now()));
        let active_time_clone = active_time.clone();
        let tunnel_clone = tunnel.clone();
        let sock_clone = sock.clone();
        let tunnel_mapper_context_clone = tunnel_mapper_context.clone();

        // 创建隧道映射器
        tunnel_mapper_context.clone().add(key, TunnelMapper {
            tunnel_mapper_info: RwLock::new(tunnel_mapper_info),
            tunnel_mapper_context: tunnel_mapper_context.clone(),
            tunnel_mapper_type: TunnelMapperType::TProxyNativeUDPTunnel,
            server_writer: None,
            client_writer: None,
            tun_stream_writer: None,
            server_read_handler: Some(spawn(async move {
                Self::handle_tproxy_native_udp_read(
                    sock_clone,
                    source_addr,
                    tunnel_clone,
                    tunnel_mapper_context_clone,
                    active_time_clone,
                ).await;
            })),
            tunnel: Some(tunnel.clone()),
            package_protocol: PackageProtocol::NativeUdp,
            udp_socket: Some(sock),
            active_time: Arc::new(RwLock::new(Instant::now())),
            tun_writer: None,
            native_udp_created: RwLock::new(false),
        }).await;

        // 发送新连接命令到隧道
        let tunnel_package = TunnelPackage {
            cmd: PackageCmd::NewConnect,
            protocol: PackageProtocol::NativeUdp,
            source_address: Some(source_addr_str),
            target_address: Some(target_addr_str),
            data: None,
        };

        if let Err(e) = tunnel.write_to_tunnel(tunnel_package).await {
            error!("发送新连接命令失败: {}", e);
        }
    }

    /// 处理 TProxy 原生 UDP 数据读取
    ///
    /// 从 UDP 套接字读取数据，解析隧道包格式，并通过 TProxy 套接字转发。
    ///
    /// # 参数
    /// * `sock` - UDP 套接字
    /// * `source_addr` - 源地址
    /// * `tunnel` - 隧道连接（用于流量统计）
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    /// * `active_time` - 活跃时间跟踪
    async fn handle_tproxy_native_udp_read(
        sock: Arc<UdpSocket>,
        source_addr: SocketAddr,
        tunnel: Arc<Tunnel>,
        tunnel_mapper_context: Arc<TunnelMapperContext>,
        active_time: Arc<RwLock<Instant>>,
    ) {
        const BUFFER_SIZE: usize = 2048;
        const PACKAGE_HEADER: [u8; 2] = [0x0f, 0x2f];

        let mut buf = [0; BUFFER_SIZE];
        let mut package_buf = Vec::new();

        while let Ok((len, _addr)) = sock.recv_from(&mut buf).await {
            if len == 0 {
                break;
            }

            // 更新隧道流量统计
            tunnel.add_download_traffic(len as i64).await;

            // 处理包数据
            if len > 2 && buf[..2] == PACKAGE_HEADER {
                package_buf.clear();
            }
            package_buf.extend_from_slice(&buf[..len]);

            // 尝试解析隧道包
            match TunnelPackage::from_byte_array(&mut package_buf) {
                Ok(Some(package)) => {
                    if let Err(e) = Self::process_tproxy_native_udp_package(
                        package,
                        &source_addr,
                        &tunnel_mapper_context,
                    ).await {
                        error!("处理 TProxy 原生 UDP 包失败: {}", e);
                        break;
                    }
                }
                Ok(None) => {
                    // 包不完整，继续读取
                    continue;
                }
                Err(e) => {
                    error!("解析隧道包失败: {}", e);
                }
            }

            // 更新活跃时间
            *active_time.write().await = Instant::now();
        }
    }

    /// 处理 TProxy 原生 UDP 隧道包
    ///
    /// 解析隧道包并通过 TProxy 套接字转发数据。
    ///
    /// # 参数
    /// * `package` - 隧道包
    /// * `source_addr` - 源地址
    /// * `tunnel_mapper_context` - 隧道映射器上下文
    ///
    /// # 返回值
    /// 成功返回 Ok(())，失败返回错误信息
    async fn process_tproxy_native_udp_package(
        package: TunnelPackage,
        source_addr: &SocketAddr,
        tunnel_mapper_context: &Arc<TunnelMapperContext>,
    ) -> Result<(), String> {
        let package_source_addr = package.source_address
            .ok_or("包中缺少源地址")?
            .parse::<SocketAddr>()
            .map_err(|e| format!("解析包源地址失败: {}", e))?;

        let data = package.data
            .ok_or("包中缺少数据")?;

        // 获取 TProxy UDP 套接字
        let udp_socket = tunnel_mapper_context
            .get_tproxy_udp_socket(package_source_addr.ip().to_string(), package_source_addr.port())
            .await
            .map_err(|e| format!("创建 TProxy UDP 套接字失败: {}", e))?;

        // 发送数据
        udp_socket.send_to(&data, source_addr).await
            .map_err(|e| format!("发送数据到 TProxy 套接字失败: {}", e))?;

        Ok(())
    }

    /// 向服务器写入数据
    ///
    /// 根据隧道映射器类型，将数据写入到相应的目标。
    /// 支持直连模式和隧道模式的数据传输。
    ///
    /// # 参数
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    pub async fn write_to_server(&self, data: &[u8]) -> bool {
        // 更新上传流量统计
        self.tunnel_mapper_info.read().await.traffic_info
            .add_upload_traffic(data.len() as u128).await;

        match self.tunnel_mapper_type {
            // 直连模式：直接写入服务器连接
            TunnelMapperType::ProxyDirect | TunnelMapperType::TunTcpDirect => {
                if let Some(server_writer) = self.server_writer.as_ref() {
                    server_writer.write().await.write_all(data).await.is_ok()
                } else {
                    false
                }
            }

            // 隧道模式：通过隧道发送数据包
            TunnelMapperType::ProxyTunnel | TunnelMapperType::TunTcpTunnel => {
                self.send_data_through_tunnel(data).await
            }

            // UDP 相关类型：这些类型不使用此方法
            TunnelMapperType::TunNativeUDPTunnel | TunnelMapperType::Socks5UDPTunnel |
            TunnelMapperType::TunUDPTunnel | TunnelMapperType::TunUDPDirect |
            TunnelMapperType::TProxyUDPTunnel | TunnelMapperType::TProxyNativeUDPTunnel => {
                true
            }
        }
    }

    /// 通过隧道发送数据
    ///
    /// 将数据封装为隧道包并通过隧道发送。
    ///
    /// # 参数
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    async fn send_data_through_tunnel(&self, data: &[u8]) -> bool {
        let info = self.tunnel_mapper_info.read().await;
        let tunnel_package = TunnelPackage {
            cmd: PackageCmd::TData,
            protocol: self.package_protocol,
            source_address: Some(format!("{}:{}", info.source_addr, info.source_port)),
            target_address: Some(format!("{}:{}", info.target_addr, info.target_port)),
            data: Some(data.to_vec()),
        };
        drop(info);

        if let Some(tunnel) = self.tunnel.as_ref() {
            tunnel.write_to_tunnel(tunnel_package).await.is_ok()
        } else {
            false
        }
    }
    /// 向服务器写入 UDP 数据
    ///
    /// 根据隧道映射器类型，将 UDP 数据发送到相应的目标。
    /// 支持直连、隧道、原生 UDP 等多种传输模式。
    ///
    /// # 参数
    /// * `target_addr` - 目标地址
    /// * `target_port` - 目标端口
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    pub async fn write_udp_to_server(&self, target_addr: String, target_port: u16, data: &[u8]) -> bool {
        // 更新活跃时间和流量统计
        {
            *self.active_time.write().await = Instant::now();
            self.tunnel_mapper_info.read().await.traffic_info.add_upload_traffic(data.len() as u128).await;
        }

        match self.tunnel_mapper_type {
            // TCP 相关类型不处理 UDP 数据
            TunnelMapperType::ProxyDirect | TunnelMapperType::TunTcpDirect |
            TunnelMapperType::ProxyTunnel | TunnelMapperType::TunTcpTunnel => {
                true
            }

            // TUN UDP 直连模式
            TunnelMapperType::TunUDPDirect => {
                self.handle_tun_udp_direct(&target_addr, target_port, data).await;
                true
            }

            // 原生 UDP 隧道模式
            TunnelMapperType::TunNativeUDPTunnel | TunnelMapperType::TProxyNativeUDPTunnel => {
                self.handle_native_udp_tunnel(&target_addr, target_port, data).await;
                true
            }

            // 标准 UDP 隧道模式
            TunnelMapperType::Socks5UDPTunnel | TunnelMapperType::TunUDPTunnel | TunnelMapperType::TProxyUDPTunnel => {
                self.handle_standard_udp_tunnel(&target_addr, target_port, data).await
            }
        }
    }

    /// 处理 TUN UDP 直连
    ///
    /// 将 UDP 数据直接发送到目标服务器，支持 Fake IP 到真实 IP 的转换。
    ///
    /// # 参数
    /// * `target_addr` - 目标地址
    /// * `target_port` - 目标端口
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    async fn handle_tun_udp_direct(&self, target_addr: &str, target_port: u16, data: &[u8]) {
        let Some(sock) = self.udp_socket.as_ref() else {
            return;
        };

        // 获取接口选择器（如果在 TUN 模式下）
        let interface_selector = if self.tunnel_mapper_context.get_tun_mode_enable().await {
            Some(self.tunnel_mapper_context.get_local_interface_selector().await)
        } else {
            None
        };

        // 如果是 Fake IP，则转换为真实 IP
        let resolved_addr = match self.tunnel_mapper_context
            .fake_ip_cache_context
            .query_ip(interface_selector, target_addr)
            .await
        {
            Ok(ip_addresses) => {
                if let Some(ip_addr) = ip_addresses.first() {
                    ip_addr.to_string()
                } else {
                    error!("DNS 解析返回空结果: {}", target_addr);
                    return;
                }
            }
            Err(e) => {
                error!("DNS 解析失败: {} - {}", target_addr, e);
                return;
            }
        };

        // 发送数据到目标地址
        if let Ok(socket_addr) = format!("{}:{}", resolved_addr, target_port).parse::<SocketAddr>() {
            if let Err(e) = sock.send_to(data, socket_addr).await {
                error!("UDP 直连发送失败: {} - {}", socket_addr, e);
            }
        } else {
            error!("无效的套接字地址: {}:{}", resolved_addr, target_port);
        }
    }

    /// 处理原生 UDP 隧道
    ///
    /// 等待原生 UDP 连接建立后，将数据封装为隧道包发送。
    ///
    /// # 参数
    /// * `target_addr` - 目标地址
    /// * `target_port` - 目标端口
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    async fn handle_native_udp_tunnel(&self, target_addr: &str, target_port: u16, data: &[u8]) {
        let Some(sock) = self.udp_socket.as_ref() else {
            return;
        };

        // 等待原生 UDP 连接建立（最多等待 1 秒）
        const MAX_RETRIES: u8 = 10;
        const RETRY_INTERVAL: Duration = Duration::from_millis(100);

        for _ in 0..MAX_RETRIES {
            if *self.native_udp_created.read().await {
                self.send_native_udp_data(sock, target_addr, target_port, data).await;
                return;
            }
            sleep(RETRY_INTERVAL).await;
        }

        error!("原生 UDP 连接未建立，超时等待");
    }

    /// 发送原生 UDP 数据
    ///
    /// 将数据封装为原生 UDP 隧道包并发送到服务器。
    ///
    /// # 参数
    /// * `sock` - UDP 套接字
    /// * `target_addr` - 目标地址
    /// * `target_port` - 目标端口
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    async fn send_native_udp_data(&self, sock: &UdpSocket, target_addr: &str, target_port: u16, data: &[u8]) {
        // 获取服务器地址
        let server_addr = {
            let info = self.tunnel_mapper_info.read().await;
            format!("{}:{}", info.target_addr, info.target_port)
        };

        let Ok(socket_addr) = server_addr.parse::<SocketAddr>() else {
            error!("无效的服务器地址: {}", server_addr);
            return;
        };

        // 更新隧道流量统计
        if let Some(tunnel) = self.tunnel.as_ref() {
            tunnel.add_upload_traffic(data.len() as i64).await;
        }

        // 创建原生 UDP 隧道包
        let mut send_data = Vec::new();
        TunnelPackage::new(
            PackageCmd::TData,
            PackageProtocol::NativeUdp,
            None,
            Some(format!("{}:{}", target_addr, target_port)),
            Some(data.to_vec()),
        ).to_byte_array(&mut send_data);

        // 发送数据
        match sock.send_to(&send_data, socket_addr).await {
            Ok(_) => {},
            Err(e) => {
                error!("原生 UDP 数据发送失败: {} - {}", socket_addr, e);
            }
        }
    }

    /// 处理标准 UDP 隧道
    ///
    /// 将 UDP 数据封装为标准隧道包并通过隧道发送。
    ///
    /// # 参数
    /// * `target_addr` - 目标地址
    /// * `target_port` - 目标端口
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    async fn handle_standard_udp_tunnel(&self, target_addr: &str, target_port: u16, data: &[u8]) -> bool {
        let Some(tunnel) = self.tunnel.as_ref() else {
            return false;
        };

        // 获取源地址信息
        let (source_addr, source_port) = {
            let info = self.tunnel_mapper_info.read().await;
            (info.source_addr.clone(), info.source_port)
        };

        // 创建隧道包
        let tunnel_package = TunnelPackage {
            cmd: PackageCmd::TData,
            protocol: self.package_protocol,
            source_address: Some(format!("{}:{}", source_addr, source_port)),
            target_address: Some(format!("{}:{}", target_addr, target_port)),
            data: Some(data.to_vec()),
        };

        // 发送到隧道
        tunnel.write_to_tunnel(tunnel_package).await.is_ok()
    }

    /// 向客户端写入数据
    ///
    /// 根据隧道映射器类型，将从隧道接收的数据转发给客户端。
    /// 支持多种客户端类型：代理、TUN、SOCKS5 等。
    ///
    /// # 参数
    /// * `tunnel_package` - 从隧道接收的数据包
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    pub async fn write_to_client(&self, tunnel_package: TunnelPackage) -> bool {
        // 更新活跃时间和流量统计
        let data = {
            *self.active_time.write().await = Instant::now();
            if let Some(data) = tunnel_package.data.clone() {
                self.tunnel_mapper_info.read().await.traffic_info.add_download_traffic(data.len() as u128).await;
                Some(data)
            } else {
                None
            }
        };

        match self.tunnel_mapper_type {
            // 代理直连模式：不需要处理数据
            TunnelMapperType::ProxyDirect => true,

            // 原生 UDP 隧道模式：更新服务器地址信息
            TunnelMapperType::TunNativeUDPTunnel | TunnelMapperType::TProxyNativeUDPTunnel => {
                // 修改目标地址为 Agent 服务器地址
                if let Some(target_addr) = tunnel_package.target_address {
                    if let Some((addr, port)) = AddressUtil::split_address_port(&target_addr) {
                        let mut guard = self.tunnel_mapper_info.write().await;
                        guard.target_addr = addr;
                        guard.target_port = port;
                        *self.native_udp_created.write().await = true;
                    }
                }
                true
            }
            // 代理隧道模式：写入客户端连接
            TunnelMapperType::ProxyTunnel => {
                if let Some(client_writer) = self.client_writer.as_ref() {
                    if let Some(data) = data {
                        client_writer.write().await.write_all(&data).await.is_ok()
                    } else {
                        true
                    }
                } else {
                    false
                }
            }

            // TUN TCP 隧道模式：写入 TUN 流
            TunnelMapperType::TunTcpTunnel => {
                if let Some(stream_writer) = self.tun_stream_writer.as_ref() {
                    if let Some(data) = data {
                        stream_writer.write().await.write_all(&data).await.is_ok()
                    } else {
                        true
                    }
                } else {
                    false
                }
            }
            // SOCKS5 UDP 隧道模式：封装 SOCKS5 格式并发送
            TunnelMapperType::Socks5UDPTunnel => {
                if let (Some(udp_socket), Some(target_address), Some(data)) =
                    (self.udp_socket.as_ref(), tunnel_package.target_address, data)
                {
                    if let Ok(socket_addr) = target_address.parse::<SocketAddr>() {
                        if let IpAddr::V4(ipv4) = socket_addr.ip() {
                            // 封装 SOCKS5 UDP 格式
                            let mut socks5_packet = vec![0x00, 0x00, 0x00, 0x01]; // RSV + FRAG + ATYP
                            socks5_packet.extend(ipv4.octets()); // IPv4 地址
                            socks5_packet.extend(&socket_addr.port().to_be_bytes()); // 端口
                            socks5_packet.extend(data); // 数据

                            // 发送到源地址
                            let source_info = self.tunnel_mapper_info.read().await;
                            let source_addr = format!("{}:{}", source_info.source_addr, source_info.source_port);
                            drop(source_info);

                            let _ = udp_socket.send_to(&socks5_packet, source_addr).await;
                        }
                        // IPv6 暂不支持
                    }
                }
                true
            }

            // TUN TCP 直连模式：不需要处理数据
            TunnelMapperType::TunTcpDirect => true,

            // TUN UDP 直连模式：不需要处理数据
            TunnelMapperType::TunUDPDirect => true,
            // TUN UDP 隧道模式：发送到 TUN 接口
            TunnelMapperType::TunUDPTunnel => {
                self.handle_tun_udp_client(&tunnel_package, data).await
            }

            // TProxy UDP 隧道模式：通过 TProxy 套接字发送
            TunnelMapperType::TProxyUDPTunnel => {
                self.handle_tproxy_udp_client(&tunnel_package, data).await
            }
        }
    }

    /// 处理 TUN UDP 客户端数据发送
    ///
    /// 将数据通过 TUN 接口发送到客户端。
    ///
    /// # 参数
    /// * `tunnel_package` - 隧道包
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    async fn handle_tun_udp_client(&self, tunnel_package: &TunnelPackage, data: Option<Vec<u8>>) -> bool {
        let Some(tun_writer) = self.tun_writer.as_ref() else {
            return true; // 没有写入器时返回 true
        };

        let Some(data) = data else {
            return true; // 没有数据时返回 true
        };

        let Some(target_address) = &tunnel_package.target_address else {
            return false; // 没有目标地址时返回 false
        };

        // 解析目标地址和源地址
        let target_addr = match target_address.parse::<SocketAddr>() {
            Ok(addr) => addr,
            Err(e) => {
                error!("解析目标地址失败: {} - {}", target_address, e);
                return false;
            }
        };

        let source_info = self.tunnel_mapper_info.read().await;
        let source_addr_str = format!("{}:{}", source_info.source_addr, source_info.source_port);
        drop(source_info);

        let source_addr = match source_addr_str.parse::<SocketAddr>() {
            Ok(addr) => addr,
            Err(e) => {
                error!("解析源地址失败: {} - {}", source_addr_str, e);
                return false;
            }
        };

        // 发送数据到 TUN 接口
        if tun_writer.send_to(&data, &target_addr, &source_addr).is_err() {
            error!("发送数据到 TUN 接口失败: {} -> {}", source_addr, target_addr);
            return false;
        }

        true
    }

    /// 处理 TProxy UDP 客户端数据发送
    ///
    /// 通过 TProxy UDP 套接字将数据发送到客户端。
    ///
    /// # 参数
    /// * `tunnel_package` - 隧道包
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    async fn handle_tproxy_udp_client(&self, tunnel_package: &TunnelPackage, data: Option<Vec<u8>>) -> bool {
        let Some(data) = data else {
            return true; // 没有数据时返回 true
        };

        let Some(target_address) = &tunnel_package.target_address else {
            return false; // 没有目标地址时返回 false
        };

        // 解析目标地址和源地址
        let target_addr = match target_address.parse::<SocketAddr>() {
            Ok(addr) => addr,
            Err(e) => {
                error!("解析目标地址失败: {} - {}", target_address, e);
                return false;
            }
        };

        let source_info = self.tunnel_mapper_info.read().await;
        let source_addr_str = format!("{}:{}", source_info.source_addr, source_info.source_port);
        drop(source_info);

        let source_addr = match source_addr_str.parse::<SocketAddr>() {
            Ok(addr) => addr,
            Err(e) => {
                error!("解析源地址失败: {} - {}", source_addr_str, e);
                return false;
            }
        };

        // 获取 TProxy UDP 套接字
        let udp_socket = match self.tunnel_mapper_context
            .get_tproxy_udp_socket(target_addr.ip().to_string(), target_addr.port())
            .await
        {
            Ok(socket) => socket,
            Err(e) => {
                error!("创建 TProxy UDP 套接字失败: {} - {}", target_addr, e);
                return false;
            }
        };

        // 发送数据
        if let Err(e) = udp_socket.send_to(&data, &source_addr).await {
            error!("TProxy UDP 数据发送失败: {} -> {} - {}", target_addr, source_addr, e);
            return false;
        }

        true
    }

    /// 关闭隧道映射器
    ///
    /// 清理所有相关资源，包括网络连接、任务句柄等。
    /// 对于隧道连接，会发送关闭连接的命令。
    pub async fn close(&self) {
        // 对于隧道连接，发送关闭命令
        match self.tunnel_mapper_type {
            TunnelMapperType::ProxyTunnel | TunnelMapperType::TunTcpTunnel => {
                self.send_close_command().await;
            }
            // 其他类型不需要发送关闭命令
            TunnelMapperType::ProxyDirect | TunnelMapperType::TunTcpDirect |
            TunnelMapperType::TunUDPDirect | TunnelMapperType::Socks5UDPTunnel |
            TunnelMapperType::TunUDPTunnel | TunnelMapperType::TunNativeUDPTunnel |
            TunnelMapperType::TProxyUDPTunnel | TunnelMapperType::TProxyNativeUDPTunnel => {}
        }

        // 关闭所有写入器
        self.shutdown_writers().await;

        // 终止读取处理任务
        if let Some(server_read_handler) = self.server_read_handler.as_ref() {
            server_read_handler.abort();
        }
    }

    /// 发送关闭连接命令
    async fn send_close_command(&self) {
        let info = self.tunnel_mapper_info.read().await;
        let source_address = format!("{}:{}", info.source_addr, info.source_port);
        drop(info);

        let tunnel_package = TunnelPackage {
            cmd: PackageCmd::CloseConnect,
            protocol: PackageProtocol::TCP,
            source_address: Some(source_address),
            target_address: None,
            data: None,
        };

        if let Some(tunnel) = self.tunnel.as_ref() {
            let _ = tunnel.write_to_tunnel(tunnel_package).await;
        }
    }

    /// 关闭所有写入器
    async fn shutdown_writers(&self) {
        if let Some(client_writer) = self.client_writer.as_ref() {
            let _ = client_writer.write().await.shutdown().await;
        }

        if let Some(server_writer) = self.server_writer.as_ref() {
            let _ = server_writer.write().await.shutdown().await;
        }

        if let Some(tun_stream_writer) = self.tun_stream_writer.as_ref() {
            let _ = tun_stream_writer.write().await.shutdown().await;
        }
    }

    /// 检查隧道映射器是否超时
    ///
    /// 根据映射器类型判断是否超时。TCP 连接不会超时，
    /// UDP 连接在 30 秒无活动后被认为超时。
    ///
    /// # 返回值
    /// 如果超时返回 true，否则返回 false
    pub async fn is_timeout(&self) -> bool {
        const UDP_TIMEOUT_SECONDS: u64 = 30;

        match self.tunnel_mapper_type {
            // TCP 连接类型不会超时，由连接本身的生命周期管理
            TunnelMapperType::ProxyDirect | TunnelMapperType::ProxyTunnel |
            TunnelMapperType::TunTcpDirect | TunnelMapperType::TunTcpTunnel => {
                false
            }

            // UDP 连接类型根据活跃时间判断是否超时
            TunnelMapperType::Socks5UDPTunnel | TunnelMapperType::TunUDPTunnel |
            TunnelMapperType::TunUDPDirect | TunnelMapperType::TunNativeUDPTunnel |
            TunnelMapperType::TProxyUDPTunnel | TunnelMapperType::TProxyNativeUDPTunnel => {
                let now = Instant::now();
                let last_active = *self.active_time.read().await;
                now.duration_since(last_active) > Duration::from_secs(UDP_TIMEOUT_SECONDS)
            }
        }
    }
}