//! # TProxy UDP 缓存模块
//!
//! 本模块提供 TProxy UDP 连接的缓存管理功能，用于优化 UDP 连接的性能和资源利用率。

use std::sync::Arc;
use std::time::Duration;
use tokio::net::UdpSocket;
use tokio::sync::RwLock;
use tokio::time::Instant;

/// TProxy UDP 缓存结构
///
/// 用于缓存 UDP 套接字连接，避免频繁创建和销毁套接字，提高性能。
/// 每个缓存项都有一个活跃时间戳，用于超时管理。
pub struct TProxyUdpCache {
    /// UDP 套接字的共享引用
    pub udp_socket: Arc<UdpSocket>,
    /// 最后活跃时间，用于超时检测
    active_time: Arc<RwLock<Instant>>,
}

impl TProxyUdpCache {
    /// 创建新的 TProxy UDP 缓存实例
    ///
    /// # 参数
    /// * `udp_socket` - 要缓存的 UDP 套接字
    ///
    /// # 返回值
    /// 返回新创建的缓存实例
    pub fn new(udp_socket: Arc<UdpSocket>) -> Self {
        TProxyUdpCache {
            udp_socket,
            active_time: Arc::new(RwLock::new(Instant::now())),
        }
    }

    /// 重置活跃时间
    ///
    /// 当套接字被使用时调用此方法，更新最后活跃时间为当前时间。
    /// 这样可以防止活跃的连接被误判为超时。
    pub async fn reset_time(&self) {
        *self.active_time.write().await = Instant::now();
    }

    /// 检查缓存是否超时
    ///
    /// 检查自最后活跃时间以来是否已经超过了超时阈值（120秒）。
    /// 超时的缓存项将被清理以释放资源。
    ///
    /// # 返回值
    /// * `true` - 如果缓存已超时
    /// * `false` - 如果缓存仍然有效
    pub async fn is_timeout(&self) -> bool {
        const TIMEOUT_DURATION: Duration = Duration::from_secs(120);
        let now = Instant::now();
        now.duration_since(*self.active_time.read().await) > TIMEOUT_DURATION
    }

    /// 获取缓存的存活时间
    ///
    /// 返回从创建或最后重置时间到现在的持续时间。
    ///
    /// # 返回值
    /// 返回存活时间的 Duration
    pub async fn get_alive_duration(&self) -> Duration {
        let now = Instant::now();
        now.duration_since(*self.active_time.read().await)
    }
}
