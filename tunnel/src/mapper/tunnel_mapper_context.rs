//! # 隧道映射器上下文模块
//!
//! 本模块提供隧道映射器的上下文管理功能，包括映射器生命周期管理、
//! 网络配置、规则匹配和资源清理等核心功能。

use flyshadow_common::interface::interface_selector::InterfaceSelector;
use flyshadow_common::tunnel::tunnel_package::TunnelPackage;
use socket2::{Domain, Protocol, Socket, Type};
use std::collections::HashMap;
use std::net::{IpAddr, SocketAddrV4, SocketAddrV6};
use std::sync::Arc;
use std::time::Duration;
use tokio::net::UdpSocket;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time::sleep;

use crate::context::rule_matcher::DomainRuleMatcher;
use crate::fake_ip::fake_ip_context::FakeIpCacheContext;
use crate::mapper::tproxy_udp_cache::TProxyUdpCache;
use crate::mapper::tunnel_mapper::TunnelMapper;
use crate::mapper::tunnel_mapper_info::{TunnelMapperInfo, TunnelMapperInfoSerialize};

/// 隧道映射器上下文
///
/// 管理所有隧道映射器的生命周期，提供网络配置、规则匹配、
/// Fake IP 缓存和 TProxy UDP 缓存等功能。
pub struct TunnelMapperContext {
    /// 隧道映射器存储映射表
    map: Arc<RwLock<HashMap<String, Arc<TunnelMapper>>>>,
    /// 域名规则匹配器
    pub domain_rule_matcher: DomainRuleMatcher,
    /// Fake IP 缓存上下文
    pub fake_ip_cache_context: FakeIpCacheContext,
    /// 本地网络接口选择器
    interface_selector: RwLock<InterfaceSelector>,
    /// TUN 模式是否启用
    tun_mode_enable: RwLock<bool>,
    /// IPv6 是否启用
    ipv6_enable: RwLock<bool>,
    /// 是否使用内置 DNS
    use_build_in_dns: RwLock<bool>,
    /// TProxy UDP 缓存映射表
    tproxy_udp_map: Arc<RwLock<HashMap<String, TProxyUdpCache>>>,
    /// 映射器超时清理任务句柄
    mapper_timeout_job: JoinHandle<()>,
}

impl TunnelMapperContext {
    /// 获取 TProxy UDP 套接字
    ///
    /// 从缓存中获取或创建新的 TProxy UDP 套接字。
    /// 如果缓存中存在，则重置其活跃时间；否则创建新的套接字并加入缓存。
    ///
    /// # 参数
    /// * `ip` - 绑定的 IP 地址
    /// * `port` - 绑定的端口号
    ///
    /// # 返回值
    /// 返回 UDP 套接字的共享引用
    ///
    /// # 错误
    /// 如果套接字创建失败，返回相应的错误
    pub async fn get_tproxy_udp_socket(&self, ip: String, port: u16) -> anyhow::Result<Arc<UdpSocket>> {
        let addr = format!("{}:{}", ip, port);

        // 首先尝试从缓存中获取
        if let Some(tproxy_udp_cache) = self.tproxy_udp_map.read().await.get(&addr) {
            tproxy_udp_cache.reset_time().await;
            return Ok(tproxy_udp_cache.udp_socket.clone());
        }

        // 缓存中不存在，创建新的套接字
        let udp_socket = self.create_udp_socket(ip, port).await?;

        // 加入缓存
        self.tproxy_udp_map.write().await.insert(addr, TProxyUdpCache::new(udp_socket.clone()));

        Ok(udp_socket)
    }

    /// 创建 UDP 套接字
    ///
    /// 根据 IP 地址类型创建相应的 UDP 套接字，并设置必要的套接字选项。
    /// 在 Linux 系统上会设置地址重用和透明代理选项。
    ///
    /// # 参数
    /// * `ip` - 要绑定的 IP 地址字符串
    /// * `port` - 要绑定的端口号
    ///
    /// # 返回值
    /// 返回创建的 UDP 套接字的共享引用
    ///
    /// # 错误
    /// 如果 IP 地址解析失败或套接字创建失败，返回相应的错误
    async fn create_udp_socket(&self, ip: String, port: u16) -> anyhow::Result<Arc<UdpSocket>> {
        match ip.parse()? {
            IpAddr::V4(ip) => {
                // 创建 IPv4 UDP 套接字
                let socket = Socket::new(Domain::IPV4, Type::DGRAM, Some(Protocol::UDP))?;

                #[cfg(any(target_os = "linux"))]
                {
                    // 在 Linux 上设置套接字选项
                    socket.set_reuse_address(true)?;
                    socket.set_ip_transparent(true)?;
                }

                let addr = SocketAddrV4::new(ip, port);
                socket.bind(&addr.into())?;

                let udp_socket = UdpSocket::from_std(socket.into())?;
                Ok(Arc::new(udp_socket))
            }
            IpAddr::V6(ip) => {
                // 创建 IPv6 UDP 套接字
                let socket = Socket::new(Domain::IPV6, Type::DGRAM, Some(Protocol::UDP))?;

                #[cfg(any(target_os = "linux"))]
                {
                    use std::os::fd::AsRawFd;
                    use libc::setsockopt;

                    socket.set_reuse_address(true)?;

                    // 设置 IPv6 透明代理选项
                    unsafe {
                        let optval: libc::c_int = 1;
                        setsockopt(
                            socket.as_raw_fd(),
                            libc::IPPROTO_IPV6,
                            libc::IPV6_TRANSPARENT,
                            &optval as *const _ as *const _,
                            core::mem::size_of_val(&optval) as u32,
                        );
                    }
                }

                let addr = SocketAddrV6::new(ip, port, 0, 0);
                socket.bind(&addr.into())?;

                let udp_socket = UdpSocket::from_std(socket.into())?;
                Ok(Arc::new(udp_socket))
            }
        }
    }
}

impl TunnelMapperContext {
    /// 创建新的隧道映射器上下文
    ///
    /// 初始化所有组件并启动后台清理任务。
    /// 清理任务每 10 秒运行一次，清理超时的映射器和 UDP 缓存。
    ///
    /// # 返回值
    /// 返回新创建的上下文实例
    pub fn new() -> Self {
        let map = Arc::new(RwLock::new(HashMap::new()));
        let tproxy_udp_map = Arc::new(RwLock::new(HashMap::new()));

        TunnelMapperContext {
            map: map.clone(),
            domain_rule_matcher: DomainRuleMatcher::new(),
            fake_ip_cache_context: FakeIpCacheContext::new(),
            interface_selector: RwLock::new(InterfaceSelector::default()),
            tun_mode_enable: RwLock::new(false),
            ipv6_enable: RwLock::new(false),
            use_build_in_dns: RwLock::new(true),
            tproxy_udp_map: tproxy_udp_map.clone(),
            mapper_timeout_job: spawn(Self::cleanup_task(map.clone(), tproxy_udp_map.clone())),
        }
    }

    /// 后台清理任务
    ///
    /// 定期清理超时的隧道映射器和 UDP 缓存，释放系统资源。
    ///
    /// # 参数
    /// * `map` - 隧道映射器映射表的共享引用
    /// * `tproxy_udp_map` - TProxy UDP 缓存映射表的共享引用
    async fn cleanup_task(
        map: Arc<RwLock<HashMap<String, Arc<TunnelMapper>>>>,
        tproxy_udp_map: Arc<RwLock<HashMap<String, TProxyUdpCache>>>,
    ) {
        const CLEANUP_INTERVAL: Duration = Duration::from_secs(10);

        loop {
            sleep(CLEANUP_INTERVAL).await;

            // 清理超时的隧道映射器
            {
                let mut write_guard = map.write().await;
                let mut expired_mappers: Vec<String> = Vec::new();

                for (key, mapper) in write_guard.iter() {
                    if mapper.is_timeout().await {
                        expired_mappers.push(key.clone());
                    }
                }

                for key in expired_mappers {
                    if let Some(tunnel_mapper) = write_guard.remove(&key) {
                        tunnel_mapper.close().await;
                    }
                }
            }

            // 清理超时的 UDP 缓存
            {
                let mut write_guard = tproxy_udp_map.write().await;
                let mut expired_caches: Vec<String> = Vec::new();

                for (key, cache) in write_guard.iter() {
                    if cache.is_timeout().await {
                        expired_caches.push(key.clone());
                    }
                }

                for key in expired_caches {
                    write_guard.remove(&key);
                }
            }
        }
    }

    /// 获取 TUN 模式启用状态
    pub async fn get_tun_mode_enable(&self) -> bool {
        *self.tun_mode_enable.read().await
    }

    /// 获取本地网络接口选择器
    pub async fn get_local_interface_selector(&self) -> InterfaceSelector {
        self.interface_selector.read().await.clone()
    }

    /// 设置 TUN 模式启用状态
    ///
    /// # 参数
    /// * `enable` - 是否启用 TUN 模式
    pub async fn set_tun_mode_enable(&self, enable: bool) {
        *self.tun_mode_enable.write().await = enable;
    }

    /// 设置 IPv6 启用状态
    ///
    /// # 参数
    /// * `enable` - 是否启用 IPv6
    pub async fn set_ipv6_enable(&self, enable: bool) {
        *self.ipv6_enable.write().await = enable;
    }

    /// 获取 IPv6 启用状态
    pub async fn get_ipv6_enable(&self) -> bool {
        *self.ipv6_enable.read().await
    }

    /// 设置本地网络接口选择器
    ///
    /// # 参数
    /// * `interface_selector` - 网络接口选择器配置
    pub async fn set_local_interface(&self, interface_selector: InterfaceSelector) {
        *self.interface_selector.write().await = interface_selector;
    }

    /// 设置是否使用内置 DNS
    ///
    /// # 参数
    /// * `enable` - 是否使用内置 DNS
    pub async fn set_use_build_in_dns(&self, enable: bool) {
        *self.use_build_in_dns.write().await = enable;
    }

    /// 获取是否使用内置 DNS
    pub async fn get_use_build_in_dns(&self) -> bool {
        *self.use_build_in_dns.read().await
    }

    /// 关闭指定的隧道映射器
    ///
    /// 从映射表中移除并关闭指定的隧道映射器，释放相关资源。
    ///
    /// # 参数
    /// * `key` - 隧道映射器的唯一标识符
    pub async fn close(&self, key: &String) {
        if let Some(tunnel_mapper) = self.map.write().await.remove(key) {
            log::debug!("关闭连接: {}", key);
            tunnel_mapper.close().await;
        }
    }

    /// 向客户端写入数据
    ///
    /// 通过指定的隧道映射器向客户端发送隧道包数据。
    ///
    /// # 参数
    /// * `key` - 隧道映射器的唯一标识符
    /// * `tunnel_package` - 要发送的隧道包
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    pub async fn write_to_client(&self, key: &String, tunnel_package: TunnelPackage) -> bool {
        if let Some(tunnel_mapper) = self.get_tunnel_mapper(key).await {
            tunnel_mapper.write_to_client(tunnel_package).await
        } else {
            false
        }
    }

    /// 向服务器写入数据
    ///
    /// 通过指定的隧道映射器向服务器发送数据。
    ///
    /// # 参数
    /// * `key` - 隧道映射器的唯一标识符
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    pub async fn write_to_server(&self, key: &String, data: &[u8]) -> bool {
        if let Some(tunnel_mapper) = self.get_tunnel_mapper(key).await {
            tunnel_mapper.write_to_server(data).await
        } else {
            false
        }
    }

    /// 向服务器写入 UDP 数据
    ///
    /// 通过指定的隧道映射器向服务器发送 UDP 数据。
    ///
    /// # 参数
    /// * `key` - 隧道映射器的唯一标识符
    /// * `target_addr` - 目标地址
    /// * `target_port` - 目标端口
    /// * `data` - 要发送的数据
    ///
    /// # 返回值
    /// 成功返回 true，失败返回 false
    pub async fn write_udp_to_server(&self, key: &String, target_addr: String, target_port: u16, data: &[u8]) -> bool {
        if let Some(tunnel_mapper) = self.get_tunnel_mapper(key).await {
            tunnel_mapper.write_udp_to_server(target_addr, target_port, data).await
        } else {
            false
        }
    }

    /// 获取隧道映射器
    ///
    /// 根据键值从映射表中获取隧道映射器的共享引用。
    ///
    /// # 参数
    /// * `key` - 隧道映射器的唯一标识符
    ///
    /// # 返回值
    /// 如果找到返回映射器的共享引用，否则返回 None
    async fn get_tunnel_mapper(&self, key: &String) -> Option<Arc<TunnelMapper>> {
        self.map.read().await.get(key).cloned()
    }

    /// 获取隧道映射器信息的 JSON 字符串
    ///
    /// 收集所有活跃的隧道映射器信息，序列化为 JSON 格式。
    /// 同时重置流量统计信息。
    ///
    /// # 返回值
    /// 返回包含所有映射器信息的 JSON 字符串
    pub async fn get_tunnel_mapper_info_json(&self) -> String {
        let mut tunnel_mapper_info_arr = Vec::new();

        for tunnel_mapper in self.map.read().await.values() {
            let tunnel_mapper_info = tunnel_mapper.tunnel_mapper_info.read().await;
            tunnel_mapper_info_arr.push(
                TunnelMapperInfoSerialize::from(tunnel_mapper_info.clone()).await
            );
            // 重置流量统计
            tunnel_mapper_info.traffic_info.reset().await;
        }

        serde_json::to_string(&tunnel_mapper_info_arr)
            .unwrap_or_else(|_| "[]".to_string())
    }

    /// 获取 Fake IP 和匹配规则结果
    ///
    /// 处理 Fake IP 到真实域名的转换，并应用域名匹配规则。
    ///
    /// # 参数
    /// * `tunnel_mapper_info` - 要处理的隧道映射器信息
    pub async fn get_fake_ip_match_result(&self, tunnel_mapper_info: &mut TunnelMapperInfo) {
        // 保存原始目标地址作为伪造地址
        tunnel_mapper_info.fake_target_addr = tunnel_mapper_info.target_addr.clone();
        tunnel_mapper_info.fake_target_port = tunnel_mapper_info.target_port;

        // 如果目标地址是 Fake IP，则转换为真实域名
        if let Some(fake_ip_domain) = self.fake_ip_cache_context
            .get_domain_by_fake_ip(&tunnel_mapper_info.target_addr).await
        {
            tunnel_mapper_info.target_addr = fake_ip_domain;
        }

        // 应用域名匹配规则
        self.domain_rule_matcher.match_domain(tunnel_mapper_info).await;
    }

    /// 添加隧道映射器
    ///
    /// 将新的隧道映射器添加到映射表中。如果键已存在，则关闭旧的映射器。
    ///
    /// # 参数
    /// * `key` - 隧道映射器的唯一标识符
    /// * `tunnel_mapper` - 要添加的隧道映射器
    pub async fn add(&self, key: String, tunnel_mapper: TunnelMapper) {
        if let Some(old) = self.map.write().await.insert(key, Arc::new(tunnel_mapper)) {
            old.close().await;
        }
    }

    /// 检查隧道映射器是否存在
    ///
    /// # 参数
    /// * `key` - 隧道映射器的唯一标识符
    ///
    /// # 返回值
    /// 如果存在返回 true，否则返回 false
    pub async fn exist(&self, key: &String) -> bool {
        self.map.read().await.contains_key(key)
    }

    /// 清理所有隧道映射器和缓存
    ///
    /// 关闭所有活跃的隧道映射器并清空所有缓存。
    /// 通常在程序关闭时调用。
    pub async fn clear(&self) {
        // 关闭所有隧道映射器
        let mut write_guard = self.map.write().await;
        for (_, tunnel_mapper) in write_guard.drain() {
            tunnel_mapper.close().await;
        }
        drop(write_guard);

        // 清空 UDP 缓存
        self.tproxy_udp_map.write().await.clear();
    }
}