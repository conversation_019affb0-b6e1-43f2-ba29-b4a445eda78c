//! # 隧道连接器测试模块
//!
//! 本模块包含隧道连接器的单元测试和集成测试，
//! 验证重构后代码的正确性和稳定性。

#[cfg(test)]
mod tests {
    use super::super::config::{presets, TunnelConnectorConfig, TunnelConnectorError};
    use super::super::utils::{address_utils, connection_utils, crypto_utils, time_utils};
    use std::time::Duration;

    // ==================== 配置测试 ====================

    #[test]
    fn test_default_config() {
        let config = TunnelConnectorConfig::default();
        assert_eq!(config.ping_interval_seconds, 50);
        assert_eq!(config.reconnect_interval_seconds, 5);
        assert_eq!(config.default_ws_host, "www.baidu.com");
        assert_eq!(config.connection_timeout_seconds, 30);
    }

    #[test]
    fn test_config_builder_pattern() {
        let config = TunnelConnectorConfig::new()
            .with_ping_interval(30)
            .with_reconnect_interval(10)
            .with_default_ws_host("custom.host.com".to_string())
            .with_connection_timeout(60);

        assert_eq!(config.ping_interval_seconds, 30);
        assert_eq!(config.reconnect_interval_seconds, 10);
        assert_eq!(config.default_ws_host, "custom.host.com");
        assert_eq!(config.connection_timeout_seconds, 60);
    }

    #[test]
    fn test_config_validation() {
        let mut config = TunnelConnectorConfig::default();
        assert!(config.validate().is_ok());

        // 测试无效的心跳间隔
        config.ping_interval_seconds = 0;
        assert!(config.validate().is_err());

        // 重置并测试无效的重连间隔
        config = TunnelConnectorConfig::default();
        config.reconnect_interval_seconds = 0;
        assert!(config.validate().is_err());

        // 重置并测试空的默认主机
        config = TunnelConnectorConfig::default();
        config.default_ws_host = String::new();
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_config_duration_methods() {
        let config = TunnelConnectorConfig::new()
            .with_ping_interval(30)
            .with_reconnect_interval(10)
            .with_connection_timeout(60);

        assert_eq!(config.ping_interval(), Duration::from_secs(30));
        assert_eq!(config.reconnect_interval(), Duration::from_secs(10));
        assert_eq!(config.connection_timeout(), Duration::from_secs(60));
    }

    #[test]
    fn test_config_presets() {
        let fast = presets::fast_connection();
        assert_eq!(fast.ping_interval_seconds, 30);
        assert_eq!(fast.reconnect_interval_seconds, 3);
        assert_eq!(fast.connection_timeout_seconds, 15);

        let stable = presets::stable_connection();
        assert_eq!(stable.ping_interval_seconds, 60);
        assert_eq!(stable.reconnect_interval_seconds, 10);
        assert_eq!(stable.connection_timeout_seconds, 45);

        let low_power = presets::low_power();
        assert_eq!(low_power.ping_interval_seconds, 120);
        assert_eq!(low_power.reconnect_interval_seconds, 30);
        assert_eq!(low_power.connection_timeout_seconds, 60);
    }

    // ==================== 工具函数测试 ====================

    #[test]
    fn test_parse_host_config() {
        // 测试单个主机
        let (host, ws_host) = address_utils::parse_host_config("example.com", "default.com");
        assert_eq!(host, "example.com");
        assert_eq!(ws_host, "default.com");

        // 测试主机和 WebSocket 主机
        let (host, ws_host) = address_utils::parse_host_config("example.com:ws.example.com", "default.com");
        assert_eq!(host, "example.com");
        assert_eq!(ws_host, "ws.example.com");

        // 测试复杂的 WebSocket 主机
        let (host, ws_host) = address_utils::parse_host_config("example.com:ws1.example.com:ws2.example.com", "default.com");
        assert_eq!(host, "example.com");
        assert_eq!(ws_host, "ws1.example.com:ws2.example.com");

        // 测试空字符串
        let (host, ws_host) = address_utils::parse_host_config("", "default.com");
        assert_eq!(host, "default.com");
        assert_eq!(ws_host, "default.com");
    }

    #[test]
    fn test_validate_ip_addresses() {
        use std::net::IpAddr;

        // 测试有效的 IP 地址列表
        let valid_ips = vec![
            "127.0.0.1".parse::<IpAddr>().unwrap(),
            "::1".parse::<IpAddr>().unwrap(),
        ];
        assert!(address_utils::validate_ip_addresses(&valid_ips).is_ok());

        // 测试空的 IP 地址列表
        let empty_ips: Vec<IpAddr> = vec![];
        assert!(address_utils::validate_ip_addresses(&empty_ips).is_err());
    }

    #[test]
    fn test_connection_validation() {
        // 测试端口验证
        assert!(connection_utils::validate_port(80).is_ok());
        assert!(connection_utils::validate_port(65535).is_ok());
        assert!(connection_utils::validate_port(0).is_err());

        // 测试主机验证
        assert!(connection_utils::validate_host("example.com").is_ok());
        assert!(connection_utils::validate_host("127.0.0.1").is_ok());
        assert!(connection_utils::validate_host("").is_err());
        assert!(connection_utils::validate_host("   ").is_err());

        // 测试密码验证
        assert!(connection_utils::validate_password("password123").is_ok());
        assert!(connection_utils::validate_password("").is_err());
    }

    #[test]
    fn test_crypto_utils() {
        // 测试 MD5 密码生成
        let password = "test_password";
        let md5_hash = crypto_utils::generate_md5_password(password);

        assert_eq!(md5_hash.len(), 32);
        assert!(md5_hash.chars().all(|c| c.is_ascii_hexdigit()));

        // 相同密码应该生成相同的哈希
        let md5_hash2 = crypto_utils::generate_md5_password(password);
        assert_eq!(md5_hash, md5_hash2);

        // 测试 MD5 密码验证
        assert!(crypto_utils::validate_md5_password(&md5_hash).is_ok());
        assert!(crypto_utils::validate_md5_password("invalid").is_err());
        assert!(crypto_utils::validate_md5_password("098f6bcd4621d373cade4e832627b4g6").is_err()); // 包含非十六进制字符
        assert!(crypto_utils::validate_md5_password("098f6bcd4621d373cade4e832627b4f").is_err()); // 长度不足
    }

    #[test]
    fn test_time_utils() {
        // 测试时间戳获取
        let timestamp_millis = time_utils::current_timestamp_millis();
        assert!(timestamp_millis.is_ok());

        let timestamp_secs = time_utils::current_timestamp_secs();
        assert!(timestamp_secs.is_ok());

        // 测试时间差计算
        assert_eq!(time_utils::time_diff_millis(100, 200), 100);
        assert_eq!(time_utils::time_diff_millis(200, 100), 0); // saturating_sub 应该返回 0
        assert_eq!(time_utils::time_diff_millis(100, 100), 0);
    }

    // ==================== 错误处理测试 ====================

    #[test]
    fn test_error_types() {
        let connection_error = TunnelConnectorError::Connection("连接失败".to_string());
        assert_eq!(connection_error.to_string(), "连接错误: 连接失败");

        let network_error = TunnelConnectorError::Network("网络不可达".to_string());
        assert_eq!(network_error.to_string(), "网络错误: 网络不可达");

        let protocol_error = TunnelConnectorError::Protocol("协议错误".to_string());
        assert_eq!(protocol_error.to_string(), "协议错误: 协议错误");
    }

    #[test]
    fn test_error_conversion() {
        let error = TunnelConnectorError::Configuration("配置错误".to_string());
        let error_string: String = error.into();
        assert_eq!(error_string, "配置错误: 配置错误");
    }

    // ==================== 集成测试辅助函数 ====================

    /// 创建测试用的配置
    fn create_test_config() -> TunnelConnectorConfig {
        TunnelConnectorConfig::new()
            .with_ping_interval(10)
            .with_reconnect_interval(2)
            .with_connection_timeout(15)
    }

    #[test]
    fn test_integration_config_creation() {
        let config = create_test_config();
        assert!(config.validate().is_ok());
        assert_eq!(config.ping_interval_seconds, 10);
        assert_eq!(config.reconnect_interval_seconds, 2);
        assert_eq!(config.connection_timeout_seconds, 15);
    }
}

// ==================== 基准测试 ====================

#[cfg(test)]
mod benchmarks {
    use super::super::utils::crypto_utils;
    use std::time::Instant;

    #[test]
    fn benchmark_md5_generation() {
        let password = "test_password_for_benchmark";
        let iterations = 1000;

        let start = Instant::now();
        for _ in 0..iterations {
            let _ = crypto_utils::generate_md5_password(password);
        }
        let duration = start.elapsed();

        println!("MD5 生成 {} 次耗时: {:?}", iterations, duration);
        println!("平均每次耗时: {:?}", duration / iterations);

        // 确保性能在合理范围内（每次操作应该少于 1ms）
        assert!(duration.as_millis() < iterations as u128);
    }
}
