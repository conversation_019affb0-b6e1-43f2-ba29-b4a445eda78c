//! # 隧道连接器配置模块
//!
//! 本模块定义了隧道连接器的配置选项和错误类型，
//! 提供了灵活的配置管理和统一的错误处理。

use std::time::Duration;

/// 隧道连接器配置
///
/// 包含隧道连接器运行时的各种配置参数，
/// 可以根据不同的使用场景进行调整。
#[derive(Debug, Clone)]
pub struct TunnelConnectorConfig {
    /// 心跳检测间隔时间（秒）
    ///
    /// 定期发送 PING 命令以维持连接活跃状态的间隔时间。
    /// 较小的值可以更快地检测到连接断开，但会增加网络开销。
    pub ping_interval_seconds: u64,

    /// 连接重试间隔时间（秒）
    ///
    /// 当连接断开后，自动重连的等待时间。
    /// 较小的值可以更快地恢复连接，但可能对服务器造成压力。
    pub reconnect_interval_seconds: u64,

    /// 默认 WebSocket 主机
    ///
    /// 当主机配置中没有指定 WebSocket 主机时使用的默认值。
    pub default_ws_host: String,

    /// 连接超时时间（秒）
    ///
    /// TCP 连接建立的超时时间。
    pub connection_timeout_seconds: u64,

    /// 最大重连次数
    ///
    /// 连续重连失败的最大次数，超过后将停止重连。
    /// 设置为 0 表示无限重连。
    pub max_reconnect_attempts: u32,

    /// DNS 查询超时时间（秒）
    ///
    /// DNS 解析的超时时间。
    pub dns_timeout_seconds: u64,

    /// WebSocket 握手超时时间（秒）
    ///
    /// WebSocket 握手过程的超时时间。
    pub websocket_timeout_seconds: u64,
}

impl Default for TunnelConnectorConfig {
    fn default() -> Self {
        Self {
            ping_interval_seconds: 50,
            reconnect_interval_seconds: 5,
            default_ws_host: "www.baidu.com".to_string(),
            connection_timeout_seconds: 30,
            max_reconnect_attempts: 0, // 无限重连
            dns_timeout_seconds: 10,
            websocket_timeout_seconds: 15,
        }
    }
}

impl TunnelConnectorConfig {
    /// 创建新的配置实例
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置心跳间隔
    pub fn with_ping_interval(mut self, seconds: u64) -> Self {
        self.ping_interval_seconds = seconds;
        self
    }

    /// 设置重连间隔
    pub fn with_reconnect_interval(mut self, seconds: u64) -> Self {
        self.reconnect_interval_seconds = seconds;
        self
    }

    /// 设置默认 WebSocket 主机
    pub fn with_default_ws_host(mut self, host: String) -> Self {
        self.default_ws_host = host;
        self
    }

    /// 设置连接超时
    pub fn with_connection_timeout(mut self, seconds: u64) -> Self {
        self.connection_timeout_seconds = seconds;
        self
    }

    /// 设置最大重连次数
    pub fn with_max_reconnect_attempts(mut self, attempts: u32) -> Self {
        self.max_reconnect_attempts = attempts;
        self
    }

    /// 设置 DNS 超时
    pub fn with_dns_timeout(mut self, seconds: u64) -> Self {
        self.dns_timeout_seconds = seconds;
        self
    }

    /// 设置 WebSocket 超时
    pub fn with_websocket_timeout(mut self, seconds: u64) -> Self {
        self.websocket_timeout_seconds = seconds;
        self
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> Result<(), TunnelConnectorError> {
        if self.ping_interval_seconds == 0 {
            return Err(TunnelConnectorError::Configuration(
                "心跳间隔不能为0".to_string(),
            ));
        }

        if self.reconnect_interval_seconds == 0 {
            return Err(TunnelConnectorError::Configuration(
                "重连间隔不能为0".to_string(),
            ));
        }

        if self.default_ws_host.is_empty() {
            return Err(TunnelConnectorError::Configuration(
                "默认WebSocket主机不能为空".to_string(),
            ));
        }

        if self.connection_timeout_seconds == 0 {
            return Err(TunnelConnectorError::Configuration(
                "连接超时不能为0".to_string(),
            ));
        }

        if self.dns_timeout_seconds == 0 {
            return Err(TunnelConnectorError::Configuration(
                "DNS超时不能为0".to_string(),
            ));
        }

        if self.websocket_timeout_seconds == 0 {
            return Err(TunnelConnectorError::Configuration(
                "WebSocket超时不能为0".to_string(),
            ));
        }

        Ok(())
    }

    /// 获取心跳间隔的 Duration
    pub fn ping_interval(&self) -> Duration {
        Duration::from_secs(self.ping_interval_seconds)
    }

    /// 获取重连间隔的 Duration
    pub fn reconnect_interval(&self) -> Duration {
        Duration::from_secs(self.reconnect_interval_seconds)
    }

    /// 获取连接超时的 Duration
    pub fn connection_timeout(&self) -> Duration {
        Duration::from_secs(self.connection_timeout_seconds)
    }

    /// 获取 DNS 超时的 Duration
    pub fn dns_timeout(&self) -> Duration {
        Duration::from_secs(self.dns_timeout_seconds)
    }

    /// 获取 WebSocket 超时的 Duration
    pub fn websocket_timeout(&self) -> Duration {
        Duration::from_secs(self.websocket_timeout_seconds)
    }
}

/// 隧道连接器错误类型
///
/// 定义了隧道连接器可能遇到的各种错误情况，
/// 提供了详细的错误分类和描述。
#[derive(Debug, thiserror::Error)]
pub enum TunnelConnectorError {
    /// 连接相关错误
    #[error("连接错误: {0}")]
    Connection(String),

    /// 认证相关错误
    #[error("认证错误: {0}")]
    Authentication(String),

    /// 网络相关错误
    #[error("网络错误: {0}")]
    Network(String),

    /// 协议相关错误
    #[error("协议错误: {0}")]
    Protocol(String),

    /// 配置相关错误
    #[error("配置错误: {0}")]
    Configuration(String),

    /// 系统相关错误
    #[error("系统错误: {0}")]
    System(String),

    /// 超时错误
    #[error("超时错误: {0}")]
    Timeout(String),

    /// 加密/解密错误
    #[error("加密错误: {0}")]
    Encryption(String),
}

impl From<TunnelConnectorError> for String {
    fn from(error: TunnelConnectorError) -> Self {
        error.to_string()
    }
}

/// 隧道连接器结果类型
pub type TunnelResult<T> = Result<T, TunnelConnectorError>;

/// 预定义的配置模板
pub mod presets {
    use super::TunnelConnectorConfig;

    /// 快速连接配置
    ///
    /// 适用于需要快速响应的场景，使用较短的间隔时间。
    pub fn fast_connection() -> TunnelConnectorConfig {
        TunnelConnectorConfig::new()
            .with_ping_interval(30)
            .with_reconnect_interval(3)
            .with_connection_timeout(15)
    }

    /// 稳定连接配置
    ///
    /// 适用于网络环境不稳定的场景，使用较长的间隔时间。
    pub fn stable_connection() -> TunnelConnectorConfig {
        TunnelConnectorConfig::new()
            .with_ping_interval(60)
            .with_reconnect_interval(10)
            .with_connection_timeout(45)
    }

    /// 低功耗配置
    ///
    /// 适用于移动设备或需要节省电量的场景。
    pub fn low_power() -> TunnelConnectorConfig {
        TunnelConnectorConfig::new()
            .with_ping_interval(120)
            .with_reconnect_interval(30)
            .with_connection_timeout(60)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = TunnelConnectorConfig::default();
        assert_eq!(config.ping_interval_seconds, 50);
        assert_eq!(config.reconnect_interval_seconds, 5);
        assert_eq!(config.default_ws_host, "www.baidu.com");
    }

    #[test]
    fn test_config_builder() {
        let config = TunnelConnectorConfig::new()
            .with_ping_interval(30)
            .with_reconnect_interval(10);

        assert_eq!(config.ping_interval_seconds, 30);
        assert_eq!(config.reconnect_interval_seconds, 10);
    }

    #[test]
    fn test_config_validation() {
        let mut config = TunnelConnectorConfig::default();
        assert!(config.validate().is_ok());

        config.ping_interval_seconds = 0;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_presets() {
        let fast = presets::fast_connection();
        assert_eq!(fast.ping_interval_seconds, 30);

        let stable = presets::stable_connection();
        assert_eq!(stable.ping_interval_seconds, 60);

        let low_power = presets::low_power();
        assert_eq!(low_power.ping_interval_seconds, 120);
    }
}
