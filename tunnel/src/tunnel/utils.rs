//! # 隧道连接器工具模块
//!
//! 本模块提供了隧道连接器使用的通用工具函数，
//! 包括时间处理、地址解析、连接管理等功能。

use crate::tunnel::config::{TunnelConnectorError, TunnelResult};
use std::net::{IpAddr, SocketAddr};
use std::time::{SystemTime, UNIX_EPOCH};

/// 时间工具
pub mod time_utils {
    use super::*;

    /// 获取当前时间戳（毫秒）
    ///
    /// # 返回值
    /// 返回自 Unix 纪元以来的毫秒数
    ///
    /// # 错误
    /// 如果系统时间早于 Unix 纪元，返回系统错误
    pub fn current_timestamp_millis() -> TunnelResult<u128> {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map(|duration| duration.as_millis())
            .map_err(|e| TunnelConnectorError::System(format!("获取系统时间失败: {}", e)))
    }

    /// 获取当前时间戳（秒）
    ///
    /// # 返回值
    /// 返回自 Unix 纪元以来的秒数
    ///
    /// # 错误
    /// 如果系统时间早于 Unix 纪元，返回系统错误
    pub fn current_timestamp_secs() -> TunnelResult<u64> {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .map(|duration| duration.as_secs())
            .map_err(|e| TunnelConnectorError::System(format!("获取系统时间失败: {}", e)))
    }

    /// 计算时间差（毫秒）
    ///
    /// # 参数
    /// * `start_time` - 开始时间戳（毫秒）
    /// * `end_time` - 结束时间戳（毫秒）
    ///
    /// # 返回值
    /// 返回时间差（毫秒），如果结束时间早于开始时间则返回 0
    pub fn time_diff_millis(start_time: u128, end_time: u128) -> u128 {
        end_time.saturating_sub(start_time)
    }
}

/// 地址解析工具
pub mod address_utils {
    use super::*;

    /// 解析主机配置字符串
    ///
    /// 支持以下格式：
    /// - `host` -> (host, default_ws_host)
    /// - `host:ws_host` -> (host, ws_host)
    /// - `host:ws_host1:ws_host2` -> (host, ws_host1:ws_host2)
    ///
    /// # 参数
    /// * `host_config` - 主机配置字符串
    /// * `default_ws_host` - 默认的 WebSocket 主机
    ///
    /// # 返回值
    /// 返回 (实际主机地址, WebSocket主机) 元组
    pub fn parse_host_config(host_config: &str, default_ws_host: &str) -> (String, String) {
        let host_parts: Vec<String> = host_config
            .split(':')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect();

        if host_parts.len() >= 2 {
            (host_parts[0].clone(), host_parts[1..].join(":"))
        } else if !host_parts.is_empty() {
            (host_parts[0].clone(), default_ws_host.to_string())
        } else {
            (default_ws_host.to_string(), default_ws_host.to_string())
        }
    }

    /// 验证 IP 地址列表是否为空
    ///
    /// # 参数
    /// * `ip_addresses` - IP 地址列表
    ///
    /// # 返回值
    /// 如果列表为空，返回网络错误；否则返回 Ok
    pub fn validate_ip_addresses(ip_addresses: &[IpAddr]) -> TunnelResult<()> {
        if ip_addresses.is_empty() {
            Err(TunnelConnectorError::Network("没有可用的IP地址".to_string()))
        } else {
            Ok(())
        }
    }

    /// 格式化套接字地址用于日志输出
    ///
    /// # 参数
    /// * `addr` - 套接字地址
    ///
    /// # 返回值
    /// 返回格式化后的地址字符串
    pub fn format_socket_addr(addr: &SocketAddr) -> String {
        match addr {
            SocketAddr::V4(v4) => format!("{}:{}", v4.ip(), v4.port()),
            SocketAddr::V6(v6) => format!("[{}]:{}", v6.ip(), v6.port()),
        }
    }
}

/// 连接工具
pub mod connection_utils {
    use super::*;
    use std::time::Duration;

    /// 创建连接延迟
    ///
    /// 为了避免同时发起多个连接，为后续连接添加延迟。
    ///
    /// # 参数
    /// * `is_first` - 是否是第一个连接
    /// * `delay_seconds` - 延迟秒数
    ///
    /// # 返回值
    /// 返回延迟的 Duration，如果是第一个连接则返回零延迟
    pub fn connection_delay(is_first: bool, delay_seconds: u64) -> Duration {
        if is_first {
            Duration::from_secs(0)
        } else {
            Duration::from_secs(delay_seconds)
        }
    }

    /// 验证端口号
    ///
    /// # 参数
    /// * `port` - 端口号
    ///
    /// # 返回值
    /// 如果端口号有效返回 Ok，否则返回配置错误
    pub fn validate_port(port: u16) -> TunnelResult<()> {
        if port == 0 {
            Err(TunnelConnectorError::Configuration("端口号不能为0".to_string()))
        } else {
            Ok(())
        }
    }

    /// 验证主机名
    ///
    /// # 参数
    /// * `host` - 主机名
    ///
    /// # 返回值
    /// 如果主机名有效返回 Ok，否则返回配置错误
    pub fn validate_host(host: &str) -> TunnelResult<()> {
        if host.trim().is_empty() {
            Err(TunnelConnectorError::Configuration("主机名不能为空".to_string()))
        } else {
            Ok(())
        }
    }

    /// 验证密码
    ///
    /// # 参数
    /// * `password` - 密码
    ///
    /// # 返回值
    /// 如果密码有效返回 Ok，否则返回配置错误
    pub fn validate_password(password: &str) -> TunnelResult<()> {
        if password.is_empty() {
            Err(TunnelConnectorError::Configuration("密码不能为空".to_string()))
        } else {
            Ok(())
        }
    }
}

/// 加密工具
pub mod crypto_utils {
    use super::*;

    /// 生成 MD5 密码哈希
    ///
    /// # 参数
    /// * `password` - 原始密码
    ///
    /// # 返回值
    /// 返回 MD5 哈希的十六进制字符串
    pub fn generate_md5_password(password: &str) -> String {
        let md5_hash = md5::compute(password.as_bytes());
        format!("{:x}", md5_hash)
    }

    /// 验证 MD5 密码格式
    ///
    /// # 参数
    /// * `md5_password` - MD5 密码字符串
    ///
    /// # 返回值
    /// 如果格式有效返回 Ok，否则返回配置错误
    pub fn validate_md5_password(md5_password: &str) -> TunnelResult<()> {
        if md5_password.len() != 32 {
            return Err(TunnelConnectorError::Configuration(
                "MD5密码长度必须为32个字符".to_string(),
            ));
        }

        if !md5_password.chars().all(|c| c.is_ascii_hexdigit()) {
            return Err(TunnelConnectorError::Configuration(
                "MD5密码必须只包含十六进制字符".to_string(),
            ));
        }

        Ok(())
    }
}

/// 日志工具
pub mod log_utils {
    use log::{debug, error, info, warn};

    /// 记录连接开始日志
    pub fn log_connection_start(host: &str, port: u16) {
        info!("开始连接隧道服务器: {}:{}", host, port);
    }

    /// 记录连接成功日志
    pub fn log_connection_success(remote_addr: &std::net::SocketAddr) {
        info!("隧道连接建立成功，远程地址: {}", remote_addr);
    }

    /// 记录连接失败日志
    pub fn log_connection_failure(error: &str) {
        error!("连接失败: {}", error);
    }

    /// 记录任务启动日志
    pub fn log_task_start(task_name: &str) {
        info!("{}任务已启动", task_name);
    }

    /// 记录任务结束日志
    pub fn log_task_end(task_name: &str) {
        info!("{}任务已结束", task_name);
    }

    /// 记录心跳延迟日志
    pub fn log_ping_delay(delay_ms: u128) {
        info!("隧道延迟: {}ms", delay_ms);
    }

    /// 记录流量统计日志
    pub fn log_traffic_stats(upload: i64, download: i64) {
        debug!("流量统计 - 上传: {} 字节, 下载: {} 字节", upload, download);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_host_config() {
        let (host, ws_host) = address_utils::parse_host_config("example.com", "default.com");
        assert_eq!(host, "example.com");
        assert_eq!(ws_host, "default.com");

        let (host, ws_host) = address_utils::parse_host_config("example.com:ws.example.com", "default.com");
        assert_eq!(host, "example.com");
        assert_eq!(ws_host, "ws.example.com");
    }

    #[test]
    fn test_validate_port() {
        assert!(connection_utils::validate_port(80).is_ok());
        assert!(connection_utils::validate_port(0).is_err());
    }

    #[test]
    fn test_validate_host() {
        assert!(connection_utils::validate_host("example.com").is_ok());
        assert!(connection_utils::validate_host("").is_err());
        assert!(connection_utils::validate_host("   ").is_err());
    }

    #[test]
    fn test_generate_md5_password() {
        let md5 = crypto_utils::generate_md5_password("test");
        assert_eq!(md5.len(), 32);
        assert!(md5.chars().all(|c| c.is_ascii_hexdigit()));
    }

    #[test]
    fn test_validate_md5_password() {
        let valid_md5 = "098f6bcd4621d373cade4e832627b4f6"; // MD5 of "test"
        assert!(crypto_utils::validate_md5_password(valid_md5).is_ok());

        assert!(crypto_utils::validate_md5_password("invalid").is_err());
        assert!(crypto_utils::validate_md5_password("098f6bcd4621d373cade4e832627b4g6").is_err()); // contains 'g'
    }

    #[test]
    fn test_time_diff() {
        assert_eq!(time_utils::time_diff_millis(100, 200), 100);
        assert_eq!(time_utils::time_diff_millis(200, 100), 0); // saturating_sub
    }
}
