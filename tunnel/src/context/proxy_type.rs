use serde_derive::{Deserialize, Serialize};

/// 代理类型索引常量定义
///
/// 这些常量用于与外部系统（如配置文件、API接口）进行交互时的类型转换
pub mod proxy_type_index {
    /// 直连模式索引
    pub const DIRECT: i32 = 0;
    /// 拒绝连接索引
    pub const REJECT: i32 = 1;
    /// 代理模式索引
    pub const PROXY: i32 = 2;
    /// 规则模式索引
    pub const RULE: i32 = 3;
}

/// 代理类型枚举
///
/// 定义了系统支持的四种代理处理方式，用于控制网络连接的路由策略
#[derive(PartialEq, Eq, Clone, Debug, Serialize, Deserialize, Hash)]
pub enum ProxyType {
    /// 直连模式
    ///
    /// 网络请求直接连接到目标服务器，不经过任何代理服务器。
    /// 适用于访问本地网络或不需要代理的服务。
    Direct,

    /// 代理模式
    ///
    /// 所有网络请求都通过配置的代理服务器进行转发。
    /// 适用于需要通过代理访问外部网络的场景。
    Proxy,

    /// 规则模式
    ///
    /// 根据预设的规则（如域名、IP地址等）自动选择是直连还是代理。
    /// 这是最智能的模式，可以根据不同的目标自动选择最优的连接方式。
    Rule,

    /// 拒绝连接
    ///
    /// 直接拒绝网络连接请求，不进行任何处理。
    /// 适用于屏蔽特定网站或服务的场景。
    Reject,
}

impl ProxyType {
    /// 从整数索引创建代理类型
    ///
    /// # 参数
    /// * `index` - 代理类型的整数索引
    ///
    /// # 返回值
    /// 对应的 `ProxyType` 枚举值，无效索引默认返回 `Direct`
    ///
    /// # 索引映射
    /// * `0` -> `Direct` - 直连模式
    /// * `1` -> `Reject` - 拒绝连接
    /// * `2` -> `Proxy` - 代理模式
    /// * `3` -> `Rule` - 规则模式
    /// * 其他 -> `Direct` - 默认直连模式
    ///
    /// # 示例
    /// ```rust
    /// use tunnel::context::proxy_type::ProxyType;
    ///
    /// let proxy_type = ProxyType::from_index(2);
    /// assert_eq!(proxy_type, ProxyType::Proxy);
    /// ```
    pub fn from_index(index: i32) -> Self {
        match index {
            proxy_type_index::DIRECT => Self::Direct,
            proxy_type_index::REJECT => Self::Reject,
            proxy_type_index::PROXY => Self::Proxy,
            proxy_type_index::RULE => Self::Rule,
            _ => {
                log::warn!("无效的代理类型索引: {}，使用默认的直连模式", index);
                Self::Direct
            }
        }
    }

    /// 将代理类型转换为整数索引
    ///
    /// # 返回值
    /// 对应的整数索引值
    ///
    /// # 示例
    /// ```rust
    /// use tunnel::context::proxy_type::ProxyType;
    ///
    /// let index = ProxyType::Proxy.to_index();
    /// assert_eq!(index, 2);
    /// ```
    pub fn to_index(&self) -> i32 {
        match self {
            Self::Direct => proxy_type_index::DIRECT,
            Self::Reject => proxy_type_index::REJECT,
            Self::Proxy => proxy_type_index::PROXY,
            Self::Rule => proxy_type_index::RULE,
        }
    }

    /// 获取代理类型的中文描述
    ///
    /// # 返回值
    /// 代理类型的中文描述字符串
    ///
    /// # 示例
    /// ```rust
    /// use tunnel::context::proxy_type::ProxyType;
    ///
    /// let description = ProxyType::Rule.description();
    /// assert_eq!(description, "规则模式");
    /// ```
    pub fn description(&self) -> &'static str {
        match self {
            Self::Direct => "直连模式",
            Self::Reject => "拒绝连接",
            Self::Proxy => "代理模式",
            Self::Rule => "规则模式",
        }
    }

    /// 获取代理类型的英文名称
    ///
    /// # 返回值
    /// 代理类型的英文名称字符串
    ///
    /// # 示例
    /// ```rust
    /// use tunnel::context::proxy_type::ProxyType;
    ///
    /// let name = ProxyType::Direct.name();
    /// assert_eq!(name, "Direct");
    /// ```
    pub fn name(&self) -> &'static str {
        match self {
            Self::Direct => "Direct",
            Self::Reject => "Reject",
            Self::Proxy => "Proxy",
            Self::Rule => "Rule",
        }
    }

    /// 检查是否为需要代理服务器的类型
    ///
    /// # 返回值
    /// * `true` - 需要代理服务器（Proxy 或 Rule 模式）
    /// * `false` - 不需要代理服务器（Direct 或 Reject 模式）
    ///
    /// # 示例
    /// ```rust
    /// use tunnel::context::proxy_type::ProxyType;
    ///
    /// assert!(ProxyType::Proxy.requires_proxy_server());
    /// assert!(!ProxyType::Direct.requires_proxy_server());
    /// ```
    pub fn requires_proxy_server(&self) -> bool {
        matches!(self, Self::Proxy | Self::Rule)
    }

    /// 检查是否允许网络连接
    ///
    /// # 返回值
    /// * `true` - 允许连接（Direct、Proxy、Rule 模式）
    /// * `false` - 拒绝连接（Reject 模式）
    ///
    /// # 示例
    /// ```rust
    /// use tunnel::context::proxy_type::ProxyType;
    ///
    /// assert!(ProxyType::Direct.allows_connection());
    /// assert!(!ProxyType::Reject.allows_connection());
    /// ```
    pub fn allows_connection(&self) -> bool {
        !matches!(self, Self::Reject)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试从索引创建代理类型
    #[test]
    fn test_from_index() {
        // 测试有效索引
        assert_eq!(ProxyType::from_index(0), ProxyType::Direct);
        assert_eq!(ProxyType::from_index(1), ProxyType::Reject);
        assert_eq!(ProxyType::from_index(2), ProxyType::Proxy);
        assert_eq!(ProxyType::from_index(3), ProxyType::Rule);

        // 测试无效索引，应该返回默认的Direct
        assert_eq!(ProxyType::from_index(-1), ProxyType::Direct);
        assert_eq!(ProxyType::from_index(4), ProxyType::Direct);
        assert_eq!(ProxyType::from_index(100), ProxyType::Direct);
    }

    /// 测试将代理类型转换为索引
    #[test]
    fn test_to_index() {
        assert_eq!(ProxyType::Direct.to_index(), 0);
        assert_eq!(ProxyType::Reject.to_index(), 1);
        assert_eq!(ProxyType::Proxy.to_index(), 2);
        assert_eq!(ProxyType::Rule.to_index(), 3);
    }

    /// 测试索引转换的往返一致性
    #[test]
    fn test_index_roundtrip() {
        let types = vec![
            ProxyType::Direct,
            ProxyType::Reject,
            ProxyType::Proxy,
            ProxyType::Rule,
        ];

        for proxy_type in types {
            let index = proxy_type.to_index();
            let converted_back = ProxyType::from_index(index);
            assert_eq!(proxy_type, converted_back);
        }
    }

    /// 测试获取中文描述
    #[test]
    fn test_description() {
        assert_eq!(ProxyType::Direct.description(), "直连模式");
        assert_eq!(ProxyType::Reject.description(), "拒绝连接");
        assert_eq!(ProxyType::Proxy.description(), "代理模式");
        assert_eq!(ProxyType::Rule.description(), "规则模式");
    }

    /// 测试获取英文名称
    #[test]
    fn test_name() {
        assert_eq!(ProxyType::Direct.name(), "Direct");
        assert_eq!(ProxyType::Reject.name(), "Reject");
        assert_eq!(ProxyType::Proxy.name(), "Proxy");
        assert_eq!(ProxyType::Rule.name(), "Rule");
    }

    /// 测试是否需要代理服务器
    #[test]
    fn test_requires_proxy_server() {
        assert!(!ProxyType::Direct.requires_proxy_server());
        assert!(!ProxyType::Reject.requires_proxy_server());
        assert!(ProxyType::Proxy.requires_proxy_server());
        assert!(ProxyType::Rule.requires_proxy_server());
    }

    /// 测试是否允许连接
    #[test]
    fn test_allows_connection() {
        assert!(ProxyType::Direct.allows_connection());
        assert!(!ProxyType::Reject.allows_connection());
        assert!(ProxyType::Proxy.allows_connection());
        assert!(ProxyType::Rule.allows_connection());
    }

    /// 测试序列化和反序列化
    #[test]
    fn test_serialization() {
        let proxy_type = ProxyType::Proxy;

        // 测试序列化
        let serialized = serde_json::to_string(&proxy_type).unwrap();
        assert_eq!(serialized, "\"Proxy\"");

        // 测试反序列化
        let deserialized: ProxyType = serde_json::from_str(&serialized).unwrap();
        assert_eq!(deserialized, proxy_type);
    }

    /// 测试所有枚举变体的序列化
    #[test]
    fn test_all_variants_serialization() {
        let variants = vec![
            (ProxyType::Direct, "\"Direct\""),
            (ProxyType::Reject, "\"Reject\""),
            (ProxyType::Proxy, "\"Proxy\""),
            (ProxyType::Rule, "\"Rule\""),
        ];

        for (variant, expected_json) in variants {
            let serialized = serde_json::to_string(&variant).unwrap();
            assert_eq!(serialized, expected_json);

            let deserialized: ProxyType = serde_json::from_str(&serialized).unwrap();
            assert_eq!(deserialized, variant);
        }
    }

    /// 测试克隆功能
    #[test]
    fn test_clone() {
        let original = ProxyType::Rule;
        let cloned = original.clone();
        assert_eq!(original, cloned);
    }

    /// 测试调试输出
    #[test]
    fn test_debug() {
        let proxy_type = ProxyType::Proxy;
        let debug_str = format!("{:?}", proxy_type);
        assert_eq!(debug_str, "Proxy");
    }

    /// 测试哈希功能
    #[test]
    fn test_hash() {
        use std::collections::HashMap;

        let mut map = HashMap::new();
        map.insert(ProxyType::Direct, "direct");
        map.insert(ProxyType::Proxy, "proxy");

        assert_eq!(map.get(&ProxyType::Direct), Some(&"direct"));
        assert_eq!(map.get(&ProxyType::Proxy), Some(&"proxy"));
        assert_eq!(map.get(&ProxyType::Rule), None);
    }

    /// 测试常量值
    #[test]
    fn test_constants() {
        assert_eq!(proxy_type_index::DIRECT, 0);
        assert_eq!(proxy_type_index::REJECT, 1);
        assert_eq!(proxy_type_index::PROXY, 2);
        assert_eq!(proxy_type_index::RULE, 3);
    }
}