use crate::context::proxy_type::ProxyType;
use crate::mapper::tunnel_mapper::TunnelMapper;
use crate::mapper::tunnel_mapper_context::TunnelMapperContext;
use crate::mapper::tunnel_mapper_info::TunnelMapperInfo;
use crate::tunnel::tunnel::{Tunnel, TunnelStatus};
use crate::util::sni_util::SniUtil;
use flyshadow_common::interface::interface_selector::InterfaceSelector;
use flyshadow_common::tunnel::tunnel_package::PackageProtocol;
use log::{debug, error, info};
use netstack_lwip::SendHalf;
use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::io::{AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};
use tokio::net::tcp::OwnedWriteHalf;
use tokio::net::UdpSocket;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time::timeout;

pub struct TunnelContext {
    tunnel_mapper_context: Arc<TunnelMapperContext>,
    // 隧道
    tunnel: Arc<Tunnel>,
    // Socks5 UDP的Socket
    socks5_udp_socket: Option<Arc<UdpSocket>>,
    // Socks5 UDP的Socket地址
    socks5_udp_socket_addr: Option<SocketAddr>,
    // Socks5 UDP监听线程
    socks5_udp_job: Option<JoinHandle<()>>,
    enable_native_udp: RwLock<bool>,
    direct_conn_priority: RwLock<bool>,
    direct_conn_priority_timeout: RwLock<u64>,
}

impl TunnelContext {
    /// socks5 UDP监听线程
    async fn start_socks5_udp_job(&mut self, tunnel_mapper_context: Arc<TunnelMapperContext>, tunnel: Arc<Tunnel>) {
        match UdpSocket::bind("0.0.0.0:0").await {
            Ok(udp_socket) => {
                let socks5_udp_socket = Arc::new(udp_socket);
                let socks5_udp_socket_clone = socks5_udp_socket.clone();

                let socks5_udp_socket_addr = match socks5_udp_socket.local_addr() {
                    Ok(socks5_udp_socket_addr) => {
                        socks5_udp_socket_addr
                    }
                    Err(e) => {
                        log::error!("socks5_udp_socket.local_addr() error {}",e);
                        return;
                    }
                };
                info!("Socks5 UDP listen on :{}",socks5_udp_socket_addr);

                let socks5_udp_job = spawn(async move {
                    loop {
                        // 读UDP数据
                        let mut buf = vec![];
                        match socks5_udp_socket.recv_buf_from(&mut buf).await {
                            Ok((n, socket_addr)) => {
                                if n == 0 {
                                    return;
                                }
                                let temp_data = &buf[..n];
                                let last_domain_index;
                                // 解析目标地址
                                let target_domain = match temp_data[3] {
                                    // IPV4
                                    0x01 => {
                                        last_domain_index = 8;
                                        Ipv4Addr::new(
                                            temp_data[4], temp_data[5], temp_data[6], temp_data[7],
                                        ).to_string()
                                    }
                                    // 域名
                                    0x03 => {
                                        let len = temp_data[4];
                                        last_domain_index = 4 + len;
                                        let mut data = Vec::<u8>::new();
                                        for i in 0..len {
                                            data.push(data[(i + 5) as usize]);
                                        }
                                        String::from_utf8_lossy(data.as_slice()).to_string()
                                    }
                                    // IPV6
                                    0x04 => {
                                        last_domain_index = 20;
                                        let mut temp_data = [0u8; 16];
                                        for i in 0..16 {
                                            temp_data[i] = temp_data[i + 4];
                                        }
                                        Ipv6Addr::from(temp_data).to_string()
                                    }
                                    _ => {
                                        continue;
                                    }
                                };
                                let port = (((temp_data[last_domain_index as usize] & 0xff) as i32) << 8) | ((temp_data[(last_domain_index + 1) as usize] & 0xff) as i32);

                                // 端口占2个字节
                                let data = &temp_data[(last_domain_index + 2) as usize..];

                                // 发送udp数据到服务端
                                let tunnel_mapper_info = TunnelMapperInfo {
                                    protocol: PackageProtocol::UDP,
                                    source_addr: socket_addr.ip().to_string(),
                                    source_port: socket_addr.port(),
                                    target_addr: target_domain.clone(),
                                    target_port: port as u16,
                                    fake_target_addr: "".to_string(),
                                    fake_target_port: 0,
                                    process_name: "".to_string(),
                                    matcher_name: "".to_string(),
                                    matcher_rule: "".to_string(),
                                    proxy_type: ProxyType::Proxy,
                                    active_time: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis(),
                                    temp_data: vec![],
                                    direct_conn_priority: true,
                                    traffic_info: Arc::new(Default::default())
                                };
                                TunnelMapper::create_from_socks5_udp_to_tunnel(tunnel_mapper_context.clone(),
                                                                               tunnel_mapper_info,
                                                                               socks5_udp_socket.clone(),
                                                                               tunnel.clone(),
                                ).await;
                                tunnel_mapper_context.write_udp_to_server(&format!("{}==={:?}", socket_addr, PackageProtocol::UDP), target_domain, port as u16, data).await;
                            }
                            Err(_) => { break; }
                        }
                    }
                });

                self.socks5_udp_socket = Some(socks5_udp_socket_clone);
                self.socks5_udp_socket_addr = Some(socks5_udp_socket_addr);
                self.socks5_udp_job = Some(socks5_udp_job);
            }
            Err(e) => {
                log::error!("Start socks5 UDP error:{}",e);
            }
        }
    }
}

impl TunnelContext {
    /// 新建一个Tunnel上下文
    pub async fn new() -> TunnelContext {
        let tunnel_mapper_context = Arc::new(TunnelMapperContext::new());
        let tunnel = Tunnel::new(tunnel_mapper_context.clone()).await;

        let mut context = TunnelContext {
            tunnel_mapper_context: tunnel_mapper_context.clone(),
            tunnel: tunnel.clone(),
            socks5_udp_socket: None,
            socks5_udp_socket_addr: None,
            socks5_udp_job: None,
            enable_native_udp: RwLock::new(false),
            direct_conn_priority: RwLock::new(true),
            direct_conn_priority_timeout: RwLock::new(500),
        };
        context.start_socks5_udp_job(tunnel_mapper_context.clone(), tunnel.clone()).await;
        context
    }

    /// 关闭连接
    pub async fn close_connect(&self, key: String) {
        self.tunnel_mapper_context.close(&key).await;
    }

    /// 设置客户端uuid
    pub async fn set_client_uuid(&self, uuid: String) {
        self.tunnel.set_client_uuid(uuid).await;
    }

    /// 删除本地监听
    pub async fn remove_port_forwarding(&self, port: u16) {
        self.tunnel.port_forward.remove_port_forwarding(port).await;
    }

    /// 清空本地监听
    pub async fn clear_port_forwarding(&self) {
        self.tunnel.port_forward.clear_port_forwarding().await;
    }

    /// 添加本地监听
    pub async fn add_port_forwarding(
        &self,
        port: u16,
        target_address: String,
        target_client_uuid: String,
    ) -> Result<(), String> {
        self.tunnel.port_forward.clone().add_port_forwarding(port, target_address, target_client_uuid).await
    }

    /// 设置直连优先启动状态
    pub async fn set_direct_conn_priority(&self, enable: bool) {
        *self.direct_conn_priority.write().await = enable
    }

    /// 启用内置DNS
    pub async fn set_use_build_in_dns(&self, enable: bool) {
        self.tunnel_mapper_context.set_use_build_in_dns(enable).await;
    }

    /// 设置直连优先超时时间
    pub async fn set_direct_conn_priority_timeout(&self, timeout: u64) {
        *self.direct_conn_priority_timeout.write().await = timeout
    }

    /// 设置原生udp启动状态
    pub async fn set_native_udp(&self, enable: bool) {
        *self.enable_native_udp.write().await = enable
    }

    /// 获取socks5 UDP代理
    pub fn get_socks5_udp_addr(&self) -> Option<SocketAddr> {
        if let Some(socks5_udp_socket_addr) = self.socks5_udp_socket_addr {
            Some(socks5_udp_socket_addr.clone())
        } else { None }
    }

    /// 设置域名匹配规则 单条
    pub async fn set_domain_rule_obj(&self, domain: &String, matching: i32, proxy_type: i32, direct_conn_priority: u8) {
        self.tunnel_mapper_context.domain_rule_matcher.set_domain_rule_obj(domain, matching, proxy_type, direct_conn_priority).await;
    }
    /// 清空域名匹配规则
    pub async fn clear_domain_rule(&self) {
        self.tunnel_mapper_context.domain_rule_matcher.clear_domain_rule().await;
    }

    /// 设置GEO IP数据
    pub async fn set_geoip(&self, key: String, value: Vec<u8>) {
        self.tunnel_mapper_context.domain_rule_matcher.set_geoip(key, value).await;
    }

    /// 使用匹配器匹配域名
    async fn match_domain(&self, tunnel_mapper_info: &mut TunnelMapperInfo) {
        self.tunnel_mapper_context.domain_rule_matcher.match_domain(tunnel_mapper_info).await
    }

    pub async fn get_tunnel_mapper_info_json(&self) -> String {
        self.tunnel_mapper_context.get_tunnel_mapper_info_json().await
    }

    /// 设置代理规则
    pub async fn set_proxy_type(&self, id: i32) {
        self.tunnel_mapper_context.domain_rule_matcher.set_proxy_type(id).await
    }

    /// 设置UDP代理规则
    pub async fn set_udp_proxy_type(&self, id: i32) {
        self.tunnel_mapper_context.domain_rule_matcher.set_udp_proxy_type(id).await
    }

    /// 设置FakeIP 开关
    pub async fn set_fake_ip(&self, enable: bool) {
        self.tunnel_mapper_context.fake_ip_cache_context.set_fake_ip(enable).await;
    }

    /// 设置本地网卡IP地址
    pub async fn set_tun_mode_enable(&self, enable: bool) {
        self.tunnel_mapper_context.set_tun_mode_enable(enable).await;
    }
    /// 设置IPV6 开关
    pub async fn set_ipv6_enable(&self, enable: bool) {
        self.tunnel_mapper_context.set_ipv6_enable(enable).await;
    }

    /// 获取IPV6 开关
    pub async fn get_ipv6_enable(&self) -> bool {
        self.tunnel_mapper_context.get_ipv6_enable().await
    }

    /// 设置本地网卡
    pub async fn set_local_interface(&self, interface_selector: InterfaceSelector) {
        self.tunnel_mapper_context.set_local_interface(interface_selector).await;
    }

    /// 获取本地网卡
    pub async fn get_local_interface(&self) -> InterfaceSelector {
        self.tunnel_mapper_context.get_local_interface_selector().await
    }

    /// 获取隧道的上传流量
    pub async fn get_tunnel_upload(&self) -> i64 {
        self.tunnel.get_upload().await
    }

    /// 获取隧道的下载流量
    pub async fn get_tunnel_download(&self) -> i64 {
        self.tunnel.get_download().await
    }
    /// 获取隧道的Ping延迟
    pub async fn get_tunnel_ping_delay(&self) -> i32 {
        self.tunnel.get_ping_delay().await as i32
    }

    /// 获取隧道的状态
    pub async fn get_tunnel_status(&self) -> i32 {
        match self.tunnel.get_status().await {
            TunnelStatus::Success => { 0 }
            TunnelStatus::WaitLogin => { 1 }
            TunnelStatus::Logout => { 2 }
        }
    }

    ///连接Tunnel
    pub async fn connect_tunnel(&self, host: String, port: u16, password: String) {
        self.tunnel.connect(host, port, password).await;
        self.tunnel_mapper_context.clear().await;
    }

    /// 关闭隧道连接
    pub async fn close_tunnel(&self) {
        self.tunnel.disconnect().await;
        self.tunnel_mapper_context.clear().await;
    }

    /// 关闭服务端连接
    pub async fn close_server_connect(&self, source_addr: &String) {
        self.tunnel_mapper_context.close(&format!("{}==={:?}", source_addr, PackageProtocol::TCP)).await;
    }

    /// 从代理客户端连接服务端
    pub async fn proxy_connect_server(&self, mut tunnel_mapper_info: TunnelMapperInfo, client_writer: OwnedWriteHalf) -> Result<(), String> {
        self.tunnel_mapper_context.get_fake_ip_match_result(&mut tunnel_mapper_info).await;

        self.check_direct_connect_priority(&mut tunnel_mapper_info).await;

        match tunnel_mapper_info.proxy_type {
            ProxyType::Direct => {
                debug!("Connect {}:{} source:{}:{} Direct", tunnel_mapper_info.target_addr,tunnel_mapper_info.target_port,tunnel_mapper_info.source_addr,tunnel_mapper_info.source_port);
                TunnelMapper::create_from_proxy_to_direct(self.tunnel_mapper_context.clone(), tunnel_mapper_info,
                                                          client_writer).await
            }
            ProxyType::Reject => {
                debug!("{}:{} source:{}:{} Reject", tunnel_mapper_info.target_addr,tunnel_mapper_info.target_port,tunnel_mapper_info.source_addr,tunnel_mapper_info.source_port);
                Err("Reject".to_string())
            }
            ProxyType::Proxy => {
                debug!("Connect {}:{} source:{}:{} Proxy", tunnel_mapper_info.target_addr,tunnel_mapper_info.target_port,tunnel_mapper_info.source_addr,tunnel_mapper_info.source_port);

                // 连接服务端
                TunnelMapper::create_from_proxy_to_tunnel(self.tunnel_mapper_context.clone(), tunnel_mapper_info,
                                                          client_writer,
                                                          self.tunnel.clone()).await
            }
            _ => {
                Err("matcher error".to_string())
            }
        }
    }

    /// 从Tun连接服务端
    pub async fn tun_connect_server<S>(&self, mut tunnel_mapper_info: TunnelMapperInfo, mut tcp_stream: S) -> Result<(), String>
    where
        S: AsyncRead + AsyncWrite + Unpin + Send + 'static + Sync,
    {
        self.tunnel_mapper_context.get_fake_ip_match_result(&mut tunnel_mapper_info).await;

        // DNS解析
        if tunnel_mapper_info.target_port == 53 {
            let mut buf = [0u8; 2048];
            let mut data = match tcp_stream.read(&mut buf).await {
                Ok(0) => {
                    return Err("None data read".to_string());
                }
                Ok(n) => {
                    buf[..n].to_vec()
                }
                Err(e) => {
                    return Err(e.to_string());
                }
            };
            match self.tunnel_mapper_context.fake_ip_cache_context.generate_fake_response(&mut data).await {
                Ok(r) => {
                    let _ = tcp_stream.write_all(r.as_slice()).await;
                }
                Err(_) => {}
            };
            return Ok(());
        }

        // 如果是IP 尝试查询SNI
        if tunnel_mapper_info.target_addr.parse::<IpAddr>().is_ok() {
            for _ in 0..2 {
                match timeout(Duration::from_millis(100), tcp_stream.read_buf(&mut tunnel_mapper_info.temp_data)).await {
                    Ok(result) => {
                        if result.is_ok() {
                            // 查询SNI
                            if let Some(domain) = SniUtil::resolve_sni(&tunnel_mapper_info.temp_data) {
                                tunnel_mapper_info.target_addr = domain;
                                // 重新匹配规则
                                self.tunnel_mapper_context.get_fake_ip_match_result(&mut tunnel_mapper_info).await;
                            }
                        }
                    }
                    Err(_) => {}
                }
            }
        }

        self.check_direct_connect_priority(&mut tunnel_mapper_info).await;

        match tunnel_mapper_info.proxy_type {
            ProxyType::Direct => {
                debug!("Tun Connect {}:{} ,fake target addr: {}:{} ,source: {}:{} Direct", tunnel_mapper_info.target_addr,tunnel_mapper_info.target_port,
                    tunnel_mapper_info.fake_target_addr,tunnel_mapper_info.fake_target_port,tunnel_mapper_info.source_addr,tunnel_mapper_info.source_port);
                TunnelMapper::create_from_tun_to_direct(self.tunnel_mapper_context.clone(),
                                                        tunnel_mapper_info, tcp_stream).await
            }
            ProxyType::Reject => {
                let _ = tcp_stream.shutdown().await;
                debug!("Tun {}:{} source:{}:{} Reject", tunnel_mapper_info.target_addr,tunnel_mapper_info.target_port,tunnel_mapper_info.source_addr,tunnel_mapper_info.source_port);
                Err("Reject".to_string())
            }
            ProxyType::Proxy => {
                debug!("Tun Connect {}:{} ,fake target addr: {}:{} ,source:{}:{} Proxy", tunnel_mapper_info.target_addr,tunnel_mapper_info.target_port,
                    tunnel_mapper_info.fake_target_addr,tunnel_mapper_info.fake_target_port,tunnel_mapper_info.source_addr,tunnel_mapper_info.source_port);

                // 连接服务端
                TunnelMapper::create_from_tun_to_tunnel(self.tunnel_mapper_context.clone(),
                                                        tunnel_mapper_info,
                                                        tcp_stream,
                                                        self.tunnel.clone()).await
            }
            _ => {
                Err("matcher error".to_string())
            }
        }
    }

    /// 判断直连优先
    async fn check_direct_connect_priority(&self, tunnel_mapper_info: &mut TunnelMapperInfo) {
        if *self.direct_conn_priority.read().await &&
            tunnel_mapper_info.direct_conn_priority &&
            self.tunnel_mapper_context.domain_rule_matcher.get_proxy_type().await == ProxyType::Rule &&
            tunnel_mapper_info.proxy_type == ProxyType::Proxy {
            TunnelMapper::check_direct_connect_priority(&self.tunnel_mapper_context, tunnel_mapper_info, *self.direct_conn_priority_timeout.read().await).await;
        }
    }

    /// 客户代理端向服务端发送数据
    pub async fn proxy_write_to_server(&self, source_addr: &String, data: &[u8]) -> bool {
        self.tunnel_mapper_context.write_to_server(&format!("{}==={:?}", source_addr, PackageProtocol::TCP), data).await
    }

    /// tun端向服务端发送数据
    pub async fn tun_tcp_write_to_server(&self, source_addr: &String, data: &[u8]) -> bool {
        self.tunnel_mapper_context.write_to_server(&format!("{}==={:?}", source_addr, PackageProtocol::TCP), data).await
    }

    /// tun端向服务端发送UDP数据
    pub async fn tun_udp_write_to_server(&self, mut tunnel_mapper_info: TunnelMapperInfo, writer: Arc<SendHalf>, data: &[u8]) {
        self.tunnel_mapper_context.get_fake_ip_match_result(&mut tunnel_mapper_info).await;
        let mut key = format!("{}:{}==={:?}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port, PackageProtocol::UDP);
        let target_addr = tunnel_mapper_info.target_addr.clone();
        let target_port = tunnel_mapper_info.target_port;

        if target_port == 53 {
            match self.tunnel_mapper_context.fake_ip_cache_context.generate_fake_response(data).await {
                Ok(data) => {
                    if let Ok(socket_addr1) = format!("{}:{}", tunnel_mapper_info.target_addr, target_port).parse::<SocketAddr>() {
                        if let Ok(socket_addr2) = format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port).parse::<SocketAddr>() {
                            let _ = writer.send_to(data.as_slice(), &socket_addr1, &socket_addr2);
                        }
                    }
                }
                Err(_e) => {}
            };
            return;
        }

        match tunnel_mapper_info.proxy_type {
            ProxyType::Direct => {
                // info!("Tun UDP send to {}:{} Direct",target_addr,target_port);
                TunnelMapper::create_from_tun_udp_to_direct(self.tunnel_mapper_context.clone(), tunnel_mapper_info,
                                                            writer).await;
            }
            ProxyType::Reject => {}
            ProxyType::Proxy => {
                if tunnel_mapper_info.source_port == 20516 {
                    info!("Tun UDP send to {}:{} Proxy , native udp:{}",target_addr,target_port,*self.enable_native_udp.read().await);
                }
                if *self.enable_native_udp.read().await {
                    key = format!("{}:{}==={:?}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port, PackageProtocol::NativeUdp);
                    TunnelMapper::create_from_tun_native_udp_to_tunnel(self.tunnel_mapper_context.clone(), tunnel_mapper_info,
                                                                       writer,
                                                                       self.tunnel.clone()).await;
                } else {
                    TunnelMapper::create_from_tun_udp_to_tunnel(self.tunnel_mapper_context.clone(), tunnel_mapper_info,
                                                                writer,
                                                                self.tunnel.clone()).await;
                }
            }
            _ => {}
        }

        // info!("Tun UDP write_udp_to_server {}:{}",target_addr,target_port);
        self.tunnel_mapper_context.write_udp_to_server(&key, target_addr, target_port, data).await;
    }

    /// tproxy端向服务端发送UDP数据
    pub async fn tproxy_udp_write_to_server(&self, mut tunnel_mapper_info: TunnelMapperInfo, data: Vec<u8>, udp_socket: Arc<std::net::UdpSocket>, tproxy_mode_type: u16) {
        self.tunnel_mapper_context.get_fake_ip_match_result(&mut tunnel_mapper_info).await;
        let mut key = format!("{}:{}==={:?}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port, PackageProtocol::UDP);
        let target_addr = tunnel_mapper_info.target_addr.clone();
        let target_port = tunnel_mapper_info.target_port;

        if target_port == 53 || tproxy_mode_type == 2 {
            match self.tunnel_mapper_context.fake_ip_cache_context.generate_fake_response(&data).await {
                Ok(data) => {
                    if let Ok(socket_addr) = format!("{}:{}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port).parse::<SocketAddr>() {
                        if tproxy_mode_type == 1 {
                            let udp_socket = match self.tunnel_mapper_context.get_tproxy_udp_socket(tunnel_mapper_info.target_addr, target_port).await {
                                Ok(sock) => { sock }
                                Err(e) => {
                                    error!("Failed to create UDP socket: {}",e);
                                    return;
                                }
                            };
                            let _ = udp_socket.send_to(&data, socket_addr).await;
                        } else {
                            let _ = udp_socket.send_to(&data, socket_addr);
                        }
                    }
                }
                Err(_e) => {}
            };
            return;
        }

        match tunnel_mapper_info.proxy_type {
            ProxyType::Direct => {
                // info!("Tun UDP send to {}:{} Direct",target_addr,target_port);
                TunnelMapper::create_from_tproxy_udp_to_direct(self.tunnel_mapper_context.clone(), tunnel_mapper_info).await;
            }
            ProxyType::Reject => {}
            ProxyType::Proxy => {
                if *self.enable_native_udp.read().await {
                    // info!("Tun Native UDP send to {}:{} Proxy",target_addr,target_port);
                    key = format!("{}:{}==={:?}", tunnel_mapper_info.source_addr, tunnel_mapper_info.source_port, PackageProtocol::NativeUdp);
                    TunnelMapper::create_from_tproxy_native_udp_to_tunnel(self.tunnel_mapper_context.clone(), tunnel_mapper_info,
                                                                          self.tunnel.clone()).await;
                } else {
                    // info!("Tun UDP send to {}:{} Proxy",target_addr,target_port);
                    TunnelMapper::create_from_tproxy_udp_to_tunnel(self.tunnel_mapper_context.clone(), tunnel_mapper_info,
                                                                   self.tunnel.clone()).await;
                }
            }
            _ => {}
        }

        // info!("Tun UDP write_udp_to_server {}:{}",target_addr,target_port);
        self.tunnel_mapper_context.write_udp_to_server(&key, target_addr, target_port, &data).await;
    }
}
