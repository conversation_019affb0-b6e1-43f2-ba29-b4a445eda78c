use flyshadow_common::tunnel::tunnel_package::PackageProtocol;
use geoip2::{Country, Reader};
use ipnet::IpNet;
use std::any::Any;
use std::collections::HashMap;
use std::fmt::Debug;
use std::io::Read;
use std::net::IpAddr;
use tokio::sync::RwLock;

use crate::context::proxy_type::ProxyType;
use crate::mapper::tunnel_mapper_info::TunnelMapperInfo;

/// 规则匹配器特征
/// 定义了所有规则匹配器必须实现的基本方法
pub trait RuleMatcher: Send + Sync + Any {
    /// 通过域名字符串创建匹配器
    ///
    /// # 参数
    /// * `domain` - 域名或规则字符串
    /// * `proxy_type` - 代理类型索引
    /// * `direct_conn_priority` - 是否优先直连
    fn new(domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized;

    /// 通过字节数组创建匹配器（主要用于GeoIP数据）
    ///
    /// # 参数
    /// * `bytes` - 字节数据
    /// * `proxy_type` - 代理类型索引
    /// * `direct_conn_priority` - 是否优先直连
    fn new_bytes(bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized;

    /// 执行匹配逻辑
    ///
    /// # 参数
    /// * `tunnel_mapper_info` - 隧道映射信息，包含连接的详细信息
    ///
    /// # 返回值
    /// 如果匹配成功返回true，否则返回false
    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool;

    /// 获取Any类型引用，用于类型转换
    fn as_any(&self) -> &dyn Any;

    /// 转换为字符串表示
    fn to_string(&self) -> String;
}

/// 匹配结果结构体
/// 包含匹配器的名称、规则和代理类型信息
#[derive(Debug, Clone, PartialEq)]
pub struct MatchResult {
    /// 匹配器名称
    pub matcher_name: String,
    /// 匹配规则
    pub matcher_rule: String,
    /// 代理类型
    pub proxy_type: ProxyType,
}

impl MatchResult {
    /// 创建新的匹配结果
    ///
    /// # 参数
    /// * `matcher_name` - 匹配器名称
    /// * `matcher_domain` - 匹配的域名或规则
    /// * `proxy_type` - 代理类型
    pub fn new(matcher_name: String, matcher_domain: String, proxy_type: ProxyType) -> MatchResult {
        MatchResult {
            matcher_name,
            matcher_rule: matcher_domain,
            proxy_type,
        }
    }
}

/// 完全匹配域名匹配器
/// 只有当目标地址与配置的域名完全相同时才匹配
pub struct AllDomainMatcher {
    /// 要匹配的域名
    domain: String,
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for AllDomainMatcher {
    fn new(domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        AllDomainMatcher {
            domain,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(_bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        AllDomainMatcher {
            domain: String::new(),
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        // 检查目标地址是否与配置的域名完全匹配
        if self.domain == tunnel_mapper_info.target_addr {
            tunnel_mapper_info.matcher_name = "all_domain_matcher".to_string();
            tunnel_mapper_info.matcher_rule = self.domain.clone();
            tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
            tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
            true
        } else {
            false
        }
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("domain: {}, proxy_type: {:?}", self.domain, ProxyType::from_index(self.proxy_type))
    }
}

/// 后缀域名匹配器
/// 匹配以指定域名为后缀的所有域名，例如配置"example.com"可以匹配"sub.example.com"
pub struct SuffixDomainMatcher {
    /// 要匹配的域名后缀
    domain: String,
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for SuffixDomainMatcher {
    fn new(domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        SuffixDomainMatcher {
            domain,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(_bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        SuffixDomainMatcher {
            domain: String::new(),
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        let target_addr = &tunnel_mapper_info.target_addr;
        let domain_with_dot = format!(".{}", self.domain);

        // 检查是否以".domain"结尾或者完全等于domain
        if target_addr.ends_with(&domain_with_dot) || target_addr == &self.domain {
            tunnel_mapper_info.matcher_name = "suffix_domain_matcher".to_string();
            tunnel_mapper_info.matcher_rule = self.domain.clone();
            tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
            tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
            true
        } else {
            false
        }
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("domain: {}, proxy_type: {:?}", self.domain, ProxyType::from_index(self.proxy_type))
    }
}

/// 关键词域名匹配器
/// 匹配包含指定关键词的所有域名，例如配置"google"可以匹配"www.google.com"
pub struct KeywordDomainMatcher {
    /// 要匹配的关键词
    domain: String,
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for KeywordDomainMatcher {
    fn new(domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        KeywordDomainMatcher {
            domain,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(_bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        KeywordDomainMatcher {
            domain: String::new(),
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        // 检查目标地址是否包含指定的关键词
        if tunnel_mapper_info.target_addr.contains(&self.domain) {
            tunnel_mapper_info.matcher_name = "keyword_domain_matcher".to_string();
            tunnel_mapper_info.matcher_rule = self.domain.clone();
            tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
            tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
            true
        } else {
            false
        }
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("domain: {}, proxy_type: {:?}", self.domain, ProxyType::from_index(self.proxy_type))
    }
}

/// IPv4地址匹配器
/// 使用CIDR表示法匹配IPv4地址范围，例如"***********/24"
pub struct IPV4Matcher {
    /// CIDR规则，用于匹配IP地址范围
    cidr_rule: Option<IpNet>,
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for IPV4Matcher {
    fn new(domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        // 尝试解析CIDR格式的IP地址范围
        let cidr_rule = domain.parse::<IpNet>().ok();
        IPV4Matcher {
            cidr_rule,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(_bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        IPV4Matcher {
            cidr_rule: None,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        // 解析目标地址为IP地址
        let ip_to_check = match tunnel_mapper_info.target_addr.parse::<IpAddr>() {
            Ok(ip) => ip,
            Err(_) => return false,
        };

        // 检查CIDR规则是否存在
        let ip_net = match &self.cidr_rule {
            Some(net) => net,
            None => return false,
        };

        // 检查IP地址是否在CIDR范围内
        if ip_net.contains(&ip_to_check) {
            tunnel_mapper_info.matcher_name = "ipv4_matcher".to_string();
            tunnel_mapper_info.matcher_rule = ip_net.to_string();
            tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
            tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
            true
        } else {
            false
        }
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("cidr_rule: {:?}, proxy_type: {:?}", self.cidr_rule, ProxyType::from_index(self.proxy_type))
    }
}

/// 源IPv4地址匹配器
/// 根据连接的源IP地址进行匹配，使用CIDR表示法
pub struct SourceIPV4Matcher {
    /// CIDR规则，用于匹配源IP地址范围
    cidr_rule: Option<IpNet>,
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for SourceIPV4Matcher {
    fn new(domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        // 尝试解析CIDR格式的IP地址范围
        let cidr_rule = domain.parse::<IpNet>().ok();
        SourceIPV4Matcher {
            cidr_rule,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(_bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        SourceIPV4Matcher {
            cidr_rule: None,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        // 解析源地址为IP地址
        let ip_to_check = match tunnel_mapper_info.source_addr.parse::<IpAddr>() {
            Ok(ip) => ip,
            Err(_) => return false,
        };

        // 检查CIDR规则是否存在
        let ip_net = match &self.cidr_rule {
            Some(net) => net,
            None => return false,
        };

        // 检查源IP地址是否在CIDR范围内
        if ip_net.contains(&ip_to_check) {
            tunnel_mapper_info.matcher_name = "src_ipv4_matcher".to_string();
            tunnel_mapper_info.matcher_rule = ip_net.to_string();
            tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
            tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
            true
        } else {
            false
        }
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("cidr_rule: {:?}, proxy_type: {:?}", self.cidr_rule, ProxyType::from_index(self.proxy_type))
    }
}

/// GeoIP地理位置匹配器
/// 根据IP地址的地理位置信息进行匹配，使用MaxMind GeoIP数据库
pub struct GEOIPMatcher {
    /// GeoIP数据库的字节数据
    geo_ip_bytes: Vec<u8>,
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for GEOIPMatcher {
    fn new(_geo_ip_bytes: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        GEOIPMatcher {
            geo_ip_bytes: Vec::new(),
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        GEOIPMatcher {
            geo_ip_bytes: bytes,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        // 解析目标地址为IP地址
        let ip_to_check = match tunnel_mapper_info.target_addr.parse::<IpAddr>() {
            Ok(ip) => ip,
            Err(_) => return false,
        };

        // 使用GeoIP数据库查询IP地址的国家代码
        let country_code = Reader::<Country>::from_bytes(&self.geo_ip_bytes)
            .ok()
            .and_then(|reader| {
                reader.lookup(ip_to_check)
                    .ok()
                    .and_then(|country_info| {
                        country_info.country
                            .and_then(|country| {
                                country.iso_code.map(|code| code.to_string())
                            })
                    })
            })
            .unwrap_or_default();

        // 如果成功获取到国家代码，则匹配成功
        if !country_code.is_empty() {
            tunnel_mapper_info.matcher_name = "geo_ip_matcher".to_string();
            tunnel_mapper_info.matcher_rule = country_code;
            tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
            tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
            true
        } else {
            false
        }
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("geo_ip_list, proxy_type: {:?}", ProxyType::from_index(self.proxy_type))
    }
}


/// 目标端口匹配器
/// 根据连接的目标端口进行匹配
pub struct DstPortMatcher {
    /// 要匹配的端口号
    port: Option<i32>,
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for DstPortMatcher {
    fn new(domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        // 尝试解析端口号
        let port = domain.parse::<i32>().ok();
        DstPortMatcher {
            port,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(_bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        DstPortMatcher {
            port: None,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        // 检查是否配置了端口号
        if let Some(port) = self.port {
            // 比较目标端口是否匹配
            if tunnel_mapper_info.target_port as i32 == port {
                tunnel_mapper_info.matcher_name = "dst_port_matcher".to_string();
                tunnel_mapper_info.matcher_rule = port.to_string();
                tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
                tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
                true
            } else {
                false
            }
        } else {
            false
        }
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("DstPortMatcher: {:?}, proxy_type: {:?}", self.port, ProxyType::from_index(self.proxy_type))
    }
}


/// 源端口匹配器
/// 根据连接的源端口进行匹配
pub struct SrcPortMatcher {
    /// 要匹配的端口号
    port: Option<i32>,
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for SrcPortMatcher {
    fn new(domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        // 尝试解析端口号
        let port = domain.parse::<i32>().ok();
        SrcPortMatcher {
            port,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(_bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        SrcPortMatcher {
            port: None,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        // 检查是否配置了端口号
        if let Some(port) = self.port {
            // 比较源端口是否匹配
            if tunnel_mapper_info.source_port as i32 == port {
                tunnel_mapper_info.matcher_name = "src_port_matcher".to_string();
                tunnel_mapper_info.matcher_rule = port.to_string();
                tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
                tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
                true
            } else {
                false
            }
        } else {
            false
        }
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("SrcPortMatcher: {:?}, proxy_type: {:?}", self.port, ProxyType::from_index(self.proxy_type))
    }
}


/// 进程名称匹配器
/// 根据发起连接的进程名称进行匹配
pub struct ProcessNameMatcher {
    /// 要匹配的进程名称
    process_name: String,
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for ProcessNameMatcher {
    fn new(domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        ProcessNameMatcher {
            process_name: domain,
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(_bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        ProcessNameMatcher {
            process_name: String::new(),
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        // 检查进程名称是否为空
        if tunnel_mapper_info.process_name.is_empty() || self.process_name.is_empty() {
            return false;
        }

        // 进行进程名称匹配（完全匹配或包含匹配）
        let is_match = tunnel_mapper_info.process_name == self.process_name
            || self.process_name.contains(&tunnel_mapper_info.process_name)
            || tunnel_mapper_info.process_name.contains(&self.process_name);

        if is_match {
            tunnel_mapper_info.matcher_name = "process_name_matcher".to_string();
            tunnel_mapper_info.matcher_rule = self.process_name.clone();
            tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
            tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
            true
        } else {
            false
        }
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("ProcessNameMatcher: {}, proxy_type: {:?}", self.process_name, ProxyType::from_index(self.proxy_type))
    }
}

/// 空匹配器（默认匹配器）
/// 总是匹配成功，通常作为最后的默认规则使用
pub struct EmptyMatcher {
    /// 代理类型索引
    proxy_type: i32,
    /// 是否优先直连
    direct_conn_priority: bool,
}

impl RuleMatcher for EmptyMatcher {
    fn new(_domain: String, proxy_type: i32, direct_conn_priority: bool) -> Self {
        EmptyMatcher {
            proxy_type,
            direct_conn_priority,
        }
    }

    fn new_bytes(_bytes: Vec<u8>, proxy_type: i32, direct_conn_priority: bool) -> Self
    where
        Self: Sized,
    {
        EmptyMatcher {
            proxy_type,
            direct_conn_priority,
        }
    }

    fn do_match(&self, tunnel_mapper_info: &mut TunnelMapperInfo) -> bool {
        // 空匹配器总是匹配成功，作为默认规则
        tunnel_mapper_info.matcher_name = "empty_matcher".to_string();
        tunnel_mapper_info.matcher_rule = String::new();
        tunnel_mapper_info.proxy_type = ProxyType::from_index(self.proxy_type);
        tunnel_mapper_info.direct_conn_priority = self.direct_conn_priority;
        true
    }

    fn as_any(&self) -> &dyn Any {
        self
    }

    fn to_string(&self) -> String {
        format!("domain: empty, proxy_type: {:?}", ProxyType::from_index(self.proxy_type))
    }
}


/// 域名规则匹配器管理器
/// 负责管理所有的规则匹配器，并提供统一的匹配接口
pub struct DomainRuleMatcher {
    /// 规则匹配器列表，按优先级排序
    domain_rule_matcher: RwLock<Vec<Box<dyn RuleMatcher>>>,
    /// GeoIP数据库映射，键为国家代码，值为数据库字节数据
    geoip_map: RwLock<HashMap<String, Vec<u8>>>,
    /// TCP连接的代理类型
    proxy_type: RwLock<ProxyType>,
    /// UDP连接的代理类型
    udp_proxy_type: RwLock<ProxyType>,
}

impl DomainRuleMatcher {
    /// 创建新的域名规则匹配器
    pub fn new() -> Self {
        DomainRuleMatcher {
            domain_rule_matcher: RwLock::new(Vec::new()),
            geoip_map: RwLock::new(HashMap::new()),
            proxy_type: RwLock::new(ProxyType::Rule),
            udp_proxy_type: RwLock::new(ProxyType::Rule),
        }
    }

    /// 添加单条域名规则
    ///
    /// # 参数
    /// * `domain` - 域名或规则字符串
    /// * `matching` - 匹配类型：0=完全匹配, 1=后缀匹配, 2=关键词匹配, 3=IPv4, 5=源IPv4, 6=GeoIP, 7=目标端口, 8=源端口, 9=进程名, 10=默认
    /// * `proxy_type` - 代理类型索引
    /// * `direct_conn_priority` - 直连优先级：0=否, 1=是
    pub async fn set_domain_rule_obj(&self, domain: &String, matching: i32, proxy_type: i32, direct_conn_priority: u8) {
        let direct_conn_priority = direct_conn_priority != 0;
        let mut write_guard = self.domain_rule_matcher.write().await;

        // 查找空匹配器和GeoIP匹配器的位置，用于确定插入位置
        let empty_matcher_pos = write_guard.len().saturating_sub(1);
        let empty_matcher_opt = write_guard.iter().find(|&item| item.as_any().is::<EmptyMatcher>());

        let geo_ip_matcher_pos = write_guard.len().saturating_sub(1);
        let geo_ip_matcher_opt = write_guard.iter().find(|&item| item.as_any().is::<GEOIPMatcher>());

        // 根据匹配类型创建相应的匹配器
        let matcher: Option<Box<dyn RuleMatcher>> = match matching {
            0 => Some(Box::new(AllDomainMatcher::new(domain.clone(), proxy_type, direct_conn_priority))),
            1 => Some(Box::new(SuffixDomainMatcher::new(domain.clone(), proxy_type, direct_conn_priority))),
            2 => Some(Box::new(KeywordDomainMatcher::new(domain.clone(), proxy_type, direct_conn_priority))),
            3 => Some(Box::new(IPV4Matcher::new(domain.clone(), proxy_type, direct_conn_priority))),
            5 => Some(Box::new(SourceIPV4Matcher::new(domain.clone(), proxy_type, direct_conn_priority))),
            6 => {
                // GeoIP匹配器需要特殊处理，需要从geoip_map中获取数据
                if let Some(geo_ip) = self.geoip_map.read().await.get(domain).cloned() {
                    if geo_ip_matcher_opt.is_none() {
                        Some(Box::new(GEOIPMatcher::new_bytes(geo_ip, proxy_type, direct_conn_priority)))
                    } else {
                        None // 已存在GeoIP匹配器，不重复添加
                    }
                } else {
                    None
                }
            }
            7 => Some(Box::new(DstPortMatcher::new(domain.clone(), proxy_type, direct_conn_priority))),
            8 => Some(Box::new(SrcPortMatcher::new(domain.clone(), proxy_type, direct_conn_priority))),
            9 => Some(Box::new(ProcessNameMatcher::new(domain.clone(), proxy_type, direct_conn_priority))),
            10 => {
                // 空匹配器只能有一个
                if empty_matcher_opt.is_none() {
                    Some(Box::new(EmptyMatcher::new(domain.clone(), proxy_type, direct_conn_priority)))
                } else {
                    None
                }
            }
            _ => None,
        };

        // 如果成功创建了匹配器，则插入到合适的位置
        if let Some(matcher) = matcher {
            let has_empty_matcher = empty_matcher_opt.is_some();
            let has_geo_ip_matcher = geo_ip_matcher_opt.is_some();
            self.insert_matcher(matcher, &mut write_guard, has_empty_matcher, has_geo_ip_matcher, empty_matcher_pos, geo_ip_matcher_pos);
        }
    }

    /// 插入匹配器到合适的位置
    ///
    /// # 参数
    /// * `matcher` - 要插入的匹配器
    /// * `write_guard` - 匹配器列表的写锁
    /// * `has_empty_matcher` - 是否已存在空匹配器
    /// * `has_geo_ip_matcher` - 是否已存在GeoIP匹配器
    /// * `empty_matcher_pos` - 空匹配器的位置
    /// * `geo_ip_matcher_pos` - GeoIP匹配器的位置
    fn insert_matcher(
        &self,
        matcher: Box<dyn RuleMatcher>,
        write_guard: &mut Vec<Box<dyn RuleMatcher>>,
        has_empty_matcher: bool,
        has_geo_ip_matcher: bool,
        empty_matcher_pos: usize,
        geo_ip_matcher_pos: usize,
    ) {
        // 确定插入位置：GeoIP匹配器优先于空匹配器
        if has_geo_ip_matcher {
            write_guard.insert(geo_ip_matcher_pos, matcher);
        } else if has_empty_matcher {
            write_guard.insert(empty_matcher_pos, matcher);
        } else {
            write_guard.push(matcher);
        }
    }

    /// 清空域名规则
    pub async fn clear_domain_rule(&self) {
        self.domain_rule_matcher.write().await.clear();
    }


    /// 设置GeoIP数据
    ///
    /// # 参数
    /// * `key` - 国家代码（如"CN", "US"等）
    /// * `value` - GeoIP数据库的字节数据
    pub async fn set_geoip(&self, key: String, value: Vec<u8>) {
        self.geoip_map.write().await.insert(key, value);
    }

    /// 使用匹配器匹配域名
    /// 根据连接信息匹配相应的代理规则
    ///
    /// # 参数
    /// * `tunnel_mapper_info` - 隧道映射信息，包含连接的详细信息
    pub async fn match_domain(&self, tunnel_mapper_info: &mut TunnelMapperInfo) {
        // 根据协议类型选择相应的代理类型
        let proxy_type = if tunnel_mapper_info.protocol == PackageProtocol::TCP {
            self.proxy_type.read().await.clone()
        } else {
            self.udp_proxy_type.read().await.clone()
        };

        if proxy_type == ProxyType::Rule {
            // 使用规则模式，遍历所有匹配器进行匹配
            for matcher in self.domain_rule_matcher.read().await.iter() {
                if matcher.do_match(tunnel_mapper_info) {
                    // 匹配成功，直接返回
                    return;
                }
            }

            // 没有匹配到任何规则，使用默认的直连
            tunnel_mapper_info.matcher_name = "none_matcher".to_string();
            tunnel_mapper_info.matcher_rule = String::new();
            tunnel_mapper_info.proxy_type = ProxyType::Direct;
        } else {
            // 使用用户设置的固定代理类型
            tunnel_mapper_info.matcher_name = "user_setting_matcher".to_string();
            tunnel_mapper_info.matcher_rule = String::new();
            tunnel_mapper_info.proxy_type = proxy_type;
        }
    }

    /// 设置TCP连接的代理类型
    ///
    /// # 参数
    /// * `id` - 代理类型索引
    pub async fn set_proxy_type(&self, id: i32) {
        *self.proxy_type.write().await = ProxyType::from_index(id);
    }

    /// 设置UDP连接的代理类型
    ///
    /// # 参数
    /// * `id` - 代理类型索引
    pub async fn set_udp_proxy_type(&self, id: i32) {
        *self.udp_proxy_type.write().await = ProxyType::from_index(id);
    }

    /// 获取TCP连接的代理类型
    ///
    /// # 返回值
    /// 当前设置的TCP代理类型
    pub async fn get_proxy_type(&self) -> ProxyType {
        self.proxy_type.read().await.clone()
    }
}

/// GeoIP匹配测试
/// 测试使用GeoIP数据库进行地理位置匹配的功能
#[tokio::test]
async fn test_geo_ip() {
    use std::io::Read;
    use std::fs::File;
    use std::sync::Arc;

    // 创建域名规则匹配器
    let domain_rule_matcher = DomainRuleMatcher::new();

    let mut vec1 = vec![];

    // 读取GeoIP数据库文件
    let mut file = File::open("/Users/<USER>/Downloads/Country-only-cn-private.mmdb").unwrap();
    file.read_to_end(&mut vec1).unwrap();

    // 设置中国的GeoIP数据
    domain_rule_matcher.set_geoip("CN".to_string(), vec1).await;
    domain_rule_matcher.set_proxy_type(3).await;

    // 添加GeoIP匹配规则：中国IP直连
    domain_rule_matcher.set_domain_rule_obj(&"CN".to_string(), 6, 0, 0).await;
    // 添加默认规则：其他流量使用代理
    domain_rule_matcher.set_domain_rule_obj(&String::new(), 10, 2, 0).await;

    // 创建测试用的隧道映射信息
    let mut info = TunnelMapperInfo {
        protocol: PackageProtocol::TCP,
        source_addr: String::new(),
        source_port: 62472,
        target_addr: "*************".to_string(), // 测试IP地址
        target_port: 4563,
        fake_target_addr: String::new(),
        fake_target_port: 0,
        process_name: String::new(),
        matcher_name: String::new(),
        matcher_rule: String::new(),
        proxy_type: ProxyType::Direct,
        active_time: 0,
        temp_data: vec![],
        direct_conn_priority: false,
        traffic_info: Arc::new(Default::default()),
    };

    // 执行匹配并打印结果
    domain_rule_matcher.match_domain(&mut info).await;
    println!("{:?}", info);
}

/// 规则配置文件测试
/// 测试从JSON配置文件加载规则并进行匹配的功能
#[tokio::test]
async fn test_rule_config() {
    use std::io::Read;
    use std::sync::Arc;
    use std::fs::File;
    use serde_json::Value;

    // 创建域名规则匹配器
    let domain_rule_matcher = DomainRuleMatcher::new();
    domain_rule_matcher.set_proxy_type(3).await;

    // 读取配置文件
    let mut rule = String::new();
    File::open("C:\\Users\\<USER>\\Downloads\\config.json")
        .unwrap()
        .read_to_string(&mut rule)
        .unwrap();

    // 解析JSON配置
    let v: Value = serde_json::from_str(&rule).unwrap();
    let custom_rules = v.get("subscribeInfo")
        .and_then(|info| info.get("customRule"))
        .unwrap();

    // 遍历自定义规则并添加到匹配器中
    if let Value::Array(items) = custom_rules {
        for item in items {
            if let Value::Object(map) = item {
                let domain = map.get("domain")
                    .and_then(|v| v.as_str())
                    .unwrap_or("")
                    .to_string();
                let matching = map.get("matching")
                    .and_then(|v| v.as_i64())
                    .unwrap_or(0) as i32;
                let proxy_type = map.get("proxyType")
                    .and_then(|v| v.as_i64())
                    .unwrap_or(0) as i32;

                domain_rule_matcher.set_domain_rule_obj(&domain, matching, proxy_type, 0).await;
            }
        }
    }

    // 创建测试用的隧道映射信息
    let mut info = TunnelMapperInfo {
        protocol: PackageProtocol::TCP,
        source_addr: String::new(),
        source_port: 62472,
        target_addr: "weixinbridge.com".to_string(), // 测试域名
        target_port: 4563,
        fake_target_addr: String::new(),
        fake_target_port: 0,
        process_name: String::new(),
        matcher_name: String::new(),
        matcher_rule: String::new(),
        proxy_type: ProxyType::Direct,
        active_time: 0,
        temp_data: vec![],
        direct_conn_priority: true,
        traffic_info: Arc::new(Default::default()),
    };

    // 执行匹配并打印结果
    domain_rule_matcher.match_domain(&mut info).await;
    println!("{:?}", info);
}