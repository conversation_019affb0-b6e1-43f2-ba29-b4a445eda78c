use std::collections::HashMap;
use std::net::{IpAddr, Ipv4Addr};

use anyhow::{anyhow, Result};
use log::debug;
use tokio::sync::RwLock;
use trust_dns_proto::op::{
    header::MessageType, op_code::OpCode, response_code::ResponseCode, Message,
};
use trust_dns_proto::rr::{
    dns_class::DNSClass, rdata, record_data::RData, record_type::RecordType, resource::Record,
};

/// 虚假 DNS 过滤模式
///
/// 定义了虚假 DNS 服务如何处理域名过滤的两种模式：
/// - `Include`: 包含模式 - 只有匹配过滤器的域名才会被处理
/// - `Exclude`: 排除模式 - 除了匹配过滤器的域名外，其他域名都会被处理
#[derive(Debug, Clone)]
pub enum FakeDnsMode {
    /// 包含模式：只处理匹配过滤器的域名
    Include,
    /// 排除模式：处理所有域名，除了匹配过滤器的域名
    Exclude,
}

/// 虚假 DNS 服务
///
/// 这是虚假 DNS 系统的核心组件，负责：
/// - 为域名分配虚假 IP 地址（使用 **********/16 网段）
/// - 维护域名与虚假 IP 的双向映射关系
/// - 生成虚假的 DNS 响应数据包
/// - 管理域名过滤规则
///
/// # 线程安全
///
/// 内部使用 `RwLock` 保护共享状态，支持多线程并发访问。
/// 读操作（查询）可以并发执行，写操作（分配 IP、生成响应）会独占访问。
pub struct FakeDns(RwLock<FakeDnsImpl>);

impl FakeDns {
    /// 创建一个新的虚假 DNS 服务实例
    ///
    /// # 参数
    /// - `mode`: 过滤模式，决定如何处理域名过滤
    ///
    /// # 返回值
    /// 返回一个新的 `FakeDns` 实例
    pub fn new(mode: FakeDnsMode) -> Self {
        Self(RwLock::new(FakeDnsImpl::new(mode)))
    }

    /// 添加域名过滤器
    ///
    /// 根据配置的过滤模式，过滤器可以用于包含或排除特定的域名。
    ///
    /// # 参数
    /// - `filter`: 过滤器字符串，支持通配符 "*" 匹配所有域名
    ///
    /// # 示例
    /// ```rust
    /// let fake_dns = FakeDns::new(FakeDnsMode::Exclude);
    /// fake_dns.add_filter("example.com".to_string()).await;  // 排除 example.com
    /// fake_dns.add_filter("*.local".to_string()).await;      // 排除所有 .local 域名
    /// ```
    pub async fn add_filter(&self, filter: String) {
        self.0.write().await.add_filter(filter)
    }

    /// 根据虚假 IP 地址查询对应的域名
    ///
    /// # 参数
    /// - `ip`: 要查询的 IP 地址
    ///
    /// # 返回值
    /// - `Some(String)`: 如果找到对应的域名
    /// - `None`: 如果 IP 不是虚假 IP 或未找到对应域名
    pub async fn query_domain(&self, ip: &IpAddr) -> Option<String> {
        self.0.read().await.query_domain(ip)
    }

    /// 根据域名查询对应的虚假 IP 地址
    ///
    /// # 参数
    /// - `domain`: 要查询的域名
    ///
    /// # 返回值
    /// - `Some(IpAddr)`: 如果域名已分配虚假 IP
    /// - `None`: 如果域名尚未分配虚假 IP
    pub async fn query_fake_ip(&self, domain: &str) -> Option<IpAddr> {
        self.0.read().await.query_fake_ip(domain)
    }

    /// 为 DNS 请求生成虚假响应
    ///
    /// 解析 DNS 请求，为其中的域名分配虚假 IP，并生成相应的 DNS 响应数据包。
    ///
    /// # 参数
    /// - `request`: 原始 DNS 请求的字节数据
    ///
    /// # 返回值
    /// - `Ok(Vec<u8>)`: 生成的 DNS 响应数据包
    /// - `Err(anyhow::Error)`: 处理失败时的错误
    pub async fn generate_fake_response(&self, request: &[u8]) -> Result<Vec<u8>> {
        self.0.write().await.generate_fake_response(request)
    }

    /// 检查给定的 IP 地址是否为虚假 IP
    ///
    /// # 参数
    /// - `ip`: 要检查的 IP 地址
    ///
    /// # 返回值
    /// 如果 IP 地址在虚假 IP 范围内（**********/16）则返回 `true`
    pub async fn is_fake_ip(&self, ip: &IpAddr) -> bool {
        self.0.read().await.is_fake_ip(ip)
    }
}

/// 虚假 DNS 服务的内部实现
///
/// 这个结构体包含了虚假 DNS 服务的所有状态和核心逻辑。
/// 使用 **********/16 网段作为虚假 IP 地址池。
pub(self) struct FakeDnsImpl {
    /// IP 地址到域名的映射表（使用 u32 表示 IPv4 地址以提高性能）
    ip_to_domain: HashMap<u32, String>,
    /// 域名到 IP 地址的映射表
    domain_to_ip: HashMap<String, u32>,
    /// 当前 IP 分配游标，指向下一个要分配的 IP 地址
    cursor: u32,
    /// IP 地址池的最小值（**********）
    min_cursor: u32,
    /// IP 地址池的最大值（**************）
    max_cursor: u32,
    /// DNS 响应的 TTL（生存时间）值，单位为秒
    ttl: u32,
    /// 域名过滤器列表
    filters: Vec<String>,
    /// 过滤模式（包含或排除）
    mode: FakeDnsMode,
}

impl FakeDnsImpl {
    /// 创建一个新的虚假 DNS 实现实例
    ///
    /// # 参数
    /// - `mode`: 过滤模式
    ///
    /// # 虚假 IP 地址范围
    /// 使用 **********/16 网段，这是 RFC 2544 定义的测试网络地址段，
    /// 不会与真实的互联网地址冲突。
    pub(self) fn new(mode: FakeDnsMode) -> Self {
        // 计算 **********/16 网段的起始和结束地址
        let min_cursor = Self::ip_to_u32(&Ipv4Addr::new(198, 18, 0, 0));
        let max_cursor = Self::ip_to_u32(&Ipv4Addr::new(198, 18, 255, 255));

        Self {
            ip_to_domain: HashMap::new(),
            domain_to_ip: HashMap::new(),
            cursor: min_cursor,
            min_cursor,
            max_cursor,
            ttl: 1, // 设置较短的 TTL 以避免缓存问题
            filters: Vec::new(),
            mode,
        }
    }

    /// 添加域名过滤器
    ///
    /// # 参数
    /// - `filter`: 过滤器字符串
    pub(self) fn add_filter(&mut self, filter: String) {
        self.filters.push(filter);
    }

    /// 根据 IP 地址查询对应的域名
    ///
    /// # 参数
    /// - `ip`: 要查询的 IP 地址
    ///
    /// # 返回值
    /// 如果找到对应的域名则返回 `Some(String)`，否则返回 `None`
    ///
    /// # 注意
    /// 只支持 IPv4 地址，IPv6 地址会直接返回 `None`
    pub(self) fn query_domain(&self, ip: &IpAddr) -> Option<String> {
        // 只处理 IPv4 地址
        let ipv4 = match ip {
            IpAddr::V4(ip) => ip,
            _ => return None,
        };

        // 在映射表中查找对应的域名
        self.ip_to_domain.get(&Self::ip_to_u32(ipv4)).cloned()
    }

    /// 根据域名查询对应的虚假 IP 地址
    ///
    /// # 参数
    /// - `domain`: 要查询的域名
    ///
    /// # 返回值
    /// 如果域名已分配虚假 IP 则返回 `Some(IpAddr)`，否则返回 `None`
    pub(self) fn query_fake_ip(&self, domain: &str) -> Option<IpAddr> {
        self.domain_to_ip
            .get(domain)
            .map(|&ip_u32| IpAddr::V4(Self::u32_to_ip(ip_u32)))
    }

    /// 为 DNS 请求生成虚假响应
    ///
    /// 这是虚假 DNS 服务的核心方法，负责：
    /// 1. 解析 DNS 请求数据包
    /// 2. 验证请求的有效性
    /// 3. 为域名分配或查找虚假 IP 地址
    /// 4. 构造并返回 DNS 响应数据包
    ///
    /// # 参数
    /// - `request`: 原始 DNS 请求的字节数据
    ///
    /// # 返回值
    /// - `Ok(Vec<u8>)`: 生成的 DNS 响应数据包
    /// - `Err(anyhow::Error)`: 处理失败时的错误
    ///
    /// # 支持的查询类型
    /// - A 记录（IPv4 地址）
    /// - AAAA 记录（IPv6 地址，但会返回空响应）
    /// - HTTPS 记录
    ///
    /// # 错误情况
    /// - DNS 请求格式无效
    /// - 请求中没有查询
    /// - 不支持的查询类或记录类型
    /// - 域名被过滤器拒绝
    /// - IP 地址分配失败
    pub(self) fn generate_fake_response(&mut self, request: &[u8]) -> Result<Vec<u8>> {
        // 解析 DNS 请求消息
        let req = Message::from_vec(request)?;

        // 检查请求是否包含查询
        if req.queries().is_empty() {
            return Err(anyhow!("DNS 请求中没有查询内容"));
        }

        // 获取第一个查询（通常 DNS 请求只包含一个查询）
        let query = &req.queries()[0];

        // 检查查询类是否为 IN（Internet）
        if query.query_class() != DNSClass::IN {
            return Err(anyhow!("不支持的查询类: {}", query.query_class()));
        }

        // 检查查询类型是否受支持
        let query_type = query.query_type();
        if query_type != RecordType::A
            && query_type != RecordType::AAAA
            && query_type != RecordType::HTTPS {
            return Err(anyhow!(
                "不支持的查询记录类型: {:?}",
                query.query_type()
            ));
        }

        let raw_name = query.name();

        // TODO check if a valid domain
        // 提取域名，处理 FQDN（完全限定域名）格式
        // FQDN 以点号结尾，需要去掉最后的点号
        let domain = if raw_name.is_fqdn() {
            let fqdn = raw_name.to_ascii();
            fqdn[..fqdn.len() - 1].to_string()
        } else {
            raw_name.to_ascii()
        };

        // 检查域名是否被过滤器接受
        if !self.accept(&domain) {
            return Err(anyhow!("域名 {} 被过滤器拒绝", domain));
        }

        // 获取或分配虚假 IP 地址
        let fake_ipv4 = if let Some(existing_ip) = self.query_fake_ip(&domain) {
            // 域名已有对应的虚假 IP，直接使用
            match existing_ip {
                IpAddr::V4(ipv4) => ipv4,
                _ => return Err(anyhow!("意外的 IPv6 虚假 IP 地址")),
            }
        } else {
            // 为域名分配新的虚假 IP 地址
            let allocated_ip = self.allocate_ip(&domain)?;
            debug!("为域名 {} 分配虚假 IP: {}", &domain, &allocated_ip);
            allocated_ip
        };

        // 构造 DNS 响应消息
        let mut response = Message::new();

        // 根据请求设置响应的基本属性
        // 参考: https://github.com/miekg/dns/blob/f515aa579d28efa1af67d9a62cc57f2dfe59da76/defaults.go#L15
        response
            .set_id(req.id())                           // 使用与请求相同的 ID
            .set_message_type(MessageType::Response)    // 设置为响应类型
            .set_op_code(req.op_code());               // 复制请求的操作码

        // 如果是查询操作，复制相关标志位
        if response.op_code() == OpCode::Query {
            response
                .set_recursion_desired(req.recursion_desired())
                .set_checking_disabled(req.checking_disabled());
        }

        // 设置响应码为无错误
        response.set_response_code(ResponseCode::NoError);

        // 将原始查询添加到响应中（DNS 协议要求）
        if !req.queries().is_empty() {
            response.add_query(query.clone());
        }

        // 只为 A 记录查询添加答案（IPv4 地址）
        // 对于 AAAA 和 HTTPS 查询，返回空响应
        if query.query_type() == RecordType::A {
            let mut answer_record = Record::new();
            answer_record
                .set_name(raw_name.clone())              // 设置记录名称
                .set_rr_type(RecordType::A)             // 设置记录类型为 A
                .set_ttl(self.ttl)                      // 设置 TTL
                .set_dns_class(DNSClass::IN)            // 设置 DNS 类为 IN
                .set_data(Some(RData::A(rdata::A(fake_ipv4)))); // 设置 IP 地址数据

            response.add_answer(answer_record);
        }

        // 将响应消息序列化为字节数组
        Ok(response.to_vec()?)
    }

    /// 检查给定的 IP 地址是否为虚假 IP
    ///
    /// # 参数
    /// - `ip`: 要检查的 IP 地址
    ///
    /// # 返回值
    /// 如果 IP 地址在虚假 IP 范围内（**********/16）则返回 `true`
    ///
    /// # 注意
    /// 只有 IPv4 地址会被认为是虚假 IP，IPv6 地址总是返回 `false`
    pub(self) fn is_fake_ip(&self, ip: &IpAddr) -> bool {
        // 只处理 IPv4 地址
        let ipv4 = match ip {
            IpAddr::V4(ip) => ip,
            _ => return false,
        };

        // 检查 IP 是否在虚假 IP 地址范围内
        let ip_u32 = Self::ip_to_u32(ipv4);
        ip_u32 >= self.min_cursor && ip_u32 <= self.max_cursor
    }

    /// 为域名分配一个新的虚假 IP 地址
    ///
    /// 此方法会：
    /// 1. 使用当前游标位置的 IP 地址
    /// 2. 更新双向映射表
    /// 3. 准备下一个可用的游标位置
    ///
    /// # 参数
    /// - `domain`: 要分配 IP 的域名
    ///
    /// # 返回值
    /// - `Ok(Ipv4Addr)`: 成功分配的 IPv4 地址
    /// - `Err(anyhow::Error)`: 分配失败时的错误
    ///
    /// # 注意
    /// 如果当前游标位置已被其他域名占用，会自动清理旧的映射关系
    fn allocate_ip(&mut self, domain: &str) -> Result<Ipv4Addr> {
        // 在 IP 到域名的映射表中插入新记录
        // 如果该 IP 之前被其他域名占用，会返回旧域名
        if let Some(previous_domain) = self.ip_to_domain.insert(self.cursor, domain.to_owned()) {
            // 清理反向映射表中的旧记录，确保不会有多个域名指向同一个 IP
            self.domain_to_ip.remove(&previous_domain);
        }

        // 在域名到 IP 的映射表中插入新记录
        self.domain_to_ip.insert(domain.to_owned(), self.cursor);

        // 将当前游标转换为 IP 地址
        let allocated_ip = Self::u32_to_ip(self.cursor);

        // 准备下一个可用的游标位置
        self.prepare_next_cursor()?;

        Ok(allocated_ip)
    }

    /// 准备下一个可用的游标位置
    ///
    /// 此方法确保 `self.cursor` 指向一个有效的、可以立即用于下一个虚假 IP 分配的位置。
    /// 它会避开网络地址和广播地址（最后一个八位组为 0 或 255 的地址）。
    ///
    /// # 返回值
    /// - `Ok(())`: 成功找到下一个有效位置
    /// - `Err(anyhow::Error)`: 无法找到有效位置（理论上不应该发生）
    ///
    /// # 算法
    /// 1. 游标递增
    /// 2. 如果超出最大值，回绕到最小值
    /// 3. 检查是否为网络或广播地址，如果是则继续寻找
    /// 4. 最多尝试 3 次，避免无限循环
    fn prepare_next_cursor(&mut self) -> Result<()> {
        // 最多尝试 3 次寻找有效的游标位置
        for _ in 0..3 {
            self.cursor += 1;

            // 如果游标超出最大值，回绕到最小值（循环分配）
            if self.cursor > self.max_cursor {
                self.cursor = self.min_cursor;
            }

            // 避开网络地址和广播地址
            // 检查 IP 地址的最后一个八位组是否为 0 或 255
            match Self::u32_to_ip(self.cursor).octets()[3] {
                0 | 255 => {
                    // 跳过网络地址和广播地址
                    continue;
                }
                _ => {
                    // 找到有效的地址
                    return Ok(());
                }
            }
        }

        // 理论上不应该到达这里，因为 **********/16 网段有足够的可用地址
        Err(anyhow!("无法准备下一个游标位置"))
    }

    /// 检查域名是否被过滤器接受
    ///
    /// 根据配置的过滤模式和过滤器列表，决定是否接受给定的域名。
    ///
    /// # 参数
    /// - `domain`: 要检查的域名
    ///
    /// # 返回值
    /// 如果域名被接受则返回 `true`，否则返回 `false`
    ///
    /// # 过滤逻辑
    /// - **排除模式**: 默认接受所有域名，除非匹配过滤器
    /// - **包含模式**: 默认拒绝所有域名，除非匹配过滤器
    /// - 支持通配符 "*" 匹配所有域名
    /// - 使用字符串包含匹配（不是精确匹配）
    fn accept(&self, domain: &str) -> bool {
        match self.mode {
            FakeDnsMode::Exclude => {
                // 排除模式：检查域名是否被任何过滤器排除
                for filter in &self.filters {
                    if domain.contains(filter) || filter == "*" {
                        return false; // 域名被排除
                    }
                }
                true // 域名未被排除，接受
            }
            FakeDnsMode::Include => {
                // 包含模式：检查域名是否匹配任何过滤器
                for filter in &self.filters {
                    if domain.contains(filter) || filter == "*" {
                        return true; // 域名匹配过滤器，接受
                    }
                }
                false // 域名不匹配任何过滤器，拒绝
            }
        }
    }

    /// 将 u32 整数转换为 IPv4 地址
    ///
    /// # 参数
    /// - `ip`: 表示 IP 地址的 32 位无符号整数
    ///
    /// # 返回值
    /// 对应的 `Ipv4Addr` 实例
    ///
    /// # 注意
    /// 使用网络字节序（大端序）进行转换
    fn u32_to_ip(ip: u32) -> Ipv4Addr {
        Ipv4Addr::from(ip)
    }

    /// 将 IPv4 地址转换为 u32 整数
    ///
    /// # 参数
    /// - `ip`: 要转换的 IPv4 地址
    ///
    /// # 返回值
    /// 表示该 IP 地址的 32 位无符号整数
    ///
    /// # 注意
    /// 使用网络字节序（大端序）进行转换，这样可以直接进行数值比较
    fn ip_to_u32(ip: &Ipv4Addr) -> u32 {
        u32::from_be_bytes(ip.octets())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::Ipv4Addr;

    /// 测试 u32 到 IPv4 地址的转换
    ///
    /// 验证 `u32_to_ip` 方法能正确将 32 位整数转换为 IPv4 地址
    #[test]
    fn test_u32_to_ip() {
        // 测试 localhost 地址 (127.0.0.1)
        let expected_ip = Ipv4Addr::new(127, 0, 0, 1);
        let converted_ip = FakeDnsImpl::u32_to_ip(2130706433u32);
        assert_eq!(expected_ip, converted_ip);

        // 测试虚假 IP 范围的起始地址 (**********)
        let fake_ip_start = Ipv4Addr::new(198, 18, 0, 0);
        let fake_ip_start_u32 = (198u32 << 24) | (18u32 << 16);
        let converted_fake_ip = FakeDnsImpl::u32_to_ip(fake_ip_start_u32);
        assert_eq!(fake_ip_start, converted_fake_ip);
    }

    /// 测试 IPv4 地址到 u32 的转换
    ///
    /// 验证 `ip_to_u32` 方法能正确将 IPv4 地址转换为 32 位整数
    #[test]
    fn test_ip_to_u32() {
        // 测试 localhost 地址 (127.0.0.1)
        let ip = Ipv4Addr::new(127, 0, 0, 1);
        let converted_u32 = FakeDnsImpl::ip_to_u32(&ip);
        let expected_u32 = 2130706433u32;
        assert_eq!(converted_u32, expected_u32);

        // 测试虚假 IP 范围的结束地址 (**************)
        let fake_ip_end = Ipv4Addr::new(198, 18, 255, 255);
        let converted_u32 = FakeDnsImpl::ip_to_u32(&fake_ip_end);
        let expected_u32 = (198u32 << 24) | (18u32 << 16) | (255u32 << 8) | 255u32;
        assert_eq!(converted_u32, expected_u32);
    }

    /// 测试 IP 地址转换的双向一致性
    ///
    /// 验证 IP 地址与 u32 之间的转换是可逆的
    #[test]
    fn test_ip_conversion_roundtrip() {
        let original_ip = Ipv4Addr::new(192, 168, 1, 100);
        let u32_value = FakeDnsImpl::ip_to_u32(&original_ip);
        let converted_back = FakeDnsImpl::u32_to_ip(u32_value);
        assert_eq!(original_ip, converted_back);
    }

    /// 测试虚假 IP 检测功能
    #[test]
    fn test_is_fake_ip() {
        let fake_dns_impl = FakeDnsImpl::new(FakeDnsMode::Exclude);

        // 测试虚假 IP 范围内的地址
        let fake_ip = IpAddr::V4(Ipv4Addr::new(198, 18, 1, 1));
        assert!(fake_dns_impl.is_fake_ip(&fake_ip));

        // 测试虚假 IP 范围外的地址
        let real_ip = IpAddr::V4(Ipv4Addr::new(8, 8, 8, 8));
        assert!(!fake_dns_impl.is_fake_ip(&real_ip));

        // 测试 IPv6 地址（应该返回 false）
        let ipv6 = "2001:db8::1".parse::<IpAddr>().unwrap();
        assert!(!fake_dns_impl.is_fake_ip(&ipv6));
    }

    /// 测试域名过滤功能
    #[test]
    fn test_domain_filtering() {
        // 测试排除模式
        let mut exclude_impl = FakeDnsImpl::new(FakeDnsMode::Exclude);
        exclude_impl.add_filter("blocked.com".to_string());

        assert!(exclude_impl.accept("example.com"));      // 应该接受
        assert!(!exclude_impl.accept("blocked.com"));     // 应该拒绝
        assert!(!exclude_impl.accept("sub.blocked.com")); // 应该拒绝（包含匹配）

        // 测试包含模式
        let mut include_impl = FakeDnsImpl::new(FakeDnsMode::Include);
        include_impl.add_filter("allowed.com".to_string());

        assert!(!include_impl.accept("example.com"));      // 应该拒绝
        assert!(include_impl.accept("allowed.com"));       // 应该接受
        assert!(include_impl.accept("sub.allowed.com"));   // 应该接受（包含匹配）

        // 测试通配符
        let mut wildcard_impl = FakeDnsImpl::new(FakeDnsMode::Include);
        wildcard_impl.add_filter("*".to_string());
        assert!(wildcard_impl.accept("any.domain.com"));   // 通配符应该匹配所有域名
    }
}
