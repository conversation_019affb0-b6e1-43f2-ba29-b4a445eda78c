use std::net::IpAddr;

use flyshadow_common::interface::interface_selector::InterfaceSelector;
use flyshadow_common::util::dns_util::DnsUtil;
use tokio::sync::RwLock;

use crate::fake_ip::fake_dns::{FakeDns, FakeDnsMode};

/// 虚假 IP 地址缓存上下文
///
/// 这个结构体管理虚假 IP 地址的分配、缓存和 DNS 查询功能。
/// 它是虚假 IP 系统的主要入口点，提供了以下核心功能：
///
/// - 启用/禁用虚假 IP 功能
/// - 根据虚假 IP 地址查询对应的域名
/// - 生成虚假的 DNS 响应
/// - 执行真实的 DNS 查询
///
/// # 线程安全
///
/// 所有方法都是异步的且线程安全的，可以在多线程环境中安全使用。
pub struct FakeIpCacheContext {
    /// 虚假 IP 功能是否启用的标志
    enable: RwLock<bool>,
    /// 虚假 DNS 服务实例，负责核心的 IP 分配和 DNS 响应生成
    fake_dns: FakeDns,
    /// DNS 工具实例，用于执行真实的 DNS 查询
    dns_util: DnsUtil,
}

impl FakeIpCacheContext {
    /// 创建一个新的虚假 IP 地址上下文实例
    ///
    /// # 默认配置
    /// - 虚假 IP 功能默认禁用
    /// - 使用排除模式的虚假 DNS（默认接受所有域名，除非被过滤器排除）
    /// - 启用 DNS 缓存以提高查询性能
    ///
    /// # 返回值
    /// 返回一个新的 `FakeIpCacheContext` 实例
    pub fn new() -> Self {
        Self {
            enable: RwLock::new(false),
            fake_dns: FakeDns::new(FakeDnsMode::Exclude),
            dns_util: DnsUtil::new(true),
        }
    }

    /// 设置虚假 IP 功能的启用状态
    ///
    /// # 参数
    /// - `enable`: 如果为 `true` 则启用虚假 IP 功能，否则禁用
    ///
    /// # 示例
    /// ```rust
    /// let context = FakeIpCacheContext::new();
    /// context.set_fake_ip(true).await;  // 启用虚假 IP
    /// context.set_fake_ip(false).await; // 禁用虚假 IP
    /// ```
    pub async fn set_fake_ip(&self, enable: bool) {
        *self.enable.write().await = enable;
    }

    /// 获取虚假 IP 功能的当前启用状态
    ///
    /// # 返回值
    /// 如果虚假 IP 功能已启用则返回 `true`，否则返回 `false`
    pub async fn get_enable(&self) -> bool {
        *self.enable.read().await
    }


    /// 根据虚假 IP 地址查询对应的原始域名
    ///
    /// 此方法用于反向查询，即根据之前分配的虚假 IP 地址找回对应的域名。
    /// 这在处理网络流量时非常有用，可以将虚假 IP 还原为真实的域名。
    ///
    /// # 参数
    /// - `fake_ip`: 要查询的虚假 IP 地址字符串
    ///
    /// # 返回值
    /// - `Some(String)`: 如果找到对应的域名，返回域名字符串
    /// - `None`: 如果 IP 地址无效、不是虚假 IP 或未找到对应域名
    ///
    /// # 示例
    /// ```rust
    /// let context = FakeIpCacheContext::new();
    /// if let Some(domain) = context.get_domain_by_fake_ip(&"**********".to_string()).await {
    ///     println!("虚假 IP ********** 对应的域名是: {}", domain);
    /// }
    /// ```
    pub async fn get_domain_by_fake_ip(&self, fake_ip: &str) -> Option<String> {
        // 尝试解析 IP 地址字符串
        let ip_addr = fake_ip.parse::<IpAddr>().ok()?;

        // 检查是否为虚假 IP 并查询对应域名
        if self.fake_dns.is_fake_ip(&ip_addr).await {
            self.fake_dns.query_domain(&ip_addr).await
        } else {
            None
        }
    }

    /// 为 DNS 请求生成虚假的 DNS 响应
    ///
    /// 此方法接收原始的 DNS 查询数据包，解析其中的域名，为其分配一个虚假 IP 地址，
    /// 然后构造并返回相应的 DNS 响应数据包。
    ///
    /// # 参数
    /// - `data`: 原始 DNS 查询请求的字节数据
    ///
    /// # 返回值
    /// - `Ok(Vec<u8>)`: 成功生成的 DNS 响应数据包
    /// - `Err(anyhow::Error)`: 生成失败时的错误信息
    ///
    /// # 错误情况
    /// - DNS 请求格式无效
    /// - 不支持的查询类型或记录类型
    /// - 域名被过滤器拒绝
    /// - IP 地址分配失败
    pub async fn generate_fake_response(&self, data: &[u8]) -> anyhow::Result<Vec<u8>> {
        self.fake_dns.generate_fake_response(data).await
    }

    /// 执行真实的 DNS 查询
    ///
    /// 此方法用于查询域名的真实 IP 地址，通常在需要建立实际网络连接时使用。
    /// 它会使用配置的网络接口和 DNS 服务器进行查询。
    ///
    /// # 参数
    /// - `interface_selector`: 可选的网络接口选择器，用于指定查询使用的网络接口
    /// - `domain`: 要查询的域名
    ///
    /// # 返回值
    /// - `Ok(Vec<IpAddr>)`: 查询成功，返回域名对应的 IP 地址列表
    /// - `Err(String)`: 查询失败时的错误描述
    ///
    /// # 注意
    /// 此方法会使用内置的 DNS 缓存来提高查询性能，避免重复查询相同域名。
    pub async fn query_ip(
        &self,
        interface_selector: Option<InterfaceSelector>,
        domain: &str,
    ) -> Result<Vec<IpAddr>, String> {
        // 将 &str 转换为 String 以匹配 dns_util.query_ip 的参数类型
        self.dns_util.query_ip(interface_selector, &domain.to_string()).await
    }
}