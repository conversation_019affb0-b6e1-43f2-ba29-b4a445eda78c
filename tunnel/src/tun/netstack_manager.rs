//! # 网络栈管理器模块
//!
//! 本模块提供网络栈的创建、管理和生命周期控制功能，
//! 封装了 NetStack 的复杂性，提供简洁的接口。

use std::pin::Pin;
use std::sync::Arc;

use futures_util::stream::{SplitSink, SplitStream};
use futures_util::StreamExt;
use log::debug;
use netstack_lwip::{NetStack, TcpListener, UdpSocket};

use super::config::TunConfig;
use super::error::{TunError, TunResult};

/// 网络栈管理器
///
/// 负责管理网络栈的生命周期，包括创建、配置和销毁
pub struct NetStackManager {
    /// TUN 配置
    config: TunConfig,
}

/// 网络栈实例
///
/// 包含网络栈的各个组件，提供统一的接口
pub struct NetStackInstance {
    /// TCP 监听器
    pub tcp_listener: Pin<Box<TcpListener>>,
    /// UDP 套接字
    pub udp_socket: Pin<Box<UdpSocket>>,
    /// 网络栈发送端
    pub stack_sink: SplitSink<Pin<Box<NetStack>>, Vec<u8>>,
    /// 网络栈接收端
    pub stack_stream: SplitStream<Pin<Box<NetStack>>>,
}

impl NetStackManager {
    /// 创建新的网络栈管理器
    ///
    /// # 参数
    /// * `config` - TUN 配置
    ///
    /// # 返回值
    /// 返回新的网络栈管理器实例
    pub fn new(config: TunConfig) -> Self {
        Self { config }
    }

    /// 创建网络栈实例
    ///
    /// # 返回值
    /// 返回配置好的网络栈实例，如果创建失败则返回错误
    pub fn create_netstack(&self) -> TunResult<NetStackInstance> {
        debug!(
            "创建网络栈，TCP缓冲区: {}, UDP缓冲区: {}",
            self.config.netstack_tcp_buffer_size, self.config.netstack_udp_buffer_size
        );

        // 创建网络栈
        let (stack, tcp_listener, udp_socket) = NetStack::with_buffer_size(
            self.config.netstack_tcp_buffer_size,
            self.config.netstack_udp_buffer_size,
        )
            .map_err(|e| TunError::NetStackInitError(format!("网络栈创建失败: {}", e)))?;

        // 分离网络栈的发送和接收端
        let (stack_sink, stack_stream) = stack.split();

        debug!("网络栈创建成功");

        Ok(NetStackInstance {
            tcp_listener,
            udp_socket,
            stack_sink,
            stack_stream,
        })
    }

    /// 启动网络栈数据处理任务
    ///
    /// # 参数
    /// * `netstack` - 网络栈实例
    ///
    /// # 返回值
    /// 返回网络栈组件，包括 TCP 监听器、UDP 套接字、发送端和接收端
    pub async fn start_data_processing(
        &self,
        netstack: NetStackInstance,
    ) -> TunResult<(
        Pin<Box<netstack_lwip::TcpListener>>,
        Pin<Box<netstack_lwip::UdpSocket>>,
        SplitSink<Pin<Box<NetStack>>, Vec<u8>>,
        SplitStream<Pin<Box<NetStack>>>,
    )> {
        let NetStackInstance {
            tcp_listener,
            udp_socket,
            stack_sink,
            stack_stream,
        } = netstack;

        debug!("网络栈数据处理任务启动完成");
        Ok((tcp_listener, udp_socket, stack_sink, stack_stream))
    }

    /// 获取配置信息
    ///
    /// # 返回值
    /// 返回当前的 TUN 配置
    pub fn config(&self) -> &TunConfig {
        &self.config
    }

    /// 更新配置
    ///
    /// # 参数
    /// * `config` - 新的配置
    pub fn update_config(&mut self, config: TunConfig) {
        debug!("更新网络栈管理器配置");
        self.config = config;
    }

    /// 验证配置有效性
    ///
    /// # 返回值
    /// 如果配置有效则返回 `Ok(())`，否则返回配置错误
    pub fn validate_config(&self) -> TunResult<()> {
        if self.config.netstack_tcp_buffer_size == 0 {
            return Err(TunError::ConfigurationError(
                "TCP缓冲区大小不能为0".to_string(),
            ));
        }

        if self.config.netstack_udp_buffer_size == 0 {
            return Err(TunError::ConfigurationError(
                "UDP缓冲区大小不能为0".to_string(),
            ));
        }

        debug!("网络栈配置验证通过");
        Ok(())
    }

    /// 获取网络栈统计信息
    ///
    /// # 返回值
    /// 返回网络栈的统计信息
    pub fn get_stats(&self) -> NetStackStats {
        NetStackStats {
            tcp_buffer_size: self.config.netstack_tcp_buffer_size,
            udp_buffer_size: self.config.netstack_udp_buffer_size,
            dns_blocking_enabled: self.config.enable_dns_blocking,
            custom_blocked_dns_count: self.config.custom_blocked_dns_addresses.len(),
        }
    }
}

/// 网络栈统计信息
///
/// 包含网络栈的配置和运行时统计信息
#[derive(Debug, Clone)]
pub struct NetStackStats {
    /// TCP 缓冲区大小
    pub tcp_buffer_size: usize,
    /// UDP 缓冲区大小
    pub udp_buffer_size: usize,
    /// DNS 封锁是否启用
    pub dns_blocking_enabled: bool,
    /// 自定义封锁 DNS 地址数量
    pub custom_blocked_dns_count: usize,
}

impl std::fmt::Display for NetStackStats {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "NetStack Stats: TCP Buffer: {}, UDP Buffer: {}, DNS Blocking: {}, Custom DNS Blocks: {}",
            self.tcp_buffer_size,
            self.udp_buffer_size,
            self.dns_blocking_enabled,
            self.custom_blocked_dns_count
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_netstack_manager_creation() {
        let config = TunConfig::default();
        let manager = NetStackManager::new(config.clone());
        assert_eq!(manager.config().netstack_tcp_buffer_size, config.netstack_tcp_buffer_size);
        assert_eq!(manager.config().netstack_udp_buffer_size, config.netstack_udp_buffer_size);
    }

    #[test]
    fn test_config_validation() {
        let config = TunConfig::default();
        let manager = NetStackManager::new(config.clone());
        assert!(manager.validate_config().is_ok());

        let mut invalid_config = TunConfig::default();
        invalid_config.netstack_tcp_buffer_size = 0;
        let manager = NetStackManager::new(invalid_config);
        assert!(manager.validate_config().is_err());
    }

    #[test]
    fn test_stats() {
        let config = TunConfig::default()
            .with_netstack_buffer_size(1024, 512);
        let manager = NetStackManager::new(config);

        let stats = manager.get_stats();
        assert_eq!(stats.tcp_buffer_size, 1024);
        assert_eq!(stats.udp_buffer_size, 512);
        assert!(stats.dns_blocking_enabled);
    }

    #[test]
    fn test_config_update() {
        let config1 = TunConfig::default();
        let mut manager = NetStackManager::new(config1);

        let config2 = TunConfig::default().with_netstack_buffer_size(2048, 1024);
        manager.update_config(config2);

        assert_eq!(manager.config().netstack_tcp_buffer_size, 2048);
        assert_eq!(manager.config().netstack_udp_buffer_size, 1024);
    }
}
