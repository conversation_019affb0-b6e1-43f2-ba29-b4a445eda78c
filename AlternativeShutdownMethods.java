import java.io.*;
import java.net.Socket;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Java程序示例：使用不同方法优雅关闭flyshadow_mac_tun程序
 */
public class AlternativeShutdownMethods {
    
    /**
     * 方法1：通过创建文件来触发关闭
     */
    public static void shutdownByFile() {
        try {
            Path shutdownFile = Paths.get("/tmp/flyshadow_shutdown");
            Files.createFile(shutdownFile);
            System.out.println("已创建关闭文件，等待程序响应...");
        } catch (IOException e) {
            System.err.println("创建关闭文件失败: " + e.getMessage());
        }
    }
    
    /**
     * 方法2：通过Unix Domain Socket发送关闭命令
     */
    public static void shutdownBySocket() {
        String socketPath = "/tmp/flyshadow_control.sock";
        
        try {
            // 注意：Java标准库不直接支持Unix Domain Socket
            // 这里使用ProcessBuilder调用系统命令
            ProcessBuilder pb = new ProcessBuilder("bash", "-c", 
                "echo 'shutdown' | nc -U " + socketPath);
            Process process = pb.start();
            
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                System.out.println("已通过Unix Socket发送关闭命令");
            } else {
                System.err.println("发送关闭命令失败，退出码: " + exitCode);
            }
            
        } catch (IOException | InterruptedException e) {
            System.err.println("通过Socket发送关闭命令失败: " + e.getMessage());
        }
    }
    
    /**
     * 方法3：通过HTTP请求触发关闭
     */
    public static void shutdownByHttp() {
        try {
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create("http://127.0.0.1:8080/shutdown"))
                    .GET()
                    .build();
            
            HttpResponse<String> response = client.send(request, 
                    HttpResponse.BodyHandlers.ofString());
            
            if (response.statusCode() == 200) {
                System.out.println("已通过HTTP发送关闭命令: " + response.body());
            } else {
                System.err.println("HTTP关闭请求失败，状态码: " + response.statusCode());
            }
            
        } catch (Exception e) {
            System.err.println("通过HTTP发送关闭命令失败: " + e.getMessage());
        }
    }
    
    /**
     * 方法4：检查程序状态（通过HTTP）
     */
    public static boolean checkStatus() {
        try {
            HttpClient client = HttpClient.newHttpClient();
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create("http://127.0.0.1:8080/status"))
                    .GET()
                    .build();
            
            HttpResponse<String> response = client.send(request, 
                    HttpResponse.BodyHandlers.ofString());
            
            if (response.statusCode() == 200) {
                System.out.println("程序状态: " + response.body());
                return true;
            } else {
                System.out.println("无法获取程序状态");
                return false;
            }
            
        } catch (Exception e) {
            System.out.println("程序可能未运行或HTTP控制接口不可用");
            return false;
        }
    }
    
    /**
     * 综合示例：管理TUN进程的完整生命周期
     */
    public static class TunProcessManager {
        private Process tunProcess;
        private final String executablePath;
        
        public TunProcessManager(String executablePath) {
            this.executablePath = executablePath;
        }
        
        public void start() throws IOException {
            System.out.println("启动TUN进程...");
            ProcessBuilder pb = new ProcessBuilder(executablePath);
            pb.redirectErrorStream(true);
            tunProcess = pb.start();
            
            // 启动输出读取线程
            startOutputReader();
            
            System.out.println("TUN进程已启动，PID: " + tunProcess.pid());
        }
        
        private void startOutputReader() {
            Thread reader = new Thread(() -> {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(tunProcess.getInputStream()))) {
                    String line;
                    while ((line = br.readLine()) != null) {
                        System.out.println("[TUN] " + line);
                    }
                } catch (IOException e) {
                    if (tunProcess.isAlive()) {
                        System.err.println("读取输出失败: " + e.getMessage());
                    }
                }
            });
            reader.setDaemon(true);
            reader.start();
        }
        
        public void shutdown() {
            if (tunProcess == null || !tunProcess.isAlive()) {
                System.out.println("进程未运行");
                return;
            }
            
            System.out.println("尝试优雅关闭...");
            
            // 尝试多种关闭方法
            boolean success = false;
            
            // 方法1：HTTP关闭
            if (checkStatus()) {
                shutdownByHttp();
                success = waitForExit(5000);
            }
            
            // 方法2：文件关闭
            if (!success) {
                shutdownByFile();
                success = waitForExit(5000);
            }
            
            // 方法3：信号关闭
            if (!success) {
                tunProcess.destroy(); // SIGTERM
                success = waitForExit(10000);
            }
            
            // 方法4：强制关闭
            if (!success) {
                System.out.println("优雅关闭失败，强制终止进程");
                tunProcess.destroyForcibly();
                waitForExit(5000);
            }
        }
        
        private boolean waitForExit(long timeoutMs) {
            try {
                return tunProcess.waitFor(timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }
        
        public boolean isRunning() {
            return tunProcess != null && tunProcess.isAlive();
        }
    }
    
    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("用法:");
            System.out.println("  java AlternativeShutdownMethods file     - 通过文件关闭");
            System.out.println("  java AlternativeShutdownMethods socket   - 通过Socket关闭");
            System.out.println("  java AlternativeShutdownMethods http     - 通过HTTP关闭");
            System.out.println("  java AlternativeShutdownMethods status   - 检查状态");
            System.out.println("  java AlternativeShutdownMethods manage   - 完整管理示例");
            return;
        }
        
        String method = args[0].toLowerCase();
        
        switch (method) {
            case "file":
                shutdownByFile();
                break;
            case "socket":
                shutdownBySocket();
                break;
            case "http":
                shutdownByHttp();
                break;
            case "status":
                checkStatus();
                break;
            case "manage":
                // 完整管理示例
                TunProcessManager manager = new TunProcessManager("./flyshadow_mac_tun/target/release/flyshadow_mac_tun");
                try {
                    manager.start();
                    Thread.sleep(10000); // 运行10秒
                    manager.shutdown();
                } catch (Exception e) {
                    System.err.println("管理进程时出错: " + e.getMessage());
                }
                break;
            default:
                System.err.println("未知方法: " + method);
        }
    }
}
