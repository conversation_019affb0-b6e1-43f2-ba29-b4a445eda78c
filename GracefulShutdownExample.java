import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Java程序示例：优雅地启动和关闭flyshadow_mac_tun程序
 */
public class GracefulShutdownExample {
    private Process tunProcess;
    private Thread shutdownHook;
    
    public void startTunProcess() throws IOException {
        System.out.println("启动 flyshadow_mac_tun 进程...");
        
        ProcessBuilder pb = new ProcessBuilder("./flyshadow_mac_tun/target/release/flyshadow_mac_tun");
        pb.redirectErrorStream(true); // 合并错误流和输出流
        
        tunProcess = pb.start();
        
        // 添加JVM关闭钩子，确保在Java程序退出时优雅关闭子进程
        shutdownHook = new Thread(this::gracefulShutdown);
        Runtime.getRuntime().addShutdownHook(shutdownHook);
        
        // 启动线程读取子进程输出
        startOutputReader();
        
        System.out.println("flyshadow_mac_tun 进程已启动，PID: " + tunProcess.pid());
    }
    
    private void startOutputReader() {
        Thread outputReader = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(tunProcess.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("[TUN] " + line);
                }
            } catch (IOException e) {
                if (tunProcess.isAlive()) {
                    System.err.println("读取子进程输出时出错: " + e.getMessage());
                }
            }
        });
        outputReader.setDaemon(true);
        outputReader.start();
    }
    
    /**
     * 优雅关闭TUN进程
     */
    public void gracefulShutdown() {
        if (tunProcess != null && tunProcess.isAlive()) {
            System.out.println("正在优雅关闭 flyshadow_mac_tun 进程...");
            
            try {
                // 方法1：发送SIGTERM信号（推荐）
                tunProcess.destroy(); // 发送SIGTERM
                
                // 等待进程优雅退出，最多等待10秒
                boolean exited = tunProcess.waitFor(10, TimeUnit.SECONDS);
                
                if (exited) {
                    System.out.println("flyshadow_mac_tun 进程已优雅退出，退出码: " + tunProcess.exitValue());
                } else {
                    System.out.println("进程未在指定时间内退出，强制终止...");
                    tunProcess.destroyForcibly(); // 发送SIGKILL
                    tunProcess.waitFor(5, TimeUnit.SECONDS);
                }
                
            } catch (InterruptedException e) {
                System.err.println("等待进程退出时被中断: " + e.getMessage());
                tunProcess.destroyForcibly();
            }
        }
    }
    
    /**
     * 手动停止TUN进程（用于程序运行时主动停止）
     */
    public void stopTunProcess() {
        gracefulShutdown();
        
        // 移除关闭钩子，避免重复执行
        if (shutdownHook != null) {
            try {
                Runtime.getRuntime().removeShutdownHook(shutdownHook);
            } catch (IllegalStateException e) {
                // JVM已经在关闭过程中，忽略异常
            }
        }
    }
    
    /**
     * 检查TUN进程是否还在运行
     */
    public boolean isRunning() {
        return tunProcess != null && tunProcess.isAlive();
    }
    
    /**
     * 获取进程PID
     */
    public long getProcessId() {
        return tunProcess != null ? tunProcess.pid() : -1;
    }
    
    public static void main(String[] args) {
        GracefulShutdownExample example = new GracefulShutdownExample();
        
        try {
            // 启动TUN进程
            example.startTunProcess();
            
            // 模拟程序运行一段时间
            System.out.println("程序运行中... 按Ctrl+C或等待30秒后自动退出");
            Thread.sleep(30000);
            
            // 主动停止TUN进程
            example.stopTunProcess();
            
        } catch (IOException e) {
            System.err.println("启动进程失败: " + e.getMessage());
        } catch (InterruptedException e) {
            System.out.println("程序被中断");
            example.stopTunProcess();
        }
        
        System.out.println("Java程序退出");
    }
}
