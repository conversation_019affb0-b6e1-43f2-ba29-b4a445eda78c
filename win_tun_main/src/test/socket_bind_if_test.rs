use std::net::{<PERSON>cketAddr, SocketAddrV4, ToSocketAddrs};
use std::os::windows::io::{FromRawSocket, IntoRawSocket};
use std::time::Duration;

use socket2::{Domain, SockAddr, Socket, Type};
use tokio::io::{self, AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpSocket;

#[tokio::test]
async fn bind_test() -> io::Result<()> {
    let interface_ip = "************"; // 替换为您的接口IP
    // let target_addr: SocketAddr = "*************:80".parse().unwrap(); // api.ip.sb 的 IP 地址和端口
    // let request = "GET /ip HTTP/1.1\r\nHost: api.ip.sb\r\nConnection: close\r\n\r\n";

    let hostname = "************";
    let port = 443;
    let addr = (hostname, port).to_socket_addrs()?.next().unwrap();
    println!("hostname :{}", addr);
    let string = format!(
        "GET /ip HTTP/1.1\r\nHost: {}\r\nConnection: close\r\n\r\n",
        hostname
    );
    let request = string.as_str();

    // 创建一个 tokio 计时器，每隔 5 秒触发一次
    let mut interval = tokio::time::interval(Duration::from_secs(5));

    loop {
        interval.tick().await;

        match send_request(interface_ip, addr, request).await {
            Ok(response) => println!("Response: {}", response),
            Err(e) => eprintln!("Failed to send request: {}", e),
        }
    }
}

async fn send_request(interface_ip: &str, target_addr: SocketAddr, request: &str) -> io::Result<String> {
    // 创建一个新的套接字
    let socket = Socket::new(Domain::IPV4, Type::STREAM, None).unwrap();

    // 将套接字绑定到特定的接口
    let interface_ip = interface_ip.parse().unwrap();
    socket.bind(&SockAddr::from(SocketAddrV4::new(interface_ip, 0))).unwrap();
    // socket.bind_device(Some(interface_ip))?;

    // 将套接字设置为非阻塞模式，以便与 tokio 一起使用
    socket.set_nonblocking(true).unwrap();

    // 将套接字连接到目标地址
    // socket.connect(&target_addr.into()).unwrap();


    let tcp_socket = unsafe { TcpSocket::from_raw_socket(socket.into_raw_socket()) };
    let mut tcp_stream = tcp_socket.connect(target_addr.into()).await?;


    // 将 socket2::Socket 转换为 tokio::net::TcpStream
    // let std_tcp_stream = unsafe{std::net::TcpStream::from_raw_socket(socket.into_raw_socket())};
    // let mut tcp_stream = TcpStream::from_std(std_tcp_stream).unwrap();


    // 发送 HTTP GET 请求
    tcp_stream.write_all(request.as_bytes()).await.unwrap();

    // 读取响应内容
    let mut response = Vec::new();
    tcp_stream.read_to_end(&mut response).await?;
    Ok(String::from_utf8_lossy(&response).to_string())
}