build ios
cargo lipo --targets aarch64-apple-ios --release --package flyshadow_core_lib

build ios-sim
cargo lipo --targets aarch64-apple-ios-sim --release --package flyshadow_core_lib

build mac
cargo build -p flyshadow_core_lib --lib --release

build mac tun
cargo build -p flyshadow_mac_tun --release

build ndk
cargo ndk -t armeabi-v7a -t arm64-v8a -t x86_64 build --release --package flyshadow_core_lib --lib

build win
cargo build --lib --release --package flyshadow_core_lib

build route
cargo clean
cross build --target aarch64-unknown-linux-musl --release --package flyshadow_router
cross build --target x86_64-unknown-linux-musl --release --package flyshadow_router
VERSION=v1.12.2
tar zcvf flyshadow_router_aarch64-$VERSION.tar.gz target/aarch64-unknown-linux-musl/release/flyshadow_router 
tar zcvf flyshadow_router_x86_64-$VERSION.tar.gz target/x86_64-unknown-linux-musl/release/flyshadow_router 
cp flyshadow_router_*.tar.gz /mnt/hgfs/Downloads/
rm -f flyshadow_router_*.tar.gz


build agent
cargo build --package agent --release