[package]
name = "flyshadow_router"
version = "0.1.0"
edition = "2024"

[dependencies]
flyshadow_common = { path = "../flyshadow_common" }
tunnel = { path = "../tunnel" }
tokio = { version = "1", features = ["full"] }
serde = { version = "1" }
serde_json = { version = "1" }
actix-web = "4"
actix-web-static-files = "4"
actix-cors = "0"
static-files = "0"
tokio-tun = "0"
reqwest = { version = "0", features = ["serde_json", "json"] }
libc = "0"
futures-util = "0"
socket2 = "0"
ipnetwork = "0"
base64 = "0"
uuid = { version = "1", features = ["v4"] }

log = "0"
simple_logger = "4"
mime_guess = "2"


[build-dependencies]
static-files = "0"