import axios from "axios";
import {Loading, Message} from 'element-ui';
import Vue from "vue";

export const BASE_URL = process.env.VUE_APP_BASE_URL

const request = axios.create({
  baseURL: BASE_URL,
  timeout: 60000,
})


let requestTimes = 0;
let loadingService;

function showRequestLoading(show) {
  if (show) {
    requestTimes++;
  } else {
    requestTimes--;
  }
  if (requestTimes === 0 && loadingService) {
    loadingService.close()
  } else if (requestTimes === 1) {
    loadingService = Loading.service({
      lock: true,
      text: '疯狂加载中...',
      spinner: 'el-icon-loading',
      background: 'rgba(0, 0, 0, 0.7)'
    })
  }
}

request.interceptors.request.use(config => {
  if (!config.headers.showLoading && config.headers.showLoading !== false) {
    showRequestLoading(true)
  }
  if (Vue.$cookies.get('webPassword')) {
    config.headers.set('X-Password', Vue.$cookies.get('webPassword'))
  }

  return config
}, error => {
  console.log('网络请求错误', error)
  if (!error.config.headers && !error.config.headers.showLoading && error.config.headers.showLoading !== false) {
    showRequestLoading(false)
  }
  return error
})

request.interceptors.response.use(response => {
  if (!response.config.headers.showLoading && response.config.headers.showLoading !== false) {
    showRequestLoading(false)
  }
  if (!response?.data?.success) {
    Message.error(response.data.message)
  }
  return response.data
}, error => {
  console.log('网络请求响应错误', error)
  if (error.config && error.config.headers && !error.config.headers.showLoading && error.config.headers.showLoading !== false) {
    showRequestLoading(false)
  }
  if (error?.message) {
    Message.error(error?.message + ',Please try later.')
    if (error?.response?.status === 403) {
      setTimeout(() => location.reload(), 2000)
    }
  }
  return error
})


export default request