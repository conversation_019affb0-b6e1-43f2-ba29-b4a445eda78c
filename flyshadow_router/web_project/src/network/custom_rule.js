import request from "@/network/request";

export function getCustomRuleList() {
    return request.get('/custom/rule/list')
}

export function saveCustomRule(data) {
    return request.post('/custom/rule/save', data)
}

export function updateCustomRule(data) {
    return request.post('/custom/rule/update', data)
}

export function updateCustomRuleList(data) {
    return request.post('/custom/rule/updateList', data)
}

export function deleteCustomRule(id) {
    return request.post('/custom/rule/delete', {
        id
    })
}
