import request from "@/network/request";

// 获取隧道状态
export function getStats() {
    return request.get('/tunnel/stats', {
        headers: {showLoading: false}
    })
}

// 启动隧道
export function tunnelStart() {
    return request.post('/tunnel/start')
}

// 停止隧道
export function tunnelStop() {
    return request.post('/tunnel/stop')
}


// 选择节点
export function selectNode(node_name) {
    return request.post('/tunnel/select_node', {node_name})
}

// 重新设置配置信息
export function resetConfig() {
    return request.post('/tunnel/reset_config')
}

// 速度测试
export function testPing(node_name) {
    return request.post('/tunnel/test_ping', {node_name}, {
        headers: {showLoading: false}
    })
}