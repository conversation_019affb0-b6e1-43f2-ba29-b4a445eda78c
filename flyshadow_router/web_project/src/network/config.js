import request from "@/network/request";

export function getVersion() {
    return request.get('/version', {
        headers: {showLoading: false}
    })
}

// 获取配置
export function getConfig() {
  return request.get('/config/get', {
    headers: {showLoading: false}
  })
}

// 更新配置
export function updateConfig(config){
  return request.post('/config/update',config)
}

// 设置订阅密码
export function setSubscribePassword(password){
  return request.post('/config/setSubscribePassword', {password})
}