import request from "@/network/request";

export function getPortForwardingList() {
    return request.get('/port/forwarding/list')
}

export function updateClientRemark(remark) {
    return request.post('/port/forwarding/remark', {
        remark
    })
}

export function resetClientUuid(remark) {
    return request.post('/port/forwarding/reset', {
        remark
    })
}

export function saveOrUpdatePortForwarding(data) {
    return request.post('/port/forwarding/save_or_update', data)
}

export function deletePortForwarding(id) {
    return request.post('/port/forwarding/delete', {
        id
    })
}
