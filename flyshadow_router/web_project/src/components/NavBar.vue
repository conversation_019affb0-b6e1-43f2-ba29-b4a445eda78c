<script>

import {getVersion} from "@/network/config";

export default {
  name: "NavBar",
  data(){
    return{
      version: ""
    }
  },
  mounted() {
    getVersion().then(res => this.version = res.data)
  },
  methods: {
    hideNavbar() {
      let bootstrap = require('bootstrap')
      let hideNavbar = this.$refs['navbarSupportedContent']
      let collapseInstance = bootstrap.Collapse.getInstance(hideNavbar)
      if (collapseInstance && hideNavbar.classList.contains("show")) {
        collapseInstance.hide()
      }
    },
    switchLanguage(lang) {
      this.$i18n.locale = lang;
      this.hideNavbar()
    }
  }
}
</script>

<template>
  <div>
    <nav class="navbar navbar-expand-lg bg-body-tertiary">
      <div class="container-fluid">
        <a class="navbar-brand" href="#">
          <img src="@/assets/logo.png" alt="logo" width="30" height="30" class="d-inline-block align-text-top">
          FlyShadow
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent" ref="navbarSupportedContent">
          <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item">
              <a aria-current="page" class="nav-link" href="#/" @click="hideNavbar">
                <i class="bi bi-card-list" style="font-size: 1.2rem"></i>
                {{ $t('serverList') }}
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#/setting" @click="hideNavbar">
                <i class="bi bi-tools" style="font-size: 1.2rem"></i>
                {{ $t('setting') }}
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#/accessControl" @click="hideNavbar">
                <i class="bi bi-menu-button-fill" style="font-size: 1.2rem"></i>
                {{ $t('accessControl') }}
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#/connectList" @click="hideNavbar">
                <i class="bi bi-list-columns" style="font-size: 1.2rem"></i>
                {{ $t('connectList') }}
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#/customRule" @click="hideNavbar">
                <i class="bi bi-rulers" style="font-size: 1.2rem"></i>
                {{ $t('customRule') }}
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#/portForwarding" @click="hideNavbar">
                <i class="bi bi-fast-forward-btn" style="font-size: 1.2rem"></i>
                {{ $t('portForwarding') }}
              </a>
            </li>
          </ul>
          <div class="d-lg-flex me-3">
            <div class="nav-link" href="">
              {{ version }}
            </div>
          </div>
          <div class="d-lg-flex col-lg-3 dropdown">
            <a class="nav-link dropdown-toggle" href="" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              {{ $t('language')}}
            </a>
            <ul class="dropdown-menu justify-content-lg-end">
              <li><button class="dropdown-item" @click="switchLanguage('en')">English</button></li>
              <li><a class="dropdown-item" @click="switchLanguage('zh')">中文</a></li>
            </ul>
          </div>
        </div>
      </div>
    </nav>
  </div>
</template>

<style scoped>

</style>