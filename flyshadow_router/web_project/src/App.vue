<template>
  <div class="vh-100" style="background: linear-gradient(#ffa5006e, white, #0000ff52)" v-if="showSetting">
    <navbar/>

    <div style="height: calc(100% - 70px)">
      <div class="bg-body-tertiaryp-5 rounded h-100">
        <div class="col-sm-8 mx-auto mt-2 h-100">
          <router-view/>
        </div>
      </div>
    </div>

  </div>

  <div class="vh-100 d-flex flex-wrap align-content-center"
       style="background: linear-gradient(#ffa5006e, white, #0000ff52)" v-else>
    <div class="container shadow rounded bg-white d-flex justify-content-center" style="max-width: 400px;height: 180px">
      <form class="mt-3">
        <div class="mb-3">
          <label class="form-label" for="webPasswordInput">
            {{ hasPassword ? $t('enterPassword') : $t('setPassword') }}
          </label>
          <input id="webPasswordInput" v-model="webPassword" class="form-control"
                 required
                 type="password">
        </div>
        <button class="btn btn-warning" type="submit" @click="submitPassword">{{ $t('submit') }}</button>
      </form>
    </div>
  </div>
</template>

<script>
import Navbar from "@/components/NavBar.vue";
import {webPasswordCheck, webPasswordGet, webPasswordSet} from "@/network/web_password";

export default {
  name: 'App',
  components: {
    Navbar,
  },
  data() {
    return {
      hasPassword: false,
      webPassword: '',
      showSetting: false,
    }
  },
  mounted() {
    this.getPasswordSetting()
  },
  methods: {
    getPasswordSetting() {
      webPasswordGet().then(res => {
        if (res.data) {
          this.hasPassword = true
          this.checkPassword()
        } else {
          this.hasPassword = false
        }
      })
    },
    checkPassword() {
      if (!this.$cookies.get('webPassword')) {
        return
      }
      webPasswordCheck(this.$cookies.get('webPassword')).then(res => {
        if (res.data) {
          this.showSetting = true
        } else {
          this.$cookies.remove('webPassword')
          this.webPassword = ''
          this.webPasswordGet()
        }
      })
    },
    submitPassword() {
      if (!this.webPassword) {
        this.$message.error(this.$t('enterPassword'))
        return
      }
      if (this.hasPassword) {
        this.$cookies.set('webPassword', this.webPassword)
        this.checkPassword()
      } else {
        webPasswordSet(this.webPassword).then(res => {
          this.$cookies.set('webPassword', this.webPassword)
          this.checkPassword()
        })
      }
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.clickable {
  transition: background-color 0.5s ease, border-radius 0.5s ease;
}

.clickable:hover {
  background-color: rgba(255, 163, 0, 0.24);
  border-radius: 10px;
  cursor: pointer;
}


.scroll-container {
  max-height: 400px; /* 设置容器的最大高度 */
  overflow-y: auto; /* 允许纵向滚动 */
}

/* Webkit（Chrome、Safari等） */
::-webkit-scrollbar {
  width: 6px; /* 垂直滚动条的宽度 */
  height: 6px; /* 水平滚动条的高度 */
}

::-webkit-scrollbar-track {
  background: #f1f1f1; /* 轨道背景颜色 */
  border-radius: 10px; /* 轨道圆角 */
}

::-webkit-scrollbar-thumb {
  background: #cacaca; /* 滑块颜色 */
  border-radius: 10px; /* 滑块圆角 */
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1; /* 悬停时滑块颜色 */
}

/* Firefox */
.scroll-container {
  scrollbar-width: thin; /* 滚动条宽度 */
  scrollbar-color: #888 #f1f1f1; /* 滑块颜色和轨道颜色 */
}

/* Edge */
.scroll-container::-ms-scrollbar {
  width: 6px; /* 垂直滚动条的宽度 */
  height: 6px; /* 水平滚动条的高度 */
}

.scroll-container::-ms-scrollbar-track {
  background: #f1f1f1; /* 轨道背景颜色 */
  border-radius: 10px; /* 轨道圆角 */
}

.scroll-container::-ms-scrollbar-thumb {
  background: #888; /* 滑块颜色 */
  border-radius: 10px; /* 滑块圆角 */
}

.scroll-container::-ms-scrollbar-thumb:hover {
  background: #555; /* 悬停时滑块颜色 */
}
</style>
