// trafficUtil.js

class TrafficUnitEnum {
  static BYTE = { name: "B", value: 1 };
  static KILOBYTE = { name: "<PERSON><PERSON>", value: 1024 };
  static MEGABYTE = { name: "MB", value: 1024 * 1024 };
  static GIGABYTE = { name: "GB", value: 1024 * 1024 * 1024 };
  static TERABYTE = { name: "TB", value: 1024 * 1024 * 1024 * 1024 };

  static values() {
    return [this.BYTE, this.KILOBYTE, this.MEGABYTE, this.GIGABYTE, this.TERABYTE];
  }
}

const TrafficUtil = {
  getTrafficString(traffic, trafficUnitEnum) {
    if(traffic === undefined || traffic === null){
      traffic = 0
    }
    let unit = "";
    if (traffic < 0) {
      traffic = -traffic;
      unit = "-";
    }

    let index = 0;
    let trafficFloat = traffic;

    while (trafficFloat > 1024) {
      if (TrafficUnitEnum.values()[index] === trafficUnitEnum) {
        break;
      }
      trafficFloat /= 1024;
      index++;
    }

    return unit + trafficFloat.toFixed(2) + TrafficUnitEnum.values()[index].name;
  },

  getTrafficLong(trafficStr) {
    let trafficUnitEnum = null;

    for (const value of TrafficUnitEnum.values()) {
      const index = trafficStr.lastIndexOf(value.name);
      if (index !== -1) {
        trafficUnitEnum = value;
      }
    }

    if (trafficUnitEnum === null) {
      return null;
    }

    let trafficUnit = 1;
    const ordinal = TrafficUnitEnum.values().indexOf(trafficUnitEnum);
    for (let i = 0; i < ordinal; i++) {
      trafficUnit *= 1024;
    }

    try {
      const traffic = parseFloat(trafficStr.substring(0, trafficStr.indexOf(trafficUnitEnum.name)));
      return Math.round(traffic * trafficUnit);
    } catch (e) {
      throw new Error(`流量格式错误: ${trafficStr}`);
    }
  },

  TrafficUnitEnum, // 导出枚举
};

// 导出工具类
export default TrafficUtil;
