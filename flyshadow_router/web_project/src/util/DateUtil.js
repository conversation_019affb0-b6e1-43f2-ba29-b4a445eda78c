import i18n from "@/lang";

export const DateUtil = {
    formatTimeAgo: function (timestamp) {
        const now = Date.now(); // 获取当前时间戳
        let diff = Math.abs(now - timestamp); // 计算时间差（绝对值，避免负数）

        const days = Math.floor(diff / (1000 * 60 * 60 * 24)); // 天数
        diff -= days * (1000 * 60 * 60 * 24);

        const hours = Math.floor(diff / (1000 * 60 * 60)); // 小时数
        diff -= hours * (1000 * 60 * 60);

        const minutes = Math.floor(diff / (1000 * 60)); // 分钟数
        diff -= minutes * (1000 * 60);

        const seconds = Math.floor(diff / 1000); // 秒数

        return `${days}${i18n.t('day')}${hours}${i18n.t('hour')}${minutes}${i18n.t('minutes')}${seconds}${i18n.t('seconds')}`;
    }

}