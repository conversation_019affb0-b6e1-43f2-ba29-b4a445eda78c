export default {
    serverList: 'Server List',
    setting: 'Settings',
    accessControl: 'Access Control',
    language: 'Language',
    subscribeSetting: 'Subscription Settings',
    subscribePassword: 'Subscription Password',
    submit: 'Submit',
    add: 'Add',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete',
    confirmReset: 'Are you sure you want to reset',
    proxySetting: 'Proxy Settings',
    type: 'Type',
    default: 'Default',
    other: 'Other',
    defaultSizeError: 'The default rule can only be one',
    ipMacNotNull: 'Either IP,Port or MAC must be provided',
    direct: 'Direct',
    reject: 'Reject',
    proxy: 'Proxy',
    global: 'Global',
    rule: 'Rule',
    pleaseSelectServer: 'Please select a server',
    proxyMode: 'Proxy Mode',
    delayTest: 'Delay Test',
    expireAt: 'Expires At',
    traffic: 'Traffic',
    udpSetting: 'UDP Settings',
    udpProxyType: 'UDP Proxy Mode',
    nativeUdp: 'Native UDP',
    webPort: 'Web Port',
    webSetting: 'Web Settings',
    effectAfterRestart: 'Takes effect after restart',
    tunModelEnable: 'Enable TUN Mode',
    directConnPriorityEnable: 'Enable Direct Connect Priority',
    directConnPriorityTimeout: 'Direct Connect Priority Timeout(ms)',
    ipv6Enable: 'Enable IPv6',
    socketProxyPort: 'HTTP/Socket Proxy Port',
    transparentSetting: 'Transparent Proxy Settings',
    transparentProxyEnable: 'Enable Transparent Proxy',
    transparentProxyEnableDesc: 'Transparent proxy, required on router side',
    transparentProxyPort: 'Transparent Proxy Port',
    enterPassword: 'Please enter password',
    setPassword: 'Please set password',
    useBuildInDnsEnable: 'Use Build-in Dns',
    close: 'Close',
    tproxy: 'Transparent Proxy',
    redirect: 'Redirect',
    autoConnectAfterStartup: 'Auto connect on startup',
    connectList: 'Connect List',
    uploadTraffic: 'Upload Traffic',
    downloadTraffic: 'Download Traffic',
    sourceAddress: 'Source Address',
    targetAddress: 'Target Address',
    protocol: 'Protocol',
    matchName: 'Match Name',
    matchRule: 'Match Rule',
    totalUploadTraffic: 'Total Upload Traffic',
    totalDownloadTraffic: 'Total Download Traffic',
    survivalTime: 'Survival Time',
    createTime: 'Create Time',
    operation: 'Operation',
    day: 'Day',
    hour: 'Hour',
    minutes: 'Minute',
    seconds: 'Second',
    confirmClose: 'Are you sure you want to close the connection',
    timeout: 'timeout',
    customRule: 'Custom Rule',
    valueMatching: 'Value Matching (Domain/IP/Other)',
    sort: 'Sort',
    enable: 'Enable',
    disable: 'Disable',
    domainNameMatch: 'Domain Name Match (DOMAIN)',
    domainNameSuffixMatch: 'Domain Name Suffix Match (DOMAIN-SUFFIX)',
    domainNameKeywordMatch: 'Domain Name Keyword Match (DOMAIN-KEYWORD)',
    CIDR4Match: 'CIDR4 Match (IP-CIDR)',
    CIDR6Match: 'CIDR6 Match (IP-CIDR6)',
    srcCIDR4Match: 'Source IP Match (SRC-IP-CIDR4)',
    geoIpMatch: 'GEOIP Database (Country Code) Match (GEOIP)',
    dstPortMatch: 'Destination Port Match (DST-PORT)',
    srcPortMatch: 'Source Port Match (SRC-PORT)',
    processNameMatch: 'Source Process Name Match (PROCESS-NAME)',
    allMatch: 'All Match (MATCH)',
    clashClassicalMatch: 'ClashClassical Rule Set Link (Yaml format link)',
    clashDomainMatch: 'ClashDomain Rule Set Link (Txt format link)',
    matchingRule: 'Matching Rule',
    proxyType: 'Proxy Type',
    edit: 'Edit',
    pleaseSelect: 'Please Select ',
    pleaseEnter: 'Please Enter ',
    status: 'Status',
    cancelBlank: 'Cancel',
    directConnPriority: 'Direct Conn Priority',
    portForwarding: 'Port Forwarding',
    client: 'Client',
    listenPort: 'Listen Port',
    targetAddr: 'Target Address',
    targetUuid: 'Target Client ID',
    errorMessage: 'Error Message',
    reset: 'Reset',
    remark: 'Remark',
};
