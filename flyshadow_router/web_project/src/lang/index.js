import Vue from 'vue'
import VueI18n from 'vue-i18n' // 引入国际化插件包
import elementEnLocale from 'element-ui/lib/locale/lang/en' // element-ui 英文包
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN' // element-ui 中文包
import customEN from './en' // 自定义英语包
import customZH from './zh' // 自定义中文包
import VueCookies from 'vue-cookies'

Vue.use(VueI18n) // 全局注册国际化包

const messages = {
  en: {
    ...customEN,
    ...elementEnLocale
  },
  zh: {
    ...customZH ,
    ...elementZhLocale
  }
}
export function getLanguage() {
  const chooseLanguage = VueCookies.get('language')
  if (chooseLanguage) return setHtmlLanguage(chooseLanguage)

  // 如果没有选择语言
  const language = (navigator.language || navigator.browserLanguage).toLowerCase()
  const locales = Object.keys(messages)
  let thisLocale = 'en'
  for (const locale of locales) {
    if (language.indexOf(locale) > -1) {
      thisLocale = locale
      break
    }
  }
  return setHtmlLanguage(thisLocale) // 默认语言
}

function setHtmlLanguage(thisLocale) {
  document.getElementById('htmlElement').setAttribute('lang', thisLocale);
  return thisLocale
}

const i18n = new VueI18n({
  locale: getLanguage(),
  messages
})

i18n.setHtmlLanguage = setHtmlLanguage

export default i18n