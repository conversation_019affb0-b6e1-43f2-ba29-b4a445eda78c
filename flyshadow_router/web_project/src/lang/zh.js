export default {
    serverList: '节点列表',
    setting: '设置',
    accessControl: '访问控制',
    language: '语言',
    subscribeSetting: '订阅设置',
    subscribePassword: '订阅密码',
    submit: '提交',
    add: '添加',
    delete: '删除',
    confirmDelete: '你确定要删除吗',
    confirmReset: '你确定要重置吗',
    proxySetting: '代理设置',
    type: '类型',
    default: '默认',
    other: '其他',
    defaultSizeError: '默认规则只能是一条',
    ipMacNotNull: 'IP,Port和MAC不能同时为空',
    direct: '直连',
    reject: '拒绝',
    proxy: '代理',
    global: '全局',
    rule: '规则',
    pleaseSelectServer: '请选择节点',
    proxyMode: '代理模式',
    delayTest: '延迟测试',
    expireAt: '到期',
    traffic: '流量',
    udpSetting: 'UDP 设置',
    udpProxyType: 'UDP 代理模式',
    nativeUdp: '原生 UDP',
    webPort: 'Web 端口',
    webSetting: 'Web 设置',
    effectAfterRestart: '重启应用后生效',
    tunModelEnable: 'TUN 模式启用',
    directConnPriorityEnable: '直连优先启用',
    directConnPriorityTimeout: '直连优先超时时间(ms)',
    ipv6Enable: 'IPv6启用',
    socketProxyPort: 'Http/Socket 代理端口',
    transparentSetting: '透明代理设置',
    transparentProxyEnable: '透明代理启用',
    transparentProxyEnableDesc: '透明代理,路由端需要开启',
    transparentProxyPort: '透明代理端口',
    enterPassword: '请输入密码',
    setPassword: '请设置密码',
    useBuildInDnsEnable: '使用内置DNS',
    close: '关闭',
    tproxy: '透明代理',
    redirect: '重定向',
    autoConnectAfterStartup: '启动后自动连接',
    connectList: '连接列表',
    uploadTraffic: '上传流量',
    downloadTraffic: '下载流量',
    sourceAddress: '源地址',
    targetAddress: '目标地址',
    protocol: '协议',
    matchName: '匹配名称',
    matchRule: '匹配规则',
    totalUploadTraffic: '总上传流量',
    totalDownloadTraffic: '总下载流量',
    survivalTime: '存活时间',
    createTime: '创建时间',
    operation: '操作',
    day: '天',
    hour: '时',
    minutes: '分',
    seconds: '秒',
    confirmClose: '你确定要关闭连接吗',
    timeout: '超时',
    customRule: '自定义规则',
    valueMatching: '匹配值(域名/IP/其他)',
    sort: '排序',
    enable: '启用',
    disable: '禁用',
    domainNameMatch: '域名匹配(DOMAIN)',
    domainNameSuffixMatch: '域名后缀匹配(DOMAIN-SUFFIX)',
    domainNameKeywordMatch: '域名关键字匹配(DOMAIN-KEYWORD)',
    CIDR4Match: 'IPV4 IP段匹配(IP-CIDR)',
    CIDR6Match: 'IPV6 IP 段匹配(IP-CIDR6)',
    srcCIDR4Match: '源 IP 段匹配(SRC-IP-CIDR4)',
    geoIpMatch: 'GEOIP 数据库（国家代码）匹配(GEOIP)',
    dstPortMatch: '目标端口匹配(DST-PORT)',
    srcPortMatch: '源端口匹配(SRC-PORT)',
    processNameMatch: '源进程名匹配(PROCESS-NAME)',
    allMatch: '全匹配(MATCH)',
    clashClassicalMatch: 'ClashClassical规则集链接(Yaml format link)',
    clashDomainMatch: 'ClashDomain规则集链接(Txt format link)',
    matchingRule: '匹配规则',
    proxyType: '代理方式',
    edit: '编辑',
    pleaseSelect: '请选择',
    pleaseEnter: '请输入',
    status: '状态',
    cancelBlank: '取 消',
    directConnPriority: '直连优先',
    portForwarding: '端口转发',
    client: '客户端',
    listenPort: '监听端口',
    targetAddr: '目标地址',
    targetUuid: '目标客户端ID',
    errorMessage: '错误消息',
    reset: '重置',
    remark: '备注',
};