// src/router/index.js

import VueRouter from 'vue-router';
import Vue from "vue";
import NodeList from '../views/NodeList.vue';
import Setting from '../views/Setting.vue';
import AccessControl from '../views/AccessControl.vue';
import ConnectList from '../views/ConnectList.vue';
import CustomRule from "@/views/CustomRule.vue";
import PortForwarding from "@/views/PortForwarding.vue";

Vue.use(VueRouter)


const routes = [
    {
        path: '/',
        name: 'nodeList',
        component: NodeList
    },
    {
        path: '/setting',
        name: 'setting',
        component: Setting
    },
    {
        path: '/accessControl',
        name: 'accessControl',
        component: AccessControl
    },
    {
        path: '/connectList',
        name: 'connectList',
        component: ConnectList
    },
    {
        path: '/customRule',
        name: 'customRule',
        component: CustomRule
    },
    {
        path: '/portForwarding',
        name: 'portForwarding',
        component: PortForwarding
    },
];

const router = new VueRouter({
    mode: 'hash',
    routes
})

export default router;
