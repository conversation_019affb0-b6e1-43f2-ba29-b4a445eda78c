<template>
  <div class="container">
    <div class="shadow rounded bg-white p-3">

      <div class="d-flex justify-content-end">
        <button class="btn btn-primary btn-sm" @click="handleAdd">{{ $t('add') }}</button>
      </div>


      <el-dialog :title="$t('customRule')" :visible.sync="editShow" top="1vh" width="90%"
                 @close="editShow = false;initCustomRuleList()">
        <el-form ref="saveForm" :model="editItem" label-width="155px">
          <el-form-item :label="$t('status')"
                        prop="enable"
                        :rules="[{required:true,type:'number',message:$t('pleaseSelect') + $t('status'), trigger:'blur'}]">
            <el-select v-model="editItem.enable" size="mini">
              <el-option
                  :label="$t('enable')"
                  :value="1">
              </el-option>
              <el-option
                  :label="$t('disable')"
                  :value="0">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('valueMatching')"
                        prop="domain"
                        :rules="[{required:true,message:$t('pleaseEnter') + $t('valueMatching'), trigger:'blur'}]">
            <el-input v-model="editItem.domain" size="mini"/>
          </el-form-item>
          <el-form-item :label="$t('matchingRule')"
                        prop="matching"
                        :rules="[{required:true,type:'number',message:$t('pleaseSelect') + $t('matchingRule'), trigger:'blur'}]">
            <el-select v-model="editItem.matching" size="mini">
              <el-option
                  v-for="(item,index) in matching" :key="item"
                  :label="item"
                  :value="index">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('proxyType')"
                        prop="proxyType"
                        :rules="[{required:true,type:'number',message:$t('pleaseSelect') + $t('proxyType'), trigger:'blur'}]">
            <el-select v-model="editItem.proxyType" size="mini">
              <el-option
                  v-for="(item,index) in proxyType" :key="item"
                  :label="item"
                  :value="index">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('directConnPriority')"
                        prop="directConnPriority"
                        :rules="[{required:true,type:'number',message:$t('pleaseSelect') + $t('directConnPriority'), trigger:'blur'}]">
            <el-select v-model="editItem.directConnPriority" size="mini">
              <el-option
                  :label="$t('enable')"
                  :value="1">
              </el-option>
              <el-option
                  :label="$t('disable')"
                  :value="0">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSave('saveForm')">{{ $t('submit') }}</el-button>
            <el-button @click="editShow = false;initCustomRuleList()">{{ $t('cancelBlank') }}</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <div class="mt-2 overflow-y-auto" style="height: calc(100vh - 150px)">
        <el-table :data="customRuleList" style="width: 100%" height="100%"
                  row-key="id"
        >
          <el-table-column
              prop="enable"
              :label="$t('status')"
              width="90">
            <template slot-scope="scope">
              <el-tag :type="scope.row.enable?'success':'danger'" size="mini">
                {{ scope.row.enable ? $t('enable') : $t('disable') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
              prop="domain"
              :label="$t('valueMatching')"
              width="250">
          </el-table-column>
          <el-table-column
              prop="matching"
              :label="$t('matchingRule')"
              width="250">
            <template slot-scope="scope">
              {{ matching[scope.row.matching] }}
            </template>
          </el-table-column>
          <el-table-column
              prop="proxyType"
              :label="$t('proxyType')"
              width="100">
            <template slot-scope="scope">
              <div>
                {{ proxyType[scope.row.proxyType] }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="directConnPriority"
              :label="$t('directConnPriority')"
              width="90">
            <template slot-scope="scope">
              <el-tag :type="scope.row.directConnPriority?'success':'danger'" size="mini">
                {{ scope.row.directConnPriority ? $t('enable') : $t('disable') }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
              :label="$t('operation')"
              width="160">
            <template slot-scope="scope">
              <el-button size="mini" @click="editCustomRule(scope.row)" style="margin-right: 5px">{{
                  $t('edit')
                }}
              </el-button>

              <el-popconfirm
                  :title="$t('confirmDelete')+'?'"
                  @confirm="deleteCustomRule(scope.row.id)"
              >
                <el-button slot="reference" type="danger" size="mini">{{ $t('delete') }}</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

    </div>
  </div>
</template>

<script>
import {deleteCustomRule, getCustomRuleList, saveCustomRule, updateCustomRule} from "@/network/custom_rule";

export default {
  name: "CustomRule",
  data() {
    return {
      editItem: {},
      editShow: false,
      customRuleList: [],
      matching: [
        this.$t('domainNameMatch'),
        this.$t('domainNameSuffixMatch'),
        this.$t('domainNameKeywordMatch'),
        this.$t('CIDR4Match'),
        this.$t('CIDR6Match'),
        this.$t('srcCIDR4Match'),
        this.$t('geoIpMatch'),
        this.$t('dstPortMatch'),
        this.$t('srcPortMatch'),
        this.$t('processNameMatch'),
        this.$t('allMatch'),
        this.$t('clashClassicalMatch'),
        this.$t('clashDomainMatch'),
      ],
      proxyType: [
        this.$t('direct'),
        this.$t('reject'),
        this.$t('proxy'),
      ],
    }
  },
  mounted() {
    this.initCustomRuleList()
  },
  methods: {
    initCustomRuleList() {
      getCustomRuleList().then(res => {
        if (res.success) {
          this.customRuleList = res.data
        }
      })
    },
    deleteCustomRule(id) {
      deleteCustomRule(id).then(res => {
        this.initCustomRuleList()
      })
    },
    editCustomRule(obj) {
      this.editItem = obj
      this.editShow = true
    },
    handleSave(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.editItem.id) {
            updateCustomRule(this.editItem).then(res => {
              this.editItem = {}
              this.editShow = false
              this.initCustomRuleList()
            })
          } else {
            saveCustomRule(this.editItem).then(res => {
              this.editItem = {}
              this.editShow = false
              this.initCustomRuleList()
            })
          }
        } else {
          console.log('error submit ')
          return false
        }
      })
    },
    handleAdd() {
      this.editItem = {
        enable: 1,
        directConnPriority: 0,
      }
      this.editShow = true
    }
  }
}
</script>


<style scoped>

</style>