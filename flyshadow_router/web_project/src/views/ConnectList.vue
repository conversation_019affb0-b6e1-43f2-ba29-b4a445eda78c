<template>
  <div class="container">
    <div class="shadow rounded bg-white p-3">

      <div class="mt-2 overflow-y-auto" style="height: calc(100vh - 150px)">
        <el-input v-model="filterValue" @input="doFilterList" placeholder="Search Content"/>
        <el-table :data="filterConnectList" style="width: 100%" height="100%"
                  :default-sort="{prop: 'active_time', order: 'descending'}">
          <el-table-column
              prop="sourceAddress"
              :label="$t('sourceAddress')"
              sortable
              width="150">
            <template slot-scope="scope">
              <div>
                {{ scope.row.source_addr }}:{{ scope.row.source_port }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="targetAddress"
              :label="$t('targetAddress')"
              sortable
              width="150">
            <template slot-scope="scope">
              <div>
                {{ scope.row.target_addr }}:{{ scope.row.target_port }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="protocol"
              :label="$t('protocol')"
              sortable
              width="80">
            <template slot-scope="scope">
              <el-tag size="mini">{{ scope.row.protocol }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
              prop="proxy_type"
              :label="$t('proxyMode')"
              sortable
              width="100">
            <template slot-scope="scope">
              <div>
                {{ scope.row.proxy_type }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="matcher_name"
              :label="$t('matchName')"
              sortable
              width="130">
            <template slot-scope="scope">
              <div>
                {{ scope.row.matcher_name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="matcher_rule"
              :label="$t('matchRule')"
              sortable
              width="130">
            <template slot-scope="scope">
              <div>
                {{ scope.row.matcher_rule }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="upload_traffic"
              :label="$t('uploadTraffic')"
              sortable
              width="130">
            <template slot-scope="scope">
              <div>
                ↑ {{ TrafficUtil.getTrafficString(scope.row.upload_traffic, null) }} /S
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="download_traffic"
              :label="$t('downloadTraffic')"
              sortable
              width="130">
            <template slot-scope="scope">
              <div>
                ↓ {{ TrafficUtil.getTrafficString(scope.row.download_traffic, null) }} /S
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="upload_traffic_total"
              :label="$t('totalUploadTraffic')"
              sortable
              width="130">
            <template slot-scope="scope">
              <div>
                ↑ {{ TrafficUtil.getTrafficString(scope.row.upload_traffic_total, null) }} /S
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="download_traffic_total"
              :label="$t('totalDownloadTraffic')"
              sortable
              width="130">
            <template slot-scope="scope">
              <div>
                ↓ {{ TrafficUtil.getTrafficString(scope.row.download_traffic_total, null) }} /S
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="active_time"
              :label="$t('survivalTime')"
              sortable
              width="130">
            <template slot-scope="scope">
              <div>
                {{ DateUtil.formatTimeAgo(scope.row.active_time) }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
              prop="active_time"
              :label="$t('createTime')"
              sortable
              width="200">
            <template slot-scope="scope">
              <div>
                {{ new Date(scope.row.active_time).toLocaleString() }}
              </div>
            </template>
          </el-table-column>

          <el-table-column
              :label="$t('operation')"
              width="130">
            <template slot-scope="scope">
              <el-popconfirm
                  :title="$t('confirmClose')+'?'"
                  @confirm="closeConnect(scope.row.source_addr + ':'+scope.row.source_port+'==='+scope.row.protocol)"
              >
                <el-button slot="reference" type="danger" size="mini">{{ $t('close') }}</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

    </div>
  </div>
</template>

<script>
import {closeConnectList, getConnectList} from "@/network/connect_list";
import {DateUtil} from "@/util/DateUtil";
import TrafficUtil from "@/util/TrafficUtil";

export default {
  name: "ConnectList",
  computed: {
    TrafficUtil() {
      return TrafficUtil
    },
    DateUtil() {
      return DateUtil
    }
  },
  data() {
    return {
      destroy: false,
      connectList: [],
      filterConnectList: [],
      filterValue: ''
    }
  },
  beforeDestroy() {
    this.destroy = true
  },
  mounted() {
    this.destroy = false
    this.initConnectList()
  },
  methods: {
    doFilterList() {
      if (!this.filterValue) {
        this.filterConnectList = this.connectList
      } else {
        this.filterConnectList = this.connectList.filter(item => {
          let result = false
          for (let string of this.filterValue.toLowerCase().split(" ")) {
            result = result || item.source_addr?.toLowerCase().indexOf(string) > -1 ||
                String(item.source_port)?.toLowerCase().indexOf(string) > -1 ||
                item.target_addr?.toLowerCase().indexOf(string) > -1 ||
                String(item.target_port)?.toLowerCase().indexOf(string) > -1 ||
                item.protocol?.toLowerCase().indexOf(string) > -1 ||
                item.proxy_type?.toLowerCase().indexOf(string) > -1 ||
                item.matcher_name?.toLowerCase().indexOf(string) > -1 ||
                item.matcher_rule?.toLowerCase().indexOf(string) > -1
          }
          return result
        })
      }
    },
    closeConnect(key) {
      closeConnectList(key)
    },
    initConnectList() {
      if (this.destroy) {
        return
      }
      getConnectList().then(res => {
        if (res.success) {
          this.connectList = JSON.parse(res.data)
          this.doFilterList()
        }
        setTimeout(this.initConnectList, 2000)
      })
    },
  }
}
</script>


<style scoped>

</style>