<template>
  <div class="container">
    <div class=" shadow rounded bg-white p-3">

      <ul class="nav nav-tabs mb-2">
        <li v-for="key in Object.keys(settingList)" :key="key" class="nav-item">
          <a :class="{'active':activeSetting === key}" aria-current="page" class="nav-link"
             @click="()=>{activeSetting = key;loadConfig()}">{{ settingList[key] }}</a>
        </li>
      </ul>

      <div class="overflow-auto" style="max-height: calc(100vh - 180px)">

        <div v-if="activeSetting === 'subscribeSetting'">
          <form>
            <div class="mb-3">
              <label class="form-label" for="subscribeFormControlInput">{{ $t('subscribePassword') }}</label>
              <input id="subscribeFormControlInput" v-model="config.subscribe_password" class="form-control" placeholder="********" required
                     type="text">
            </div>
            <button class="btn btn-warning" type="submit" @click="submitSubscribePassword">{{ $t('submit') }}</button>
          </form>
        </div>

        <div v-if="activeSetting === 'webSetting'">
          <form>
            <div class="mb-3">
              <label class="form-label" for="webPortInput">{{ $t('webPort') }}</label>
              <input id="webPortInput" v-model="config.web_port" class="form-control"
                     type="number"
                     @change="config.web_port = Number(config.web_port)">
              <div class="form-text">{{ $t('effectAfterRestart') }}</div>
            </div>
            <button class="btn btn-warning" type="submit" @click="requestUpdateConfig">{{ $t('submit') }}</button>
          </form>
        </div>

        <div v-if="activeSetting === 'udpSetting'">
          <form>
            <div class="mb-3">
              <label class="form-label">{{ $t('udpProxyType') }}</label>
              <div>
                <el-radio-group v-model="config.udp_proxy_type" fill="orange" size="mini"
                                @input="(v)=>config.udp_proxy_type = v">
                  <el-radio-button :label="0">{{ $t('direct') }}</el-radio-button>
                  <el-radio-button :label="3">{{ $t('rule') }}</el-radio-button>
                  <el-radio-button :label="2">{{ $t('global') }}</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">{{ $t('nativeUdp') }}</label>
              <div>
                <el-switch :value="config.native_udp" active-color="orange" @change="(v)=>config.native_udp = v"/>
              </div>
            </div>
            <button class="btn btn-warning" type="submit" @click="requestUpdateAndResetConfig">{{ $t('submit') }}</button>
          </form>
        </div>

        <div v-if="activeSetting === 'proxySetting'">
          <form>
            <div class="mb-3">
              <label class="form-label">{{ $t('autoConnectAfterStartup') }}</label>
              <div>
                <el-switch :value="config.auto_connect_after_startup" active-color="orange"
                           @change="(v)=>config.auto_connect_after_startup = v"/>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">{{ $t('directConnPriorityEnable') }}</label>
              <div>
                <el-switch :value="config.direct_conn_priority" active-color="orange"
                           @change="(v)=>config.direct_conn_priority = v"/>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label" for="directConnPriorityTimeout">{{ $t('directConnPriorityTimeout') }}</label>
              <input id="directConnPriorityTimeout" v-model="config.direct_conn_priority_timeout" class="form-control"
                     type="number"
                     @change="config.direct_conn_priority_timeout = Number(config.direct_conn_priority_timeout)">
            </div>
            <div class="mb-3">
              <label class="form-label">{{ $t('tunModelEnable') }}</label>
              <div>
                <el-switch :value="config.tun_mode_enable" active-color="orange"
                           @change="(v)=>config.tun_mode_enable = v"/>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">{{ $t('useBuildInDnsEnable') }}</label>
              <div>
                <el-switch :value="config.use_build_in_dns" active-color="orange"
                           @change="(v)=>config.use_build_in_dns = v"/>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">{{ $t('ipv6Enable') }}</label>
              <div>
                <el-switch :value="config.ipv6_enable" active-color="orange"
                           @change="(v)=>config.ipv6_enable = v"/>
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label" for="socketProxyPort">{{ $t('socketProxyPort') }}</label>
              <input id="socketProxyPort" v-model="config.proxy_port" class="form-control"
                     type="number"
                     @change="config.proxy_port = Number(config.proxy_port)">
            </div>
            <button class="btn btn-warning" type="submit" @click="requestUpdateAndResetConfig">{{
                $t('submit')
              }}
            </button>
          </form>
        </div>

        <div v-if="activeSetting === 'transparentSetting'">
          <form>
            <div class="mb-3">
              <label class="form-label" for="transparentProxyEnable">{{ $t('transparentProxyEnable') }}</label>
              <div>
                <el-radio-group v-model="config.tproxy_mode_type" fill="orange" size="mini"
                                @input="(v)=>config.tproxy_mode_type = v">
                  <el-radio-button :label="0">{{ $t('close') }}</el-radio-button>
                  <el-radio-button :label="1">{{ $t('tproxy') }}</el-radio-button>
                  <el-radio-button :label="2">{{ $t('redirect') }}</el-radio-button>
                </el-radio-group>
              </div>
              <div class="form-text">{{ $t('transparentProxyEnableDesc') }}</div>
            </div>
            <div class="mb-3">
              <label class="form-label" for="transparentProxyPort">{{ $t('transparentProxyPort') }}</label>
              <input id="transparentProxyPort" v-model="config.tproxy_port" class="form-control"
                     type="number">
            </div>
            <button class="btn btn-warning" type="submit" @click="requestUpdateAndResetConfig">{{ $t('submit') }}</button>
          </form>
        </div>
      </div>


    </div>
  </div>
</template>
<script>
import {getConfig, setSubscribePassword, updateConfig} from "@/network/config";
import {resetConfig} from "@/network/tunnel";

export default {
  name: "Setting",
  data() {
    return {
      activeSetting: 'subscribeSetting',
      config: {},
    }
  },
  mounted() {
    this.loadConfig()
  },
  computed: {
    settingList() {
      return {
        subscribeSetting: this.$t('subscribeSetting'),
        webSetting: this.$t('webSetting'),
        udpSetting: this.$t('udpSetting'),
        proxySetting: this.$t('proxySetting'),
        transparentSetting: this.$t('transparentSetting')
      }
    }
  },
  methods: {
    loadConfig() {
      getConfig().then(res => {
        this.config = res.data
      })
    },
    submitSubscribePassword() {
      setSubscribePassword(this.config.subscribe_password).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadConfig()
        }
      })
    },
    requestUpdateConfig() {
      updateConfig(this.config).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.loadConfig()
        }
      })
    },
    requestUpdateAndResetConfig() {
      updateConfig(this.config).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          resetConfig()
        }
      })

    },
  }

}
</script>
<style scoped>
.nav-link {
    color: orange;
}
</style>