<template>
  <div class="h-100 container">
    <div class="container shadow rounded bg-white">
      <div class="d-flex align-items-center justify-content-around p-1">
        <i class="bi bi-cursor fs-3" style="color: orange"></i>
        <div class="align-items-start  ms-3 me-auto">
          <div v-if="config.select_node_name">{{ config.select_node_name }}</div>
          <div v-else>{{ $t('pleaseSelectServer') }}</div>
          <div class="text-black-50" style="font-size: 9px">{{
              stats ? `↑ ${TrafficUtil.getTrafficString(stats?.upload, null)}/S  ↓ ${TrafficUtil.getTrafficString(stats?.download, null)}/s` : '↑ 0.00B/S  ↓ 0.00B/s'
            }}
          </div>
        </div>
        <div v-show="stats.status !== 0 && stats.enabled" class="spinner-border text-warning me-3" role="status">
        </div>
        <el-switch :value="stats.enabled" active-color="orange" @change="tunnelStatusChange"/>
      </div>
      <div class="w-100 mt-1" style="background-color: #e8e7e7;height: 1px"></div>
      <div class="d-flex align-items-center justify-content-around p-1">
        <i class="bi bi-arrow-left-right fs-3" style="color: orange"></i>
        <div class="ms-3 me-auto">{{ $t('proxyMode') }}</div>
        <el-radio-group v-model="config.proxy_type" fill="orange" size="mini" @input="changeProxyType">
          <el-radio-button :label="0">{{ $t('direct') }}</el-radio-button>
          <el-radio-button :label="3">{{ $t('rule') }}</el-radio-button>
          <el-radio-button :label="2">{{ $t('global') }}</el-radio-button>
        </el-radio-group>
      </div>
      <div class="w-100 mt-1" style="background-color: #e8e7e7;height: 1px"></div>
      <div class="d-flex align-items-center justify-content-around p-1 clickable" @click="doSpeedtest">
        <i class="bi bi-speedometer2 fs-3" style="color: orange"></i>
        <div class="ms-3 me-auto">{{ $t('delayTest') }}</div>
      </div>
    </div>

    <div class="container shadow rounded mt-4 bg-white p-1" style="height: calc(100% - 200px)">
      <div class="d-flex align-items-center justify-content-around p-1">
        <i class="bi bi-list-task fs-3" style="color: orange"></i>
        <div class="me-auto w-100">
          <div class="ms-3 me-auto w-100 d-flex justify-content-between">{{ $t('serverList') }}
            <span class="me-3 sort-icon" @click="delaySortClick">
              <i :class="{'el-icon-d-caret':config.delay_sort === 0,'el-icon-caret-top':config.delay_sort === 1,'el-icon-caret-bottom':config.delay_sort === 2}"></i>
            </span>
          </div>
          <div v-if="config?.subscribe_content_entity?.user" class="ms-3 text-black-50 d-block" style="font-size: 9px">
            {{ $t('expireAt') }}: {{ new Date(config.subscribe_content_entity.user.expireTime).toLocaleString() }}
            {{ $t('traffic') }}: {{
              TrafficUtil.getTrafficString(config.subscribe_content_entity.user.trafficDownload + config.subscribe_content_entity.user.trafficUpload, null)
            }} /
            {{ TrafficUtil.getTrafficString(config.subscribe_content_entity.user.trafficSize, null) }}
          </div>
        </div>
      </div>
      <div class="w-100 mt-1" style="background-color: #e8e7e7;height: 1px"></div>
      <div class="overflow-auto" style="height: calc(100% - 60px)">
        <div v-for="(item,index) in nodeList" :key="item.name"
             :style="{'background-color':item.name === config.select_node_name ? 'rgba(255, 163, 0, 0.24)' : '',
           'border-radius':item.name === config.select_node_name ? '10px' : ''}"
             class="w-100 clickable p-1 ps-4" @click="changeSelectServerName(item.name)">
          <div class="d-flex justify-content-between">
            <span v-text="item.name"></span>
            <span v-if="item.delay" :class="{'text-red':item.delay===-1 || item.delay > 500,
            'text-green':item.delay>=0 && item.delay<=200,'text-yellow':item.delay>200 && item.delay<500,
            'spinner-border text-warning spinner-border-sm':item.delay===-2}">
              <span v-if="item.delay > -2">
                {{ item.delay === -1 ? $t('timeout') : (item.delay + 'ms') }}
              </span>
            </span>
          </div>
          <div class="text-black-50" style="font-size: 9px">
            <span class="border rounded" style="padding: 2px;border-color: #f6b1b1 !important;">UoT</span>
            <span v-if="item.nativeUdp" class="m-lg-1 border rounded"
                  style="color: #fb6464;border-color: #f6b1b1 !important;padding: 2px">NativeUDP</span>
          </div>
          <div class="w-100 mt-1" style="background-color: #e8e7e7;height: 1px"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getConfig, updateConfig} from "@/network/config";
import {getStats, resetConfig, selectNode, testPing, tunnelStart, tunnelStop} from "@/network/tunnel";
import TrafficUtil from "../util/TrafficUtil";

export default {
  name: "NodeList",
  computed: {
    TrafficUtil() {
      return TrafficUtil
    },
    nodeList() {
      if (this.config?.subscribe_content_entity?.node) {
        if (this.config.delay_sort === 0) {
          return this.config?.subscribe_content_entity?.node.sort((v1, v2) => v2.name.localeCompare(v1.name))
        } else if (this.config.delay_sort === 1) {
          return this.config?.subscribe_content_entity?.node.sort((v1, v2) => {
            if (v1.delay && v2.delay) {
              return v1.delay - v2.delay
            } else {
              return 0
            }
          })
        } else if (this.config.delay_sort === 2) {
          return this.config?.subscribe_content_entity?.node.sort((v1, v2) => {
            if (v1.delay && v2.delay) {
              return v2.delay - v1.delay
            } else {
              return 0
            }
          })
        }
      } else {
        return []
      }
    }
  },
  data() {
    return {
      config: {},
      stats: {},
      nodeInterval: -1,
      proxyMode: 0,
    }
  },
  mounted() {
    let that = this
    that.loadConfig()
    that.loadNodeStatus()
    this.nodeInterval = setInterval(() => {
      that.loadNodeStatus()
    }, 1500)
  },
  methods: {
    delaySortClick() {
      this.config.delay_sort += 1
      if (this.config.delay_sort === 3) {
        this.config.delay_sort = 0
      }
      updateConfig(this.config).then(res => {
      })
    },
    loadConfig() {
      getConfig().then(res => {
        this.config = res.data
      })
    },
    loadNodeStatus() {
      getStats().then(res => {
        this.stats = res.data
      })
    },
    changeSelectServerName(name) {
      if (this.config.select_node_name === name) {
        return
      }
      this.config.select_node_name = name
      selectNode(name).then(res => {
        if (res.success) {
          this.$message.success(res.message)
        }
      })
    },
    changeProxyType(value) {
      this.config.proxy_type = value
      updateConfig(this.config).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          resetConfig()
        }
      })

    },
    tunnelStatusChange(value) {
      if (value) {
        tunnelStart().then(() => this.loadNodeStatus())
      } else {
        tunnelStop().then(() => this.loadNodeStatus())
      }
    },
    doSpeedtest() {
      if (!this.config.subscribe_content_entity?.node || this.config.subscribe_content_entity?.node.find(item => item.delay === -2)) {
        return
      }
      for (let index = 0; index < this.config.subscribe_content_entity.node.length; index++) {
        let node = this.config.subscribe_content_entity.node[index];
        this.config.subscribe_content_entity.node.splice(index, 1, {...node, delay: -2})
        testPing(node.name).then(res => {
          for (let index = 0; index < this.config.subscribe_content_entity.node.length; index++) {
            let node1 = this.config.subscribe_content_entity.node[index];
            if (node1.name !== node.name) {
              continue
            }
            if (res.success) {
              this.config.subscribe_content_entity.node.splice(index, 1, {...node, delay: res.data})
            } else {
              this.config.subscribe_content_entity.node.splice(index, 1, {...node, delay: -1})
            }
          }
        })
      }

    }
  },
  beforeDestroy() {
    clearInterval(this.nodeInterval)
  }
}
</script>


<style scoped>
.sort-icon:hover {
  cursor: pointer;
}

.text-red {
  color: red;
}

.text-green {
  color: limegreen;
}

.text-yellow {
  color: #baba00;
}
</style>