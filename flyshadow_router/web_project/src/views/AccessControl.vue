<template>
  <div class="container">
    <div class="shadow rounded bg-white p-3">

      <div class="d-flex justify-content-end">
        <button class="btn btn-primary btn-sm" @click="accessControlList.push({...temp})">{{ $t('add') }}</button>
        <button class="btn btn-warning btn-sm ms-2" @click="updateAccessControl">{{ $t("submit") }}</button>
      </div>

      <hr class="my-4">

      <div class="mt-2">
        <el-table :data="accessControlList" style="width: 100%">
          <el-table-column
              :label="$t('type')"
              width="180">
            <template slot-scope="scope">
              <el-select v-model="scope.row.mode" size="mini" @change="log(scope)">
                <el-option
                    v-for="(item,index) in mode"
                    :key="index"
                    :label="item"
                    :value="index">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
              label="IP"
              width="180">
            <template slot-scope="scope">
              <el-input v-model="accessControlList[scope.$index].ip" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column
              label="Mac"
              width="180">
            <template slot-scope="scope">
              <el-input v-model="accessControlList[scope.$index].mac" size="mini"
                        @change="accessControlList[scope.$index].mac = accessControlList[scope.$index].mac.replaceAll('-',':');accessControlList[scope.$index].mac = accessControlList[scope.$index].mac.replaceAll('：',':')"></el-input>
            </template>
          </el-table-column>
          <el-table-column
              label="Port"
              width="90">
            <template slot-scope="scope">
              <el-input v-model="accessControlList[scope.$index].port" size="mini"></el-input>
            </template>
          </el-table-column>
          <el-table-column
              :label="$t('proxyMode')"
              width="180">
            <template slot-scope="scope">
              <el-select v-model="scope.row.proxy_type" size="mini">
                <el-option
                    v-for="item in Object.keys(proxyType)" :key="item"
                    :label="proxyType[item]"
                    :value="Number.parseInt(item)">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
              :label="$t('proxyMode')"
              width="180">
            <template slot-scope="scope">
              <el-popconfirm
                  :title="$t('confirmDelete')+'?'"
                  @confirm="deleteAccessControl(scope.$index)"
              >
                <el-button slot="reference" type="danger" size="mini">{{ $t('delete') }}</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

    </div>
  </div>
</template>

<script>
import {getAccessControlList, updateAccessControlList} from "@/network/access_control";
import {resetConfig} from "@/network/tunnel";

export default {
  name: "AccessControl",
  data() {
    return {
      mode: [this.$t('default'), this.$t('other')],
      proxyType: {0: this.$t('direct'), 2: this.$t('proxy')},
      accessControlList: [
        {mode: 1, ip: "***********", mac: "", port: "", proxy_type: 0}
      ],
      temp: {mode: 1, ip: "***********", mac: "", port: "", proxy_type: 0},
    }
  },
  mounted() {
    this.initAccessControl()
  },
  methods: {
    deleteAccessControl(index) {
      this.accessControlList.splice(index, 1)
    },
    initAccessControl() {
      getAccessControlList().then(res => {
        if (res.success) {
          this.accessControlList = res.data
        }
      })
    },
    updateAccessControl() {
      if (this.accessControlList.filter(item => item.mode === 0).length > 1) {
        this.$message.error(this.$t('defaultSizeError'))
        return
      }
      if (this.accessControlList.filter(item => item.mode === 1 && item.ip === '' && item.mac === '' && item.port === '').length > 0) {
        this.$message.error(this.$t('ipMacNotNull'))
        return
      }
      updateAccessControlList(this.accessControlList).then(res => {
        if (res.success) {
          this.$message.success(res.message)
          this.initAccessControl()
          resetConfig()
        }
      })
    }
  }
}
</script>


<style scoped>
.nav-link {
  color: orange;
}
</style>