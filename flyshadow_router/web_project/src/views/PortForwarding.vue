<template>
  <div class="container">
    <div class="shadow rounded bg-white p-3">

      <div class="row">
        <div class="col-sm-10">
          <div class="row ms-2">
            ID: {{ portForwardingInfo.uuid }} {{ getRemark(portForwardingInfo.uuid) }}
          </div>
          <div class="row">
            <div>
              <el-button size="mini" @click="handleEditRemark">{{ $t('edit') }}</el-button>
              <el-popconfirm
                  class="ms-2"
                  :title="$t('confirmReset')+'?'"
                  @confirm="resetClientUuid"
              >
                <el-button slot="reference" type="danger" size="mini">{{ $t('reset') }}</el-button>
              </el-popconfirm>
            </div>
          </div>
        </div>

        <div class="ms-auto col-sm-2">
          <el-button class="ms-auto" type="primary" size="mini" @click="handleAdd">{{ $t('add') }}</el-button>
        </div>
      </div>


      <el-dialog :title="$t('remark')" :visible.sync="editRemarkShow" top="1vh" width="90%"
                 @close="editShow = false;initPortForwardingInfo()">
        <el-form label-width="155px">
          <el-form-item :label="$t('remark')">
            <el-input v-model="remark"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSaveRemark">{{ $t('submit') }}</el-button>
            <el-button @click="editRemarkShow = false">{{ $t('cancelBlank') }}</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <el-dialog :title="$t('portForwarding')" :visible.sync="editShow" top="1vh" width="90%"
                 @close="editShow = false;initPortForwardingInfo()">
        <el-form ref="saveForm" :model="editItem" label-width="155px">
          <el-form-item :label="$t('status')"
                        prop="enable"
                        :rules="[{required:true,message:$t('pleaseSelect') + $t('status'), trigger:'blur'}]">
            <el-select v-model="editItem.enable" size="mini">
              <el-option
                  :label="$t('enable')"
                  :value="true">
              </el-option>
              <el-option
                  :label="$t('disable')"
                  :value="false">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('client')+'ID'"
                        prop="uuid"
                        :rules="[{required:true,message:$t('pleaseEnter') + $t('client') + 'ID', trigger:'blur'}]">
            <el-input v-model="portForwardingInfo.uuid" size="mini" disabled/>
          </el-form-item>
          <el-form-item :label="$t('listenPort')"
                        prop="listenPort"
                        :rules="[{required:true,message:$t('pleaseEnter') + $t('listenPort'), trigger:'blur'}]">
            <el-input v-model="editItem.listenPort" size="mini" type="number"/>
          </el-form-item>
          <el-form-item :label="$t('targetAddr')"
                        prop="targetAddr"
                        :rules="[{required:true,message:$t('pleaseEnter') + $t('targetAddr'), trigger:'blur'}]">
            <el-input v-model="editItem.targetAddr" size="mini"/>
          </el-form-item>
          <el-form-item :label="$t('targetUuid')"
                        prop="targetUuid"
                        :rules="[{required:true,message:$t('pleaseSelect') + $t('targetUuid'), trigger:'blur'}]">
            <el-select v-model="editItem.targetUuid" size="mini">
              <el-option
                  v-for="(item,index) in portForwardingInfo.client_list?.filter(item=>item.uuid !== portForwardingInfo.uuid)"
                  :key="item.uuid"
                  :label="item.uuid + getRemark(item.uuid)"
                  :value="item.uuid">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSave('saveForm')">{{ $t('submit') }}</el-button>
            <el-button @click="editShow = false;initPortForwardingInfo()">{{ $t('cancelBlank') }}</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>

      <div class="mt-2 overflow-y-auto" style="height: calc(100vh - 150px)">
        <el-table :data="portForwardingInfo.port_forwarding_list" style="width: 100%" height="100%"
                  row-key="id"
        >
          <el-table-column
              prop="enable"
              :label="$t('status')"
              width="90">
            <template slot-scope="scope">
              <el-tag :type="scope.row.enable?'success':'danger'" size="mini">
                {{ scope.row.enable ? $t('enable') : $t('disable') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
              prop="uuid"
              :label="$t('client')+'ID'"
              width="270">
          </el-table-column>
          <el-table-column
              prop="listenPort"
              :label="$t('listenPort')"
              width="100">
          </el-table-column>
          <el-table-column
              prop="targetAddr"
              :label="$t('targetAddr')"
              width="200">
          </el-table-column>
          <el-table-column
              prop="targetUuid"
              :label="$t('targetUuid')"
              width="270">
            <template slot-scope="scope">
              {{ scope.row.targetUuid }} {{ getRemark(scope.row.targetUuid) }}
            </template>
          </el-table-column>
          <el-table-column
              prop="error_message"
              :label="$t('errorMessage')"
              width="250">
          </el-table-column>

          <el-table-column
              :label="$t('operation')"
              width="160">
            <template slot-scope="scope">
              <el-button size="mini" @click="editPortForwarding(scope.row)" style="margin-right: 5px">
                {{ $t('edit') }}
              </el-button>

              <el-popconfirm
                  :title="$t('confirmDelete')+'?'"
                  @confirm="deletePortForwarding(scope.row.id)"
              >
                <el-button slot="reference" type="danger" size="mini">{{ $t('delete') }}</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

    </div>
  </div>
</template>

<script>
import {
  deletePortForwarding,
  getPortForwardingList,
  resetClientUuid,
  saveOrUpdatePortForwarding,
  updateClientRemark
} from "@/network/port_forwarding";

export default {
  name: "PortForwarding",
  data() {
    return {
      remark: '',
      editItem: {},
      editShow: false,
      editRemarkShow: false,
      portForwardingInfo: {},
    }
  },
  mounted() {
    this.initPortForwardingInfo()
  },
  methods: {
    getRemark(uuid) {
      let remark = this.portForwardingInfo.client_list?.findLast(item => item.uuid === uuid)?.remark;
      if (remark) {
        return '(' + remark + ')'
      }
      return ''
    },
    initPortForwardingInfo() {
      getPortForwardingList().then(res => {
        if (res.success) {
          this.portForwardingInfo = res.data
        }
      })
    },
    deletePortForwarding(id) {
      deletePortForwarding(id).then(res => {
        this.initPortForwardingInfo()
      })
    },
    editPortForwarding(obj) {
      this.editItem = obj
      this.editShow = true
    },
    handleSave(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          saveOrUpdatePortForwarding({...this.editItem, listenPort: Number(this.editItem.listenPort)}).then(res => {
            this.editItem = {}
            this.editShow = false
            this.initPortForwardingInfo()
          })
        } else {
          console.log('error submit ')
          return false
        }
      })
    },
    handleAdd() {
      this.editItem = {
        enable: true,
        uuid: this.portForwardingInfo.uuid
      }
      this.editShow = true
    },
    handleEditRemark() {
      this.remark = this.portForwardingInfo.client_list?.findLast(item => item.uuid === this.portForwardingInfo.uuid)?.remark ?? ''
      this.editRemarkShow = true
    },
    handleSaveRemark() {
      updateClientRemark(this.remark).then(res => {
        this.editRemarkShow = false
        this.initPortForwardingInfo()
      })
    },
    resetClientUuid() {
      let remark = this.portForwardingInfo.client_list?.findLast(item => item.uuid === this.portForwardingInfo.uuid)?.remark ?? ''
      resetClientUuid(remark).then(res => {
        this.initPortForwardingInfo()
      })
    }
  }
}
</script>


<style scoped>

</style>