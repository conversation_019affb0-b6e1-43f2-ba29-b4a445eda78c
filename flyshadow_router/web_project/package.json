{"name": "web_project", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@popperjs/core": "^2.11.8", "axios": "^1.7.7", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "core-js": "^3.8.3", "element-ui": "^2.15.14", "moment": "^2.30.1", "sortablejs": "^1.15.6", "vue": "^2.7.16", "vue-cookies": "^1.8.4", "vue-i18n": "8.22.2", "vue-router": "3.5.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}