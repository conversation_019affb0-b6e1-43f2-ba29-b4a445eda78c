mod controller;
mod service;
mod api;
mod proxy;
mod interceptor;
mod util;

use crate::api::api::Api;
use crate::controller::access_control_controller::AccessControlController;
use crate::controller::config_controller::ConfigController;
use crate::controller::connect_list_controller;
use crate::controller::connect_list_controller::ConnectListController;
use crate::controller::custom_rule_controller::CustomRuleController;
use crate::controller::port_forwarding_controller::PortForwardingController;
use crate::controller::tunnel_controller::{tunnel_start, TunnelController};
use crate::controller::web_password_controller::WebPasswordController;
use crate::interceptor::header_interceptor::HeaderInterceptor;
use crate::service::access_control_service::AccessControlService;
use crate::service::config_service::ConfigService;
use crate::service::connect_list_service::ConnectListService;
use crate::service::tproxy_service::TProxyService;
use crate::service::tun_service::TunService;
use crate::service::tunnel_service::TunnelService;
use crate::util::command_util::CommandUtil;
use actix_cors::Cors;
use actix_web::web::Data;
use actix_web::{App, HttpServer};
use actix_web_static_files::ResourceFiles;
use log::{error, info, Level};
use proxy::tproxy::Tproxy;
use std::sync::{Arc, RwLock};
use std::time::Duration;
use tokio::spawn;
use tokio::time::sleep;
use tunnel::context::context::TunnelContext;
use tunnel::proxy::proxy::Proxy;
use tunnel::tun::tun::Tun;

struct AppState {
    web_password: RwLock<String>,
    config_service: Arc<ConfigService>,
    api: Api,
    tun_service: TunService,
    tproxy_service: TProxyService,
    tunnel_service: TunnelService,
    access_control_service: AccessControlService,
    connect_list_service: ConnectListService,
    proxy: Proxy,
}

impl Drop for AppState {
    fn drop(&mut self) {
        let _ = CommandUtil::clean_iptables();
    }
}

include!(concat!(env!("OUT_DIR"), "/generated.rs"));

const MMDB_DATA: &[u8] = include_bytes!("../static/Country-only-cn-private.mmdb");

#[tokio::main]
async fn main() -> std::io::Result<()> {
    let _ = simple_logger::init_with_level(Level::Info);

    let config_service = ConfigService::init_config().await;

    let tunnel_context = Arc::new(TunnelContext::new().await);
    tunnel_context.set_geoip("CN".to_string(), MMDB_DATA.to_owned()).await;
    let tun = Arc::new(Tun::new(tunnel_context.clone()).await);
    let tproxy = Arc::new(Tproxy::new(tunnel_context.clone()));


    let web_port = config_service.config.read().await.web_port;
    let auto_connect_onstart = config_service.config.read().await.auto_connect_after_startup;
    let proxy_port = config_service.config.read().await.proxy_port;

    let proxy = Proxy::new(tunnel_context.clone());
    match proxy.start(proxy_port).await {
        Ok(_) => {}
        Err(e) => {
            log::error!("start proxy error: {}", e);
        }
    };

    let state = Data::new(AppState {
        web_password: RwLock::new("".to_string()),
        config_service: Arc::new(config_service),
        api: Api::new(),
        tun_service: TunService::new(tun),
        tproxy_service: TProxyService::new(tproxy),
        tunnel_service: TunnelService::new(tunnel_context.clone()).await,
        access_control_service: AccessControlService::new(),
        connect_list_service: ConnectListService::new(tunnel_context.clone()),
        proxy,
    });

    info!("Starting web server at 0.0.0.0:{}",web_port);

    if auto_connect_onstart {
        let state_clone = state.clone();
        spawn(async move {
            sleep(Duration::from_secs(5)).await;
            info!("Connect the server automatically after 5 seconds");
            if let Err(e) = TunnelController::tunnel_start0(state_clone).await {
                error!("Connect the server error: {}",e)
            }
        });
    }

    HttpServer::new(move || {
        App::new()
            // .wrap(Logger::default())
            .wrap(HeaderInterceptor {
                app_state: state.clone()
            })
            .wrap(Cors::default()
                .allow_any_origin()
                .allow_any_method()
                .allow_any_header()
                .max_age(3600)
            )
            .configure(WebPasswordController::init_route)
            .configure(ConnectListController::init_route)
            .configure(ConfigController::init_route)
            .configure(CustomRuleController::init_route)
            .configure(PortForwardingController::init_route)
            .configure(AccessControlController::init_route)
            .configure(TunnelController::init_route)
            .service(ResourceFiles::new("/", generate()).resolve_not_found_to_root())
            .app_data(state.clone())
    })
        .bind(("0.0.0.0", web_port))?
        .run()
        .await
}
