use crate::api::port_forwarding_entity::ClientEntity;
use crate::controller::response_entity::ResponseObj;
use crate::service::config_service::Config;
use crate::{f_response_json_fail, f_response_json_success, AppState};
use actix_web::web::{Data, Json};
use actix_web::{get, post, web, HttpResponse, Responder};
use serde_json::Value;

pub struct ConfigController;
const VERSION: &[u8] = include_bytes!("../../static/version");

#[get("/version")]
async fn version() -> impl Responder {
    f_response_json_success!(String::from_utf8_lossy(VERSION))
}
#[get("/config/get")]
async fn config_get(data: Data<AppState>) -> impl Responder {
    let guard = data.config_service.config.read().await;
    f_response_json_success!(guard.clone())
}

#[post("/config/update")]
async fn config_update(config: Json<Config>, data: Data<AppState>) -> impl Responder {
    *data.config_service.config.write().await = config.0;
    data.config_service.save_config().await;
    f_response_json_success!()
}

#[post("/config/setSubscribePassword")]
async fn set_subscribe_password(body: Json<Value>, data: Data<AppState>) -> impl Responder {
    let password = if let Some(password) = body.0["password"].as_str() {
        password
    } else {
        return f_response_json_fail!("Password cannot be empty")
    };

    match data.api.get_subscribe_content(password.to_string()).await {
        Ok(subscribe_content) => {
            let mut config = data.config_service.config.write().await;
            config.subscribe_content_entity = subscribe_content;
            config.subscribe_password = password.to_string();

            // PortForwarding
            match data.api.get_port_forwarding_list(password.to_string()).await {
                Ok(mut port_forwarding_list) => {
                    config.port_forwarding_list.clear();
                    config.port_forwarding_list.append(&mut port_forwarding_list);
                }
                Err(e) => {
                    return f_response_json_fail!(e)
                }
            }

            // CustomRule
            match data.api.get_custom_rule_list(password.to_string()).await {
                Ok(custom_rule_list) => {
                    config.custom_rule.clear();
                    config.custom_rule.append(&mut custom_rule_list.clone());
                }
                Err(e) => {
                    return f_response_json_fail!(e)
                }
            }

            // Client UUID
            if let Err(e) = data.api.save_or_update_client(password.to_string(), ClientEntity { uuid: config.uuid.clone(), remark: None }).await {
                return f_response_json_fail!(e)
            }

            drop(config);

            data.config_service.save_config().await;
        }
        Err(e) => {
            return f_response_json_fail!(e)
        }
    };

    f_response_json_success!()
}

impl ConfigController {
    pub fn init_route(cfg: &mut web::ServiceConfig) {
        cfg.service(version)
            .service(config_get)
            .service(config_update)
            .service(set_subscribe_password);
    }
}

