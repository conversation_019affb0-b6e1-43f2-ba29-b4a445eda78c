use crate::controller::response_entity::ResponseObj;
use crate::{f_response_json_fail, f_response_json_success, AppState};
use actix_web::web::{Data, Json};
use actix_web::HttpResponse;
use actix_web::{get, post, web, Responder};
use serde_json::Value;

pub struct ConnectListController;

#[get("/connectList/get")]
async fn connect_list_get(data: Data<AppState>) -> impl Responder {
    f_response_json_success!(data.connect_list_service.get_connection_list().await)
}
#[post("/connectList/close")]
async fn connect_list_close(json: Json<Value>, data: Data<AppState>) -> impl Responder {
    let key = if let Some(node_name) = json.get("key").map_or(None, |n| n.as_str()) {
        node_name.to_string()
    } else {
        return f_response_json_fail!("Connect key is error");
    };
    data.connect_list_service.close_connection_list(key).await;
    f_response_json_success!()
}


impl ConnectListController {
    pub fn init_route(cfg: &mut web::ServiceConfig) {
        cfg.service(connect_list_get)
            .service(connect_list_close);
    }
}
