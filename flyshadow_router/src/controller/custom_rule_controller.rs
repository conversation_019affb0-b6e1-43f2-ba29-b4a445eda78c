use crate::api::custom_rule_entity::CustomRuleEntity;
use crate::controller::response_entity::ResponseObj;
use crate::{f_response_json_fail, f_response_json_success, AppState};
use actix_web::web::{Data, Json};
use actix_web::{get, post, web, HttpResponse, Responder};
use serde_json::Value;

pub struct CustomRuleController {}

#[get("/custom/rule/list")]
async fn custom_rule_list(data: Data<AppState>) -> impl Responder {
    let mut config = data.config_service.config.write().await;

    let password = &config.subscribe_password;
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    match data.api.get_custom_rule_list(password.to_string()).await {
        Ok(custom_rule_list) => {
            config.custom_rule.clear();
            config.custom_rule.append(&mut custom_rule_list.clone());

            drop(config);

            data.config_service.save_config().await;

            let mut config = data.config_service.config.write().await;

            if let Err(e) = data.tunnel_service.reset_from_config(true, &mut config, &data.access_control_service, &data.proxy).await {
                f_response_json_fail!(format!("Reset config error: {}",e));
            }

            f_response_json_success!(custom_rule_list)
        }
        Err(e) => {
            f_response_json_fail!(e)
        }
    }
}

#[post("/custom/rule/save")]
async fn custom_rule_save(custom_rule_entity: Json<CustomRuleEntity>, data: Data<AppState>) -> impl Responder {
    let config = data.config_service.config.read().await;

    let password = &config.subscribe_password;
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    match data.api.save_custom_rule(password.to_string(), custom_rule_entity.0).await {
        Ok(_) => {
            f_response_json_success!()
        }
        Err(e) => {
            f_response_json_fail!(e)
        }
    }
}

#[post("/custom/rule/update")]
async fn custom_rule_update(custom_rule_entity: Json<CustomRuleEntity>, data: Data<AppState>) -> impl Responder {
    let config = data.config_service.config.read().await;

    let password = &config.subscribe_password;
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    match data.api.update_custom_rule(password.to_string(), custom_rule_entity.0).await {
        Ok(_) => {
            f_response_json_success!()
        }
        Err(e) => {
            f_response_json_fail!(e)
        }
    }
}

#[post("/custom/rule/updateList")]
async fn custom_rule_update_list(custom_rule_entity_list: Json<Vec<CustomRuleEntity>>, data: Data<AppState>) -> impl Responder {
    let config = data.config_service.config.read().await;

    let password = &config.subscribe_password;
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    match data.api.update_custom_rule_list(password.to_string(), custom_rule_entity_list.0).await {
        Ok(_) => {
            f_response_json_success!()
        }
        Err(e) => {
            f_response_json_fail!(e)
        }
    }
}
#[post("/custom/rule/delete")]
async fn custom_rule_delete(value: Json<Value>, data: Data<AppState>) -> impl Responder {
    let id = if let Some(id) = value.0["id"].as_i64() {
        id
    } else {
        return f_response_json_fail!("Password cannot be empty")
    };
    let config = data.config_service.config.read().await;

    let password = &config.subscribe_password;
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    match data.api.delete_custom_rule(password.to_string(), id).await {
        Ok(_) => {
            f_response_json_success!()
        }
        Err(e) => {
            f_response_json_fail!(e)
        }
    }
}


impl CustomRuleController {
    pub fn init_route(cfg: &mut web::ServiceConfig) {
        cfg.service(custom_rule_list)
            .service(custom_rule_save)
            .service(custom_rule_update_list)
            .service(custom_rule_update)
            .service(custom_rule_delete);
    }
}
