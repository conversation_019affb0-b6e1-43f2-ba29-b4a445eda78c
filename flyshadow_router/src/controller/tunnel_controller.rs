use crate::controller::response_entity::ResponseObj;
use crate::{f_response_json_fail, f_response_json_success, AppState};
use actix_web::web::{Data, Json};
use actix_web::{get, post, web, HttpResponse, Responder};
use log::info;
use serde_json::{json, Value};
use std::future::Future;
use std::ops::DerefMut;
use tunnel::tunnel::tunnel::{ConnectInfo, Tunnel};

pub struct TunnelController;

impl TunnelController {
    pub async fn tunnel_start0(data: Data<AppState>) -> Result<(), String> {
        let mut config = data.config_service.config.write().await;
        let access_control_service = &data.access_control_service;
        let select_node_name = config.select_node_name.clone();

        if select_node_name == "" {
            return Err("Please select server".to_string());
        }

        let node = match config.subscribe_content_entity.node.iter().find(|item| item.name == select_node_name) {
            None => {
                return Err("Server not found".to_string())
            }
            Some(node) => {
                node.clone()
            }
        };

        if let Err(e) = data.tunnel_service.set_device().await {
            return Err(format!("Server start failed: {}", e));
        }
        if let Err(e) = data.tunnel_service.reset_from_config(false, &mut config, access_control_service, &data.proxy).await {
            return Err(format!("Reset config error: {}", e));
        }

        if config.tun_mode_enable {
            if let Err(e) = data.tun_service.start_tun(&config.access_control).await {
                data.tun_service.stop_tun().await;
                return Err(format!("Server failed to start proxy: {}", e));
            }
        }
        if config.tproxy_mode_type > 0 {
            if let Err(e) = data.tproxy_service.start_tproxy(config.tproxy_port, config.tproxy_mode_type).await {
                data.tproxy_service.stop_tproxy().await;
                return Err(format!("Server failed to start tproxy: {}", e));
            }
        }

        data.tunnel_service.connect_tunnel(&node, config.native_udp).await;
        info!("start tunnel successful");

        Ok(())
    }
}

/// 启动隧道
#[post("/tunnel/start")]
pub async fn tunnel_start(data: Data<AppState>) -> impl Responder {
    match TunnelController::tunnel_start0(data).await {
        Ok(()) => {
            f_response_json_success!()
        }
        Err(e) => {
            f_response_json_fail!(e)
        }
    }
}

/// 停止隧道
#[post("/tunnel/stop")]
async fn tunnel_stop(data: Data<AppState>) -> impl Responder {
    data.tunnel_service.close_tunnel().await;
    data.tun_service.stop_tun().await;
    data.tproxy_service.stop_tproxy().await;
    data.access_control_service.clean_iptables();
    f_response_json_success!()
}

/// 获取隧道状态
#[get("/tunnel/stats")]
async fn tunnel_stats(data: Data<AppState>) -> impl Responder {
    let status = *data.tunnel_service.status.read().await;
    let download = *data.tunnel_service.download.read().await;
    let upload = *data.tunnel_service.upload.read().await;
    let enabled = *data.tunnel_service.enabled.read().await;

    f_response_json_success!(json!({ "status": status, "download": download, "upload": upload, "enabled": enabled}))
}

/// 选择节点
#[post("/tunnel/select_node")]
async fn tunnel_select_node(json: Json<Value>, data: Data<AppState>) -> impl Responder {
    let node_name = if let Some(node_name) = json.get("node_name").map_or(None, |n| n.as_str()) {
        node_name.to_string()
    } else {
        return f_response_json_fail!("Server is null");
    };
    let mut config = data.config_service.config.write().await;

    info!("node_name:{}",node_name);

    if let Some(node) = config.subscribe_content_entity.node.iter().find(|item| item.name == node_name) {
        data.tunnel_service.reselect(node, config.native_udp).await;
        config.select_node_name = node_name;
        drop(config);
        data.config_service.save_config().await;
        f_response_json_success!()
    } else {
        f_response_json_fail!("Server not found")
    }
}

/// 设置代理类型
#[post("/tunnel/reset_config")]
async fn tunnel_reset_config(data: Data<AppState>) -> impl Responder {
    let mut config = data.config_service.config.write().await;

    if let Err(e) = data.tunnel_service.reset_from_config(true, &mut config, &data.access_control_service, &data.proxy).await {
        f_response_json_fail!(format!("Reset config error: {}",e));
    }
    f_response_json_success!()
}

/// 速度测试
#[post("/tunnel/test_ping")]
async fn tunnel_test_ping(json: Json<Value>, data: Data<AppState>) -> impl Responder {
    let node_name = if let Some(node_name) = json.get("node_name").map_or(None, |n| n.as_str()) {
        node_name.to_string()
    } else {
        return f_response_json_fail!("Server is null");
    };
    let config = data.config_service.config.read().await;

    if let Some(node) = config.subscribe_content_entity.node.iter().find(|item| item.name == node_name) {
        let connect_info = ConnectInfo {
            host: node.host.clone(),
            port: node.port,
            password: node.password.clone(),
        };
        f_response_json_success!(Tunnel::test_ping_by_struct(connect_info).await)
    } else {
        f_response_json_fail!("Server not found")
    }
}


impl TunnelController {
    pub fn init_route(cfg: &mut web::ServiceConfig) {
        cfg.service(tunnel_start)
            .service(tunnel_stop)
            .service(tunnel_stats)
            .service(tunnel_select_node)
            .service(tunnel_reset_config)
            .service(tunnel_test_ping);
    }
}
