use crate::controller::response_entity::ResponseObj;
use crate::{f_response_json_fail, f_response_json_success, AppState};
use actix_web::web::{Data, Json};
use actix_web::{get, post, web, HttpResponse, Responder};
use serde_json::Value;

pub struct WebPasswordController;

#[get("/web/password/get")]
async fn password_get(data: Data<AppState>) -> impl Responder {
    f_response_json_success!(*data.web_password.read().unwrap() != "")
}
#[post("/web/password/check")]
async fn password_check(body: Json<Value>, data: Data<AppState>) -> impl Responder {
    if *data.web_password.write().unwrap() == "" {
        return f_response_json_fail!("Please set password");
    }
    let password = if let Some(password) = body.0["password"].as_str() {
        password
    } else {
        return f_response_json_fail!("Password cannot be empty")
    };
    if password == "" {
        return f_response_json_fail!("Password cannot be empty");
    }
    f_response_json_success!(*data.web_password.write().unwrap() == password.to_string())
}

#[post("/web/password/set")]
async fn password_set(body: Json<Value>, data: Data<AppState>) -> impl Responder {
    if *data.web_password.write().unwrap() != "" {
        return f_response_json_fail!("Password has been set");
    }
    let password = if let Some(password) = body.0["password"].as_str() {
        password
    } else {
        return f_response_json_fail!("Password cannot be empty")
    };
    if password == "" {
        return f_response_json_fail!("Password cannot be empty");
    }
    *data.web_password.write().unwrap() = password.to_string();
    f_response_json_success!()
}

impl WebPasswordController {
    pub fn init_route(cfg: &mut web::ServiceConfig) {
        cfg.service(password_get)
            .service(password_set)
            .service(password_check);
    }
}

