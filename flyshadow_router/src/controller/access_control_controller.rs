use crate::controller::response_entity::ResponseObj;
use crate::service::access_control_service::AccessControl;
use crate::{f_response_json_success, AppState};
use actix_web::web::{Data, Json};
use actix_web::HttpResponse;
use actix_web::{get, post, web, Responder};

pub struct AccessControlController;

#[get("/access/control/get")]
async fn access_control_get(data: Data<AppState>) -> impl Responder {
    let guard = data.config_service.config.read().await;
    f_response_json_success!(guard.access_control.clone())
}

#[post("/access/control/update")]
async fn access_control_update(mut config: Json<Vec<AccessControl>>, data: Data<AppState>) -> impl Responder {
    {
        let mut guard = data.config_service.config.write().await;
        guard.access_control.clear();
        guard.access_control.append(&mut config.0);
    }
    data.config_service.save_config().await;
    f_response_json_success!()
}


impl AccessControlController {
    pub fn init_route(cfg: &mut web::ServiceConfig) {
        cfg.service(access_control_get)
            .service(access_control_update);
    }
}
