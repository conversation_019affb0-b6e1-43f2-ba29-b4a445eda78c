use crate::api::port_forwarding_entity::{ClientEntity, PortForwardingEntity, PortForwardingInfo};
use crate::controller::response_entity::ResponseObj;
use crate::{f_response_json_fail, f_response_json_success, AppState};
use actix_web::web::{Data, Json};
use actix_web::{get, post, web, HttpResponse, Responder};
use serde_json::Value;
use uuid::Uuid;

pub struct PortForwardingController {}


#[get("/port/forwarding/list")]
async fn port_forwarding_list(data: Data<AppState>) -> impl Responder {
    let mut config = data.config_service.config.write().await;

    let password = &config.subscribe_password;
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    let mut port_forwarding_list = match data.api.get_port_forwarding_list(password.to_string()).await {
        Ok(port_forwarding_list) => {
            port_forwarding_list
        }
        Err(e) => {
            return f_response_json_fail!(e)
        }
    };

    let client_list = match data.api.get_client_list(password.to_string()).await {
        Ok(client_list) => {
            client_list
        }
        Err(e) => {
            return f_response_json_fail!(e)
        }
    };

    config.port_forwarding_list.clear();
    config.port_forwarding_list.append(&mut port_forwarding_list);

    drop(config);

    data.config_service.save_config().await;

    let mut config = data.config_service.config.write().await;

    if let Err(e) = data.tunnel_service.reset_from_config(true, &mut config, &data.access_control_service, &data.proxy).await {
        f_response_json_fail!(format!("Reset config error: {}",e));
    }

    let port_forwarding_info = PortForwardingInfo {
        uuid: config.uuid.clone(),
        client_list,
        port_forwarding_list: config.port_forwarding_list.clone(),
    };

    f_response_json_success!(port_forwarding_info)
}

#[post("/port/forwarding/remark")]
async fn update_client_remark(body: Json<Value>, data: Data<AppState>) -> impl Responder {
    let config = data.config_service.config.read().await;

    let password = &config.subscribe_password;
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    let remark = if let Some(remark) = body.0["remark"].as_str() {
        remark
    } else {
        return f_response_json_fail!("Remark cannot be empty")
    };

    if let Err(e) = data.api.save_or_update_client(password.to_string(), ClientEntity {
        uuid: config.uuid.clone(),
        remark: Some(remark.to_string()),
    }).await {
        return f_response_json_fail!(e);
    }

    f_response_json_success!()
}


#[post("/port/forwarding/save_or_update")]
async fn save_or_update_port_forwarding(body: Json<PortForwardingEntity>, data: Data<AppState>) -> impl Responder {
    let config = data.config_service.config.read().await;

    let password = &config.subscribe_password;
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    match data.api.save_or_update_port_forwarding(password.to_string(), body.0).await {
        Ok(_) => {
            f_response_json_success!()
        }
        Err(e) => {
            f_response_json_fail!(e)
        }
    }
}
#[post("/port/forwarding/delete")]
async fn delete_port_forwarding(body: Json<Value>, data: Data<AppState>) -> impl Responder {
    let config = data.config_service.config.read().await;

    let password = &config.subscribe_password;
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    let id = if let Some(id) = body.0["id"].as_i64() {
        id
    } else {
        return f_response_json_fail!("Password cannot be empty")
    };

    match data.api.delete_port_forwarding(password.to_string(), id).await {
        Ok(_) => {
            f_response_json_success!()
        }
        Err(e) => {
            f_response_json_fail!(e)
        }
    }
}


#[post("/port/forwarding/reset")]
async fn reset_client_uuid(body: Json<Value>, data: Data<AppState>) -> impl Responder {
    let mut config = data.config_service.config.write().await;

    let password = config.subscribe_password.clone();
    if password.is_empty() {
        return f_response_json_fail!("Subscribe Password cannot be empty");
    }

    let remark = body.0["remark"].as_str().unwrap_or("").to_string();

    let uuid = Uuid::new_v4().simple().encode_lower(&mut Uuid::encode_buffer()).to_string();
    config.uuid = uuid.clone();

    drop(config);
    data.config_service.save_config().await;

    match data.api.save_or_update_client(password, ClientEntity {
        uuid,
        remark: Some(remark),
    }).await {
        Ok(_) => {
            f_response_json_success!()
        }
        Err(e) => {
            f_response_json_fail!(e)
        }
    }
}

impl PortForwardingController {
    pub fn init_route(cfg: &mut web::ServiceConfig) {
        cfg.service(port_forwarding_list)
            .service(update_client_remark)
            .service(save_or_update_port_forwarding)
            .service(delete_port_forwarding)
            .service(reset_client_uuid);
    }
}
