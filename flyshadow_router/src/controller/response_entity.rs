use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct ResponseObj<T> {
    success: bool,
    message: String,
    data: Option<T>,
}

impl ResponseObj<Option<()>> {
    pub fn success() -> ResponseObj<Option<()>> {
        ResponseObj {
            success: true,
            message: "Success".to_string(),
            data: None,
        }
    }

    pub fn error(message: String) -> ResponseObj<Option<()>> {
        ResponseObj {
            success: false,
            message,
            data: None,
        }
    }
}

impl<T> ResponseObj<T> {
    pub fn success_data(t: T) -> ResponseObj<T> {
        ResponseObj {
            success: true,
            message: "Success".to_string(),
            data: Some(t),
        }
    }
}

#[macro_export]
macro_rules! f_response_json_success {
    ($data:expr) => {
        HttpResponse::Ok().json(ResponseObj::success_data($data))
    };
    () => {
        HttpResponse::Ok().json(ResponseObj::success())
    };
}
#[macro_export]
macro_rules! f_response_json_fail {
    ($message:expr) => {{
        HttpResponse::Ok().json(ResponseObj::error($message.to_string()))
    }};
}