use crate::controller::response_entity::ResponseObj;
use crate::{f_response_json_fail, AppState};
use actix_web::body::EitherBody;
use actix_web::dev::{Service, ServiceRequest, ServiceResponse, Transform};
use actix_web::web::Data;
use actix_web::{Error, HttpResponse};
use futures_util::future::LocalBoxFuture;
use futures_util::{future, FutureExt};
use std::path::Path;
use std::task::{Context, Poll};

pub struct HeaderInterceptor {
    pub(crate) app_state: Data<AppState>,
}

pub struct HeaderInterceptorMiddleware<S> {
    service: S,
    app_state: Data<AppState>,
}

impl<S, B> Transform<S, ServiceRequest> for HeaderInterceptor
where
    S: Service<ServiceRequest, Response=ServiceResponse<B>, Error=Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<EitherBody<B>>;
    type Error = Error;
    type Transform = HeaderInterceptorMiddleware<S>;
    type InitError = ();
    type Future = future::Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        future::ok(HeaderInterceptorMiddleware { service, app_state: self.app_state.clone() })
    }
}

impl<S, B> Service<ServiceRequest> for HeaderInterceptorMiddleware<S>
where
    S: Service<ServiceRequest, Response=ServiceResponse<B>, Error=Error> + 'static,
    S: 'static,
{
    type Response = ServiceResponse<EitherBody<B>>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    fn poll_ready(&self, ctx: &mut Context<'_>) -> Poll<Result<(), Self::Error>> {
        self.service.poll_ready(ctx)
    }

    fn call(&self, req: ServiceRequest) -> Self::Future {
        //
        let path = req.path();
        let is_static = Path::new(path).extension().map_or(false, |ext| {
            matches!(ext.to_str(),Some("html"|"css" | "js"|"png"|"jpg"|"jpeg"|"gif"|"ico"|"svg"|"woff"|"woff2"|"ttf"))
        }) || path.starts_with("/web/password") || path == ("/version") || path == "/" || path == "";

        if !is_static {
            if *self.app_state.web_password.write().unwrap() == "" {
                let (http_req, _) = req.into_parts();
                let res = ServiceResponse::new(http_req, f_response_json_fail!("Please set the password"));
                return (async move { Ok(res.map_into_right_body()) }).boxed_local();
            }
            if let Some(header_password) = req.headers().get("X-Password") {
                if header_password.to_str().unwrap_or_default() != *self.app_state.web_password.write().unwrap() {
                    let (http_req, _) = req.into_parts();
                    let res = ServiceResponse::new(http_req, f_response_json_fail!("Password error"));
                    return (async move { Ok(res.map_into_right_body()) }).boxed_local();
                }
            } else {
                let (http_req, _) = req.into_parts();
                let res = ServiceResponse::new(http_req, f_response_json_fail!("Password error"));
                return (async move { Ok(res.map_into_right_body()) }).boxed_local();
            }
        }

        let fut = self.service.call(req);
        Box::pin(async move {
            let res = fut.await?;
            Ok(res.map_into_left_body())
        })
    }
}