use crate::proxy::tproxy::Tproxy;
use std::sync::Arc;
use tokio::sync::RwLock;

pub struct TProxyService {
    tproxy_enable:RwLock<bool>,
    tproxy: Arc<Tproxy>,
}

impl TProxyService {
    pub fn new(tproxy: Arc<Tproxy>) -> TProxyService {
        TProxyService {
            tproxy_enable: RwLock::new(false),
            tproxy,
        }
    }
}

impl TProxyService {
    pub async fn start_tproxy(&self, tproxy_port: u16, tproxy_mode_type: u16) -> Result<(), String> {
        if *self.tproxy_enable.read().await {
            return Ok(());
        }

        self.tproxy.start(tproxy_port, tproxy_mode_type).await.map_err(|e| format!("tproxy start error:{}", e))?;

        Ok(())
    }

    pub async fn stop_tproxy(&self) {
        self.tproxy.shutdown().await;

        *self.tproxy_enable.write().await = false;
    }

}