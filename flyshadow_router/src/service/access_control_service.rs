use crate::service::config_service::Config;
use crate::util::command_util::CommandUtil;
use serde::{Deserialize, Serialize};


#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AccessControl {
    // 0:default, 1:other
    pub mode: u8,
    pub ip: String,
    pub mac: String,
    pub port: String,
    pub proxy_type: u8,
}

pub struct AccessControlService;

impl AccessControlService {
    pub fn new() -> AccessControlService {
        AccessControlService {}
    }
    pub fn set_iptables(&self, config: &Config) -> Result<(), String> {
        let _ = CommandUtil::clean_iptables();
        if config.tproxy_mode_type == 1 {
            let result = CommandUtil::set_tproxy_iptables(config.tproxy_port, &config.access_control);
            if result.is_err() {
                let _ = CommandUtil::clean_iptables();
            }
            result
        } else if config.tproxy_mode_type == 2 {
            let result = CommandUtil::set_redirect_iptables(config.tproxy_port, &config.access_control);
            if result.is_err() {
                let _ = CommandUtil::clean_iptables();
            }
            result
        } else {
            Ok(())
        }
    }
    pub fn clean_iptables(&self) {
        let _ = CommandUtil::clean_iptables();
    }
}

