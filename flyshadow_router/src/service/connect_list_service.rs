use std::sync::Arc;
use tunnel::context::context::TunnelContext;

pub struct ConnectListService {
    tunnel_context: Arc<TunnelContext>,
}

impl ConnectListService {
    pub async fn get_connection_list(&self) -> String {
        self.tunnel_context.get_tunnel_mapper_info_json().await
    }
    pub async fn close_connection_list(&self, key: String) {
        self.tunnel_context.close_connect(key).await;
    }
}

impl ConnectListService {
    pub fn new(tunnel_context: Arc<TunnelContext>) -> ConnectListService {
        ConnectListService {
            tunnel_context,
        }
    }
}