use crate::api::custom_rule_entity::CustomRuleEntity;
use crate::api::port_forwarding_entity::PortForwardingEntity;
use crate::api::subscribe_content_entity::SubscribeContentEntity;
use crate::controller::port_forwarding_controller::port_forwarding_list;
use crate::service::access_control_service::AccessControl;
use log::{error, info};
use serde::{Deserialize, Serialize};
use std::fs;
use std::fs::File;
use std::io::Write;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

pub struct ConfigService {
    pub config: Arc<RwLock<Config>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Config {
    pub web_port: u16,
    pub proxy_port: u16,
    pub tproxy_port: u16,
    // 透明代理 0关闭 1透明代理 2重定向
    pub tproxy_mode_type: u16,
    pub subscribe_password: String,
    pub select_node_name: String,
    pub native_udp: bool,
    pub proxy_type: u8,
    pub udp_proxy_type: u8,
    pub tun_mode_enable: bool,
    pub ipv6_enable: bool,
    pub subscribe_content_entity: SubscribeContentEntity,
    pub direct_conn_priority: bool,
    pub direct_conn_priority_timeout: u64,
    pub use_build_in_dns: bool,
    pub access_control: Vec<AccessControl>,
    pub auto_connect_after_startup: bool,
    pub delay_sort: u8,
    pub uuid: String,
    pub custom_rule: Vec<CustomRuleEntity>,
    pub port_forwarding_list: Vec<PortForwardingEntity>,
}


impl ConfigService {
    pub async fn init_config() -> ConfigService {
        if let Err(e) = fs::create_dir("/etc/flyshadow") {
            error!("failed to create /etc/flyshadow dir: {}", e);
        }

        let config = Config {
            web_port: 6780,
            proxy_port: 6785,
            tproxy_port: 6786,
            tproxy_mode_type: 2,
            subscribe_password: "".to_string(),
            select_node_name: "".to_string(),
            native_udp: false,
            proxy_type: 3,
            udp_proxy_type: 0,
            tun_mode_enable: false,
            ipv6_enable: false,
            subscribe_content_entity: SubscribeContentEntity::default(),
            direct_conn_priority: true,
            direct_conn_priority_timeout: 180,
            use_build_in_dns: true,
            access_control: vec![],
            auto_connect_after_startup: false,
            delay_sort: 0,
            uuid: Uuid::new_v4().simple().encode_lower(&mut Uuid::encode_buffer()).to_string(),
            custom_rule: vec![],
            port_forwarding_list: vec![],
        };

        let config_service = ConfigService {
            config: Arc::new(RwLock::new(config)),
        };

        config_service.read_config().await;
        config_service.save_config().await;

        config_service
    }

    async fn read_config(&self) {
        if let Ok(file) = File::open("/etc/flyshadow/config.json") {
            if let Ok(config) = serde_json::from_reader(file) {
                info!("read config successful");
                *self.config.write().await = config;
            }
        }
    }

    pub async fn save_config(&self) {
        let str = serde_json::to_string(&*self.config.read().await).unwrap();
        File::create("/etc/flyshadow/config.json").unwrap().write_all(str.as_bytes()).unwrap();
        info!("save config successful");
    }
}