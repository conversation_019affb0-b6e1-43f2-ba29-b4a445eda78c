use crate::api::node_info_entity::NodeInfoEntity;
use crate::service::access_control_service::AccessControlService;
use crate::service::config_service::Config;
use crate::util::command_util::CommandUtil;
use flyshadow_common::interface::interface_selector::{IfaceSelectType, InterfaceSelector};
use std::sync::Arc;
use std::time::Duration;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::Jo<PERSON><PERSON>and<PERSON>;
use tokio::time::sleep;
use tunnel::context::context::TunnelContext;
use tunnel::proxy::proxy::Proxy;

pub struct TunnelService {
    pub status: Arc<RwLock<i32>>,
    pub download: Arc<RwLock<i64>>,
    pub upload: Arc<RwLock<i64>>,
    pub enabled: Arc<RwLock<bool>>,
    tunnel_context: Arc<TunnelContext>,
    get_status_job: RwLock<Option<JoinHandle<()>>>,
}

impl TunnelService {
    pub async fn set_device(&self) -> Result<(), String> {
        let interface_selector
            = InterfaceSelector::select_iface(IfaceSelectType::DEVICE, self.tunnel_context.get_ipv6_enable().await).await
            .map_err(|e| format!("select interface error: {}", e))?;

        CommandUtil::set_sys_conf(&interface_selector)?;

        let _ = self.tunnel_context.set_local_interface(interface_selector).await;
        Ok(())
    }
    pub async fn reset_from_config(&self, check_enable: bool, config: &mut Config, access_control_service: &AccessControlService, proxy: &Proxy) -> Result<(), String> {
        if check_enable && !*self.enabled.write().await {
            return Ok(());
        }

        proxy.start(config.proxy_port).await.map_err(|e| format!("start proxy error: {}", e))?;
        self.tunnel_context.set_ipv6_enable(config.ipv6_enable).await;
        self.tunnel_context.set_tun_mode_enable(config.tun_mode_enable).await;
        self.tunnel_context.set_proxy_type(config.proxy_type as i32).await;
        self.tunnel_context.set_fake_ip(true).await;
        self.tunnel_context.set_direct_conn_priority(config.direct_conn_priority).await;
        self.tunnel_context.set_use_build_in_dns(config.use_build_in_dns).await;
        self.tunnel_context.set_direct_conn_priority_timeout(config.direct_conn_priority_timeout).await;
        // PortForwarding
        self.tunnel_context.set_client_uuid(config.uuid.clone()).await;
        self.tunnel_context.clear_port_forwarding().await;
        for port_forwarding_entity in &mut config.port_forwarding_list
            .iter_mut()
            .filter(|port_forwarding_entity| port_forwarding_entity.enable && port_forwarding_entity.uuid == config.uuid) {
            if let Err(e) = self.tunnel_context.add_port_forwarding(port_forwarding_entity.listen_port,
                                                                    port_forwarding_entity.target_addr.clone(),
                                                                    port_forwarding_entity.target_uuid.clone()).await {
                port_forwarding_entity.error_message = Some(e);
            } else {
                port_forwarding_entity.error_message = None;
            }
        }

        if config.native_udp {
            match config.subscribe_content_entity.node.iter().find(|item| item.name == config.select_node_name) {
                None => {
                    self.tunnel_context.set_native_udp(false).await;
                }
                Some(node) => {
                    self.tunnel_context.set_native_udp(node.native_udp.unwrap_or(0) == 1).await;
                }
            }
        } else {
            self.tunnel_context.set_native_udp(config.native_udp).await;
        }

        self.tunnel_context.set_udp_proxy_type(config.udp_proxy_type as i32).await;

        self.tunnel_context.clear_domain_rule().await;
        for custom_rule_entity in &config.subscribe_content_entity.custom_rule {
            self.tunnel_context.set_domain_rule_obj(&custom_rule_entity.domain,
                                                    custom_rule_entity.matching,
                                                    custom_rule_entity.proxy_type,
                                                    custom_rule_entity.direct_conn_priority).await;
        }
        for custom_rule_entity in &config.custom_rule {
            self.tunnel_context.set_domain_rule_obj(&custom_rule_entity.domain,
                                                    custom_rule_entity.matching,
                                                    custom_rule_entity.proxy_type,
                                                    custom_rule_entity.direct_conn_priority).await;
        }

        access_control_service.set_iptables(&config)?;

        if let Some(node) = config.subscribe_content_entity.node.iter().find(|item| item.name == config.select_node_name) {
            self.reselect(node, config.native_udp).await;
        }

        Ok(())
    }

    pub async fn connect_tunnel(&self, node: &NodeInfoEntity, native_udp: bool) {
        *self.enabled.write().await = true;
        self.tunnel_context.set_native_udp(native_udp && node.native_udp.unwrap_or(0) == 1).await;
        self.tunnel_context.connect_tunnel(node.host.clone(), node.port, node.password.clone()).await;
    }

    pub async fn reselect(&self, node: &NodeInfoEntity, native_udp: bool) {
        if !*self.enabled.write().await {
            return;
        }
        self.tunnel_context.set_native_udp(native_udp && node.native_udp.unwrap_or(0) == 1).await;
        self.tunnel_context.connect_tunnel(node.host.clone(), node.port, node.password.clone()).await;
    }

    pub async fn close_tunnel(&self) {
        *self.enabled.write().await = false;
        self.tunnel_context.close_tunnel().await;
    }
}

impl TunnelService {
    pub async fn new(tunnel_context: Arc<TunnelContext>) -> Self {
        let tunnel_service = TunnelService {
            status: Arc::new(RwLock::new(0)),
            download: Arc::new(RwLock::new(0)),
            upload: Arc::new(RwLock::new(0)),
            enabled: Arc::new(Default::default()),
            tunnel_context,
            get_status_job: RwLock::new(None),
        };
        tunnel_service.read_stats().await;
        tunnel_service
    }

    pub async fn read_stats(&self) {
        let status = self.status.clone();
        let download = self.download.clone();
        let upload = self.upload.clone();
        let tunnel_context = self.tunnel_context.clone();

        let _ = self.get_status_job.write().await.insert(spawn(async move {
            loop {
                sleep(Duration::from_secs(1)).await;

                *status.write().await = tunnel_context.get_tunnel_status().await;
                *download.write().await = tunnel_context.get_tunnel_download().await;
                *upload.write().await = tunnel_context.get_tunnel_upload().await;
            }
        }));
    }
}