use crate::service::access_control_service::AccessControl;
use flyshadow_common::interface::interface_selector::{IfaceSelectType, InterfaceSelector};
use ipnetwork::IpNetwork;
use log::{info, warn};
use std::fs::File;
use std::io::Write;
use std::net::{IpAddr, Ipv4Addr};
use std::process::Command;
use std::sync::Arc;
use std::time::Duration;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tokio::time::sleep;
use tunnel::context::context::TunnelContext;
use tunnel::tun::tun::Tun;

pub struct TunService {
    tun_enable: RwLock<bool>,
    tun: Arc<Tun>,
    write_job: RwLock<Option<JoinHandle<()>>>,
    read_job: RwLock<Option<JoinHandle<()>>>,
}

impl TunService {
    pub fn new(tun: Arc<Tun>) -> TunService {
        TunService {
            tun_enable: RwLock::new(false),
            tun,
            write_job: RwLock::new(None),
            read_job: RwLock::new(None),
        }
    }
}

impl TunService {
    pub async fn start_tun(&self, access_control: &Vec<AccessControl>) -> Result<(), String> {
        if *self.tun_enable.read().await {
            return Ok(());
        }

        let _ = Command::new("modprobe").arg("proxy").output();

        let tun = self.tun.clone();
        let tun_dev = tokio_tun::Tun::builder()
            .name("proxy-flyshadow") // if name is empty, then it is set by kernel.
            .tap(false) // false (default): TUN, true: TAP.
            .packet_info(false) // false: IFF_NO_PI, default is true.
            .address("***********".parse().unwrap())
            .netmask("*************".parse().unwrap())
            .up() // or set it up manually using `sudo ip link set <proxy-name> up`.
            .try_build()
            .map_err(|e| format!("create proxy dev error:{}", e))?; // or `.try_build_mq(queues)` for multi-queue support.

        info!("proxy created, name: {}", tun_dev.name());
        let (mut reader, mut writer) = tokio::io::split(tun_dev);

        // Writer: simply clone Arced Tun.
        let tun_clone = tun.clone();
        let read_job = tokio::spawn(async move {
            loop {
                let data = tun_clone.get_tun_data().await;
                let _ = writer.write_all(&data).await;
            }
        });

        let tun_clone = tun.clone();
        let write_job = tokio::spawn(async move {
            // Reader
            let mut buf = [0u8; 65535];
            loop {
                if let Ok(n) = reader.read(&mut buf).await {
                    tun_clone.handler_tun_data(buf[..n].to_vec()).await;
                }
            }
        });
        info!("proxy start successful");

        let _ = self.read_job.write().await.insert(read_job);
        let _ = self.write_job.write().await.insert(write_job);
        *self.tun_enable.write().await = true;

        sleep(Duration::from_millis(500)).await;

        self.create_route_script(access_control).await?;

        let result = Command::new("sh")
            .arg("/etc/flyshadow/add_tun_route.sh")
            .output();
        if let Ok(r) = result {
            let error_str = String::from_utf8_lossy(&r.stderr);
            if error_str != "" {
                warn!("Create route error :{}",error_str)
            }
        }

        Ok(())
    }

    async fn create_route_script(&self, access_control: &Vec<AccessControl>) -> Result<(), String> {
        let ip = InterfaceSelector::select_iface(IfaceSelectType::IP, false).await
            .map_or(None, |s| s.ipv4);
        let device = InterfaceSelector::select_iface(IfaceSelectType::DEVICE, false).await
            .map_or(None, |s| s.ipv4_device);


        let mut add_tun_route_command = vec![
            "route add -net 0.0.0.0 netmask 0.0.0.0 gw *********** dev proxy-flyshadow || true".to_string(),
        ];
        let mut del_tun_route_command = vec![
            "route del -net 0.0.0.0 netmask 0.0.0.0 gw *********** dev proxy-flyshadow || true".to_string(),
        ];

        if ip.is_some() && device.is_some() {
            let addr = ip.unwrap();
            let string = device.unwrap();
            add_tun_route_command.push(format!("route add -net 0.0.0.0 netmask ********* gw {} dev {} || true", addr, string));
            add_tun_route_command.push(format!("route add -net ********* netmask ********* gw {} dev {} || true", addr, string));
            add_tun_route_command.push(format!("route add -net 10.0.0.0 netmask ********* gw {} dev {} || true", addr, string));
            add_tun_route_command.push(format!("route add -net ********** netmask *********** gw {} dev {} || true", addr, string));
            add_tun_route_command.push(format!("route add -net *********** netmask *********** gw {} dev {} || true", addr, string));
            add_tun_route_command.push(format!("route add -net *********** netmask *********** gw {} dev {} || true", addr, string));
            add_tun_route_command.push(format!("route add -net ********* netmask 240.0.0.0 gw {} dev {} || true", addr, string));
            add_tun_route_command.push(format!("route add -net 240.0.0.0 netmask 240.0.0.0 gw {} dev {} || true", addr, string));


            del_tun_route_command.push(format!("route del -net 0.0.0.0 netmask ********* gw {} dev {} || true", addr, string));
            del_tun_route_command.push(format!("route del -net ********* netmask ********* gw {} dev {} || true", addr, string));
            del_tun_route_command.push(format!("route del -net 10.0.0.0 netmask ********* gw {} dev {} || true", addr, string));
            del_tun_route_command.push(format!("route del -net ********** netmask *********** gw {} dev {} || true", addr, string));
            del_tun_route_command.push(format!("route del -net *********** netmask *********** gw {} dev {} || true", addr, string));
            del_tun_route_command.push(format!("route del -net *********** netmask *********** gw {} dev {} || true", addr, string));
            del_tun_route_command.push(format!("route del -net ********* netmask 240.0.0.0 gw {} dev {} || true", addr, string));
            del_tun_route_command.push(format!("route del -net 240.0.0.0 netmask 240.0.0.0 gw {} dev {} || true", addr, string));

            let local_ip = access_control
                .iter()
                .filter_map(|access_control| {
                    if access_control.ip == "" || access_control.proxy_type != 0 {
                        return None;
                    }
                    if let Ok(network) = access_control.ip.parse::<IpNetwork>() {
                        return Some((
                            network.network().to_string(),
                            network.mask().to_string(),
                        ));
                    }
                    return None;
                })
                .collect::<Vec<(String, String)>>();

            for x in &local_ip {
                add_tun_route_command.push(format!("route add -net {} netmask {} gw {} dev {} || true", x.0, x.1, addr, string));
                del_tun_route_command.push(format!("route del -net {} netmask {} gw {} dev {} || true", x.0, x.1, addr, string));
            }
        }

        let mut add_tun_route_file = File::create("/etc/flyshadow/add_tun_route.sh")
            .map_err(|e| format!("create set tun route script file error:{}", e))?;
        for line in add_tun_route_command {
            writeln!(add_tun_route_file, "{}", line)
                .map_err(|e| format!("write add tun route command error:{}", e))?;
        }
        let mut del_tun_route_file = File::create("/etc/flyshadow/del_tun_route.sh")
            .map_err(|e| format!("create del tun route script file error:{}", e))?;
        for line in del_tun_route_command {
            writeln!(del_tun_route_file, "{}", line)
                .map_err(|e| format!("write del tun route command error:{}", e))?;
        }

        Ok(())
    }

    pub async fn stop_tun(&self) {
        if let Some(job) = self.read_job.write().await.take() {
            job.abort()
        }
        if let Some(job) = self.write_job.write().await.take() {
            job.abort()
        }
        *self.tun_enable.write().await = false;

        let result = Command::new("sh")
            .arg("/etc/flyshadow/del_tun_route.sh")
            .output();

        if let Ok(r) = result {
            let error_str = String::from_utf8_lossy(&r.stderr);
            if error_str != "" {
                warn!("del route error :{}",error_str)
            }
        }
    }
}
