use crate::service::access_control_service::AccessControl;
use flyshadow_common::interface::interface_selector::InterfaceSelector;
use log::{error, info, warn};
use std::fs::File;
use std::process::Command;

pub struct CommandUtil {}
impl CommandUtil {
    pub fn set_sys_conf(interface_selector: &InterfaceSelector) -> Result<(), String> {
        let route_localnet_cmd = format!("sysctl -w net.ipv4.conf.{}.route_localnet=1", interface_selector.ipv4_device.clone().unwrap_or("all".to_string()));
        let rp_filter_cmd = format!("sysctl -w net.ipv4.conf.{}.rp_filter=0", interface_selector.ipv4_device.clone().unwrap_or("all".to_string()));

        let set_iptables_command = vec![
            "sysctl -w net.ipv6.ip_forward=1 || true",
            "sysctl -w net.ipv6.conf.all.route_localnet=1 || true",
            "sysctl -w net.ipv6.conf.all.rp_filter=0 || true",
            "sysctl -w net.ipv4.ip_forward=1",
            "sysctl -w net.ipv4.conf.all.route_localnet=1",
            "sysctl -w net.ipv4.conf.all.rp_filter=0",
            route_localnet_cmd.as_str(),
            rp_filter_cmd.as_str(),
        ];
        use std::io::Write;
        let mut set_iptables_file = File::create("/etc/flyshadow/set_sys_conf.sh")
            .map_err(|e| format!("create set set_sys_conf script error:{}", e))?;
        for line in set_iptables_command {
            writeln!(set_iptables_file, "{}", line)
                .map_err(|e| format!("write set sys conf command error:{}", e))?;
        }
        let output = Command::new("sh")
            .arg("/etc/flyshadow/set_sys_conf.sh")
            .output()
            .map_err(|e| format!("Execute set sys conf command error:{}", e))?;
        info!("Execute set sys conf command output: {}",String::from_utf8_lossy(&output.stderr));
        Ok(())
    }

    pub fn set_tproxy_iptables(tproxy_port: u16, access_control: &Vec<AccessControl>) -> Result<(), String> {
        let c_dns_tcp = format!("iptables -t mangle -A flyshadow -p tcp --dport 53 -j TPROXY --on-port {} --tproxy-mark 34", tproxy_port);
        let c_dns_udp = format!("iptables -t mangle -A flyshadow -p udp --dport 53 -j TPROXY --on-port {} --tproxy-mark 34", tproxy_port);

        let control_rule: Vec<String> = access_control.iter()
            .filter(|control| control.mode == 1 && (control.ip != "" || control.mac != "" || control.port != "") && (control.proxy_type == 0 || control.proxy_type == 2))
            .map(|control| if control.proxy_type == 0 {
                let ip = if control.ip == "" { "".to_string() } else { format!("-s {}", control.ip) };
                let mac = if control.mac == "" { "".to_string() } else { format!("-m mac --mac-source {}", control.mac) };
                let port = if control.port == "" { "".to_string() } else { format!("-p tcp --dport {}", control.port) };
                format!("iptables -t mangle -A flyshadow {} {} {} -j RETURN", ip, mac, port)
            } else {
                let ip = if control.ip == "" { "".to_string() } else { format!("-s {}", control.ip) };
                let mac = if control.mac == "" { "".to_string() } else { format!("-m mac --mac-source {}", control.mac) };
                let port = if control.port == "" { "".to_string() } else { format!("--dport {}", control.port) };
                format!("iptables -t mangle -A flyshadow -p tcp {} {} {} -j TPROXY --on-port {} --tproxy-mark 34\niptables -t mangle -A flyshadow -p udp {} {} {} -j TPROXY --on-port {} --tproxy-mark 34",
                        ip, mac, port, tproxy_port, ip, mac, port, tproxy_port)
            })
            .collect();

        let default_rule = access_control
            .iter()
            .find(|control| control.mode == 0 && (control.proxy_type == 0 || control.proxy_type == 2))
            .map(|control| if control.proxy_type == 0 {
                "".to_string()
            } else {
                format!("iptables -t mangle -A flyshadow -p tcp -j TPROXY --on-port {} --tproxy-mark 34\niptables -t mangle -A flyshadow -p udp -j TPROXY --on-port {} --tproxy-mark 34", tproxy_port, tproxy_port)
            })
            .unwrap_or(format!("iptables -t mangle -A flyshadow -p tcp -j TPROXY --on-port {} --tproxy-mark 34\niptables -t mangle -A flyshadow -p udp -j TPROXY --on-port {} --tproxy-mark 34", tproxy_port, tproxy_port));

        let mut set_iptables_command = vec![
            "iptables -t mangle -N flyshadow",
            "iptables -t mangle -I PREROUTING -j flyshadow",
            c_dns_tcp.as_str(),
            c_dns_udp.as_str(),
        ];

        if !control_rule.is_empty() {
            set_iptables_command.append(&mut control_rule.iter().map(|rule| rule.as_str()).collect());
        }

        set_iptables_command.append(&mut vec![
            "iptables -t mangle -A flyshadow -d 0.0.0.0/8 -j RETURN",
            "iptables -t mangle -A flyshadow -d *********/8 -j RETURN",
            "iptables -t mangle -A flyshadow -d 10.0.0.0/8 -j RETURN",
            "iptables -t mangle -A flyshadow -d **********/12 -j RETURN",
            "iptables -t mangle -A flyshadow -d ***********/16 -j RETURN",
            "iptables -t mangle -A flyshadow -d ***********/16 -j RETURN",
            "iptables -t mangle -A flyshadow -d *********/4 -j RETURN",
            "iptables -t mangle -A flyshadow -d 240.0.0.0/4 -j RETURN",
        ]);

        set_iptables_command.push(default_rule.as_str());

        set_iptables_command.append(&mut vec![
            "ip rule add fwmark 34 lookup 34",
            "ip route add local 0.0.0.0/0 dev lo table 34"
        ]);

        use std::io::Write;
        let mut set_iptables_file = File::create("/etc/flyshadow/set_tproxy_iptables.sh")
            .map_err(|e| format!("create set iptables script error:{}", e))?;
        for line in set_iptables_command {
            writeln!(set_iptables_file, "{}", line)
                .map_err(|e| format!("write set iptables command error:{}", e))?;
        }
        let output = Command::new("sh")
            .arg("/etc/flyshadow/set_tproxy_iptables.sh")
            .output()
            .map_err(|e| format!("Execute set iptables command error:{}", e))?;
        info!("Execute set iptables command output: {}",String::from_utf8_lossy(&output.stdout));
        let error_str = String::from_utf8_lossy(&output.stderr);
        if error_str != "" {
            error!("Execute set iptables command error output: {}",error_str);
            Err(format!("Execute set iptables command error output: {}", error_str))
        } else {
            Ok(())
        }
    }


    pub fn set_redirect_iptables(tproxy_port: u16, access_control: &Vec<AccessControl>) -> Result<(), String> {
        let c_dns_udp = format!("iptables -t nat -A flyshadow -p udp --dport 53 -j REDIRECT --to-port {}", tproxy_port);
        let c_dns_tcp = format!("iptables -t nat -A flyshadow -p tcp --dport 53 -j REDIRECT --to-port {}", tproxy_port);

        let control_rule: Vec<String> = access_control.iter()
            .filter(|control| control.mode == 1 && (control.ip != "" || control.mac != "" || control.port != "") && (control.proxy_type == 0 || control.proxy_type == 2))
            .map(|control| if control.proxy_type == 0 {
                let ip = if control.ip == "" { "".to_string() } else { format!("-s {}", control.ip) };
                let mac = if control.mac == "" { "".to_string() } else { format!("-m mac --mac-source {}", control.mac) };
                let port = if control.port == "" { "".to_string() } else { format!("-p tcp --dport {}", control.port) };
                format!("iptables -t nat -A flyshadow {} {} {} -j RETURN", ip, mac, port)
            } else {
                let ip = if control.ip == "" { "".to_string() } else { format!("-s {}", control.ip) };
                let mac = if control.mac == "" { "".to_string() } else { format!("-m mac --mac-source {}", control.mac) };
                let port = if control.port == "" { "".to_string() } else { format!("--dport {}", control.port) };
                format!("iptables -t nat -A flyshadow -p tcp {} {} {} -j REDIRECT --to-port {}", ip, mac, port, tproxy_port)
            })
            .collect();

        let default_rule = access_control
            .iter()
            .find(|control| control.mode == 0 && (control.proxy_type == 0 || control.proxy_type == 2))
            .map(|control| if control.proxy_type == 0 {
                "".to_string()
            } else {
                format!("iptables -t nat -A flyshadow -p tcp -j REDIRECT --to-port {}", tproxy_port)
            })
            .unwrap_or(format!("iptables -t nat -A flyshadow -p tcp -j REDIRECT --to-port {}", tproxy_port));

        let mut set_iptables_command = vec![
            "iptables -t nat -N flyshadow",
            "iptables -t nat -I PREROUTING -j flyshadow",
            c_dns_udp.as_str(),
            c_dns_tcp.as_str(),
        ];

        if !control_rule.is_empty() {
            set_iptables_command.append(&mut control_rule.iter().map(|rule| rule.as_str()).collect());
        }

        set_iptables_command.append(&mut vec![
            "iptables -t nat -A flyshadow -d 0.0.0.0/8 -j RETURN",
            "iptables -t nat -A flyshadow -d *********/8 -j RETURN",
            "iptables -t nat -A flyshadow -d 10.0.0.0/8 -j RETURN",
            "iptables -t nat -A flyshadow -d **********/12 -j RETURN",
            "iptables -t nat -A flyshadow -d ***********/16 -j RETURN",
            "iptables -t nat -A flyshadow -d ***********/16 -j RETURN",
            "iptables -t nat -A flyshadow -d *********/4 -j RETURN",
            "iptables -t nat -A flyshadow -d 240.0.0.0/4 -j RETURN",
        ]);

        set_iptables_command.push(default_rule.as_str());

        use std::io::Write;
        let mut set_iptables_file = File::create("/etc/flyshadow/set_redirect_iptables.sh")
            .map_err(|e| format!("create set iptables script error:{}", e))?;
        for line in set_iptables_command {
            writeln!(set_iptables_file, "{}", line)
                .map_err(|e| format!("write set iptables command error:{}", e))?;
        }
        let output = Command::new("sh")
            .arg("/etc/flyshadow/set_redirect_iptables.sh")
            .output()
            .map_err(|e| format!("Execute set iptables command error:{}", e))?;
        info!("Execute set iptables command output: {}",String::from_utf8_lossy(&output.stdout));
        let error_str = String::from_utf8_lossy(&output.stderr);
        if error_str != "" {
            error!("Execute set iptables command error output: {}",error_str);
            Err(format!("Execute set iptables command error output: {}", error_str))
        } else {
            Ok(())
        }
    }

    pub fn clean_iptables() -> Result<(), String> {
        let clean_iptables_command = vec![
            "iptables -t mangle -F flyshadow || true",
            "iptables -t mangle -D PREROUTING -j flyshadow || true",
            "iptables -t mangle -X flyshadow || true",
            "iptables -t nat -F flyshadow || true",
            "iptables -t nat -D PREROUTING -j flyshadow || true",
            "iptables -t nat -X flyshadow || true",
            "ip rule del fwmark 34 lookup 34 || true",
            "ip route del local 0.0.0.0/0 dev lo table 34 || true",
        ];
        use std::io::Write;
        let mut clean_iptables_file = File::create("/etc/flyshadow/clean_iptables.sh")
            .map_err(|e| format!("create clean iptables script error:{}", e))?;
        for line in clean_iptables_command {
            writeln!(clean_iptables_file, "{}", line)
                .map_err(|e| format!("write clean iptables command error:{}", e))?;
        }
        let output = Command::new("sh")
            .arg("/etc/flyshadow/clean_iptables.sh")
            .output()
            .map_err(|e| format!("Execute clean iptables command error:{}", e))?;
        info!("Execute clean iptables command output: {}",String::from_utf8_lossy(&output.stdout));
        let error_str = String::from_utf8_lossy(&output.stderr);
        if error_str != "" {
            warn!("Execute clean iptables command error output: {}",error_str);
            Err(format!("Execute clean iptables command error output: {}", error_str))
        } else {
            Ok(())
        }
    }
}