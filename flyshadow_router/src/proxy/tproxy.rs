use flyshadow_common::tunnel::tunnel_package::PackageProtocol;
use libc::{iovec, msghdr, recvmsg, setsockopt, sockaddr_in, sockaddr_in6, socklen_t, CMSG_DATA, IP6T_SO_ORIGINAL_DST, IPPROTO_IP, IPPROTO_IPV6, IPV6_RECVORIGDSTADDR, IPV6_TRANSPARENT, IP_RECVORIGDSTADDR, IP_TRANSPARENT, SOL_IP, SOL_IPV6, SO_ORIGINAL_DST};
use log::{debug, error, info};
use std::ffi::c_void;
use std::io::ErrorKind;
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr, SocketAddrV4, SocketAddrV6, UdpSocket};
use std::os::fd::{AsRawFd, RawFd};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use std::{io, mem};
use tokio::io::unix::AsyncFd;
use tokio::net::{TcpListener, TcpStream};
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tunnel::context::context::TunnelContext;
use tunnel::context::proxy_type::ProxyType;
use tunnel::mapper::tunnel_mapper_info::TunnelMapperInfo;
use tunnel::util::process_util::get_process_name_by_port;

pub struct Tproxy {
    tunnel_context: Arc<TunnelContext>,
    listener_join_handler: RwLock<Vec<JoinHandle<()>>>,
}

impl Tproxy {
    pub fn new(tunnel_context: Arc<TunnelContext>) -> Tproxy {
        Tproxy {
            tunnel_context,
            listener_join_handler: RwLock::new(Vec::new()),
        }
    }

    pub async fn start(&self, port: u16, tproxy_mode_type: u16) -> Result<(), std::io::Error> {
        let tcp_listener = TcpListener::bind(format!("0.0.0.0:{}", port)).await?;

        unsafe {
            // 设置透明代理模式
            let optval: libc::c_int = 1;
            setsockopt(
                tcp_listener.as_raw_fd(),
                IPPROTO_IP,
                IP_TRANSPARENT,
                &optval as *const _ as *const _,
                mem::size_of_val(&optval) as u32);
        }

        info!("TProxy Listening on: {}", tcp_listener.local_addr()?);
        let tunnel_context_clone = self.tunnel_context.clone();

        let job_handler = spawn(async move {
            loop {
                let tunnel_context_clone = tunnel_context_clone.clone();
                match tcp_listener.accept().await {
                    Ok((tcp_stream, socket_addr)) => {
                        let target_addr = Self::get_original_dst(&tcp_stream).await.unwrap();
                        debug!("Accepted TCP connection from {}", socket_addr);
                        // 如果是DNS加密地址
                        let block_dns_list = vec!["*******:853", "*******:443", "*******:853", "*******:443", "*******:853", "*******:443",
                                                  "***************:853", "***************:443", "*********:443", "*********:853", "*********:443", "*********:853"];
                        // 如果是DNS加密地址
                        if block_dns_list.contains(&target_addr.to_string().as_str()) {
                            continue;
                        }
                        spawn(async move {
                            debug!("Tcp receive client {}--->{}",socket_addr,target_addr);
                            let tunnel_mapper_info = TunnelMapperInfo {
                                protocol: PackageProtocol::TCP,
                                source_addr: socket_addr.ip().to_string(),
                                source_port: socket_addr.port(),
                                target_addr: target_addr.ip().to_string(),
                                target_port: target_addr.port(),
                                fake_target_addr: "".to_string(),
                                fake_target_port: 0,
                                process_name: get_process_name_by_port(socket_addr.port(), PackageProtocol::TCP),
                                matcher_name: "".to_string(),
                                matcher_rule: "".to_string(),
                                proxy_type: ProxyType::Proxy,
                                active_time: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis(),
                                temp_data: vec![],
                                direct_conn_priority: true,
                                traffic_info: Arc::new(Default::default()),
                            };
                            if let Err(e) = tunnel_context_clone.tun_connect_server(tunnel_mapper_info, tcp_stream).await {
                                error!("connect disconnect error: {}",e);
                                return;
                            }
                        });
                    }
                    Err(e) => {
                        error!("accept err {}", e);
                    }
                }
            }
        });
        self.listener_join_handler.write().await.push(job_handler);

        // ------------------- UDP -----------------

        let udp_socket = UdpSocket::bind(format!("0.0.0.0:{}", port))?;

        let tunnel_context_clone = self.tunnel_context.clone();
        unsafe {
            let optval: libc::c_int = 1;
            setsockopt(
                udp_socket.as_raw_fd(),
                IPPROTO_IP,
                IP_TRANSPARENT,
                &optval as *const _ as *const _,
                mem::size_of_val(&optval) as u32);
            setsockopt(
                udp_socket.as_raw_fd(),
                IPPROTO_IP,
                IP_RECVORIGDSTADDR,
                &optval as *const _ as *const _,
                mem::size_of_val(&optval) as u32);
        }

        let job_handler = spawn(async move {
            let udp_socket = Arc::new(udp_socket);
            loop {
                match Tproxy::recv_with_destination(udp_socket.as_raw_fd()).await {
                    Ok((src, dst, data)) => {
                        debug!("receive udp dst: {}",dst);

                        let tunnel_mapper_info = TunnelMapperInfo {
                            protocol: PackageProtocol::UDP,
                            source_addr: src.ip().to_string(),
                            source_port: src.port(),
                            target_addr: dst.ip().to_string(),
                            target_port: dst.port(),
                            fake_target_addr: "".to_string(),
                            fake_target_port: 0,
                            process_name: get_process_name_by_port(src.port(), PackageProtocol::UDP),
                            matcher_name: "".to_string(),
                            matcher_rule: "".to_string(),
                            proxy_type: ProxyType::Proxy,
                            active_time: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis(),
                            temp_data: vec![],
                            direct_conn_priority: true,
                            traffic_info: Arc::new(Default::default()),
                        };
                        tunnel_context_clone.tproxy_udp_write_to_server(tunnel_mapper_info, data, udp_socket.clone(), tproxy_mode_type).await;
                    }
                    Err(e) => {
                        error!("recv udp err {}",e);
                    }
                }
            }
        });
        self.listener_join_handler.write().await.push(job_handler);

        let _ = self.start_ipv6(port, tproxy_mode_type).await;

        Ok(())
    }

    pub async fn start_ipv6(&self, port: u16, tproxy_mode_type: u16) -> Result<(), std::io::Error> {
        let tcp_listener = TcpListener::bind(SocketAddr::new(IpAddr::from(Ipv6Addr::LOCALHOST), port)).await?;

        unsafe {
            // 设置透明代理模式
            let optval: libc::c_int = 1;
            setsockopt(
                tcp_listener.as_raw_fd(),
                IPPROTO_IPV6,
                IPV6_TRANSPARENT,
                &optval as *const _ as *const _,
                mem::size_of_val(&optval) as u32);
        }

        info!("TProxy Listening ipv6 on: {}", tcp_listener.local_addr()?);
        let tunnel_context_clone = self.tunnel_context.clone();

        let job_handler = spawn(async move {
            loop {
                let tunnel_context_clone = tunnel_context_clone.clone();
                match tcp_listener.accept().await {
                    Ok((tcp_stream, socket_addr)) => {
                        let target_addr = Self::get_original_dst(&tcp_stream).await.unwrap();
                        debug!("Accepted TCP connection from {}", socket_addr);
                        // 如果是DNS加密地址
                        let block_dns_list = vec!["*******:853", "*******:443", "*******:853", "*******:443", "*******:853", "*******:443",
                                                  "***************:853", "***************:443", "*********:443", "*********:853", "*********:443", "*********:853"];
                        // 如果是DNS加密地址
                        if block_dns_list.contains(&target_addr.to_string().as_str()) {
                            continue;
                        }
                        spawn(async move {
                            debug!("Tcp receive client {}--->{}",socket_addr,target_addr);
                            let tunnel_mapper_info = TunnelMapperInfo {
                                protocol: PackageProtocol::TCP,
                                source_addr: socket_addr.ip().to_string(),
                                source_port: socket_addr.port(),
                                target_addr: target_addr.ip().to_string(),
                                target_port: target_addr.port(),
                                fake_target_addr: "".to_string(),
                                fake_target_port: 0,
                                process_name: get_process_name_by_port(socket_addr.port(), PackageProtocol::TCP),
                                matcher_name: "".to_string(),
                                matcher_rule: "".to_string(),
                                proxy_type: ProxyType::Proxy,
                                active_time: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis(),
                                temp_data: vec![],
                                direct_conn_priority: true,
                                traffic_info: Arc::new(Default::default()),
                            };
                            if let Err(e) = tunnel_context_clone.tun_connect_server(tunnel_mapper_info, tcp_stream).await {
                                error!("connect disconnect error: {}",e);
                                return;
                            }
                        });
                    }
                    Err(e) => {
                        error!("accept err {}", e);
                    }
                }
            }
        });
        self.listener_join_handler.write().await.push(job_handler);

        // ------------------- UDP -----------------

        let udp_socket = UdpSocket::bind(SocketAddr::new(IpAddr::from(Ipv6Addr::LOCALHOST), port))?;

        let tunnel_context_clone = self.tunnel_context.clone();
        unsafe {
            let optval: libc::c_int = 1;
            setsockopt(
                udp_socket.as_raw_fd(),
                IPPROTO_IPV6,
                IPV6_TRANSPARENT,
                &optval as *const _ as *const _,
                mem::size_of_val(&optval) as u32);
            setsockopt(
                udp_socket.as_raw_fd(),
                IPPROTO_IPV6,
                IPV6_RECVORIGDSTADDR,
                &optval as *const _ as *const _,
                mem::size_of_val(&optval) as u32);
        }

        let job_handler = spawn(async move {
            let udp_socket = Arc::new(udp_socket);
            loop {
                match Tproxy::recv_ipv6_with_destination(udp_socket.as_raw_fd()).await {
                    Ok((src, dst, data)) => {
                        debug!("receive udp dst: {}",dst);

                        let tunnel_mapper_info = TunnelMapperInfo {
                            protocol: PackageProtocol::UDP,
                            source_addr: src.ip().to_string(),
                            source_port: src.port(),
                            target_addr: dst.ip().to_string(),
                            target_port: dst.port(),
                            fake_target_addr: "".to_string(),
                            fake_target_port: 0,
                            process_name: get_process_name_by_port(src.port(), PackageProtocol::UDP),
                            matcher_name: "".to_string(),
                            matcher_rule: "".to_string(),
                            proxy_type: ProxyType::Proxy,
                            active_time: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_millis(),
                            temp_data: vec![],
                            direct_conn_priority: true,
                            traffic_info: Arc::new(Default::default()),
                        };
                        tunnel_context_clone.tproxy_udp_write_to_server(tunnel_mapper_info, data, udp_socket.clone(), tproxy_mode_type).await;
                    }
                    Err(e) => {
                        error!("recv udp err {}",e);
                    }
                }
            }
        });
        self.listener_join_handler.write().await.push(job_handler);

        Ok(())
    }

    pub async fn shutdown(&self) {
        self.listener_join_handler.write().await.iter().for_each(|h| h.abort());
    }


    async fn get_original_dst(stream: &TcpStream) -> io::Result<SocketAddr> {
        let fd = stream.as_raw_fd();
        let mut sockaddr: sockaddr_in = unsafe { mem::zeroed() };
        let mut socklen = mem::size_of::<sockaddr_in>() as socklen_t;
        let mut sockaddr_v6: sockaddr_in6 = unsafe { mem::zeroed() };
        let mut socklen_v6 = mem::size_of::<sockaddr_in6>() as socklen_t;

        let mut ipv6 = true;
        let ret = unsafe {
            let ret = libc::getsockopt(
                fd,
                SOL_IPV6,
                IP6T_SO_ORIGINAL_DST,
                &mut sockaddr_v6 as *mut _ as *mut _,
                &mut socklen_v6 as *mut _);
            if ret == 0 {
                ret
            } else {
                ipv6 = false;
                libc::getsockopt(
                    fd,
                    SOL_IP,
                    SO_ORIGINAL_DST,
                    &mut sockaddr as *mut _ as *mut _,
                    &mut socklen as *mut _)
            }
        };

        if ret != 0 {
            return Err(io::Error::last_os_error());
        }

        let ip = if ipv6 {
            IpAddr::from(Ipv6Addr::from(sockaddr_v6.sin6_addr.s6_addr))
        } else {
            let ip4_int = u32::from_be(sockaddr.sin_addr.s_addr);
            let ip = Ipv4Addr::from([(ip4_int >> 24) as u8, (ip4_int >> 16) as u8, (ip4_int >> 8) as u8, ip4_int as u8]);
            IpAddr::from(ip)
        };

        let port = u16::from_be(sockaddr.sin_port);

        Ok(SocketAddr::new(IpAddr::from(ip), port))
    }

    async fn recv_with_destination(raw_fd: RawFd) -> io::Result<(SocketAddrV4, SocketAddrV4, Vec<u8>)> {
        let async_fd = AsyncFd::new(raw_fd)?;

        loop {
            let mut gurad = async_fd.readable().await?;

            // 缓冲区和控制消息的准备
            let mut buf = [0u8; 1024];
            let mut cmsg_buf = [0u8; 64];

            // 准备IO向量，用于recvmsg传递数据
            let iov = iovec {
                iov_base: buf.as_mut_ptr() as *mut c_void,
                iov_len: buf.len(),
            };

            // 初始化msghdr结构，用于recvmsg
            let mut msg: msghdr = unsafe { mem::zeroed() };
            msg.msg_iov = &iov as *const iovec as *mut iovec;
            msg.msg_iovlen = 1;

            // 用于接收发送方的地址
            let mut src_addr: sockaddr_in = unsafe { mem::zeroed() };
            msg.msg_name = &mut src_addr as *mut sockaddr_in as *mut c_void;
            msg.msg_namelen = mem::size_of_val(&src_addr) as socklen_t;

            // 控制消息的缓冲区
            msg.msg_control = cmsg_buf.as_mut_ptr() as *mut c_void;
            #[cfg(target_env = "musl")]
            {
                msg.msg_controllen = cmsg_buf.len() as socklen_t;
            }
            #[cfg(target_env = "gnu")]
            {
                use libc::size_t;
                msg.msg_controllen = cmsg_buf.len() as size_t;
            }

            // Blocking recvmsg
            let received_bytes = unsafe {
                recvmsg(
                    raw_fd,
                    &mut msg as *mut msghdr,
                    0,
                )
            };

            if received_bytes < 0 {
                if io::Error::last_os_error().raw_os_error() == Some(libc::EAGAIN) {
                    gurad.clear_ready();
                    continue;
                }
                // 处理错误
                return Err(io::Error::last_os_error());
            }

            // 获取源地址和端口
            let (src_ip, src_port) = unsafe {
                let sockaddr = &*(msg.msg_name as *const sockaddr_in);
                let dest_ip = sockaddr.sin_addr.s_addr;
                let dest_port = sockaddr.sin_port;

                (std::net::Ipv4Addr::from(u32::from_be(dest_ip)), u16::from_be(dest_port))
            };

            // 解析控制消息中的目的IP地址
            let (dst_ip, dst_port) = unsafe {
                let mut cmsg = libc::CMSG_FIRSTHDR(&msg);
                let mut dst_ip = None;
                let mut dst_port = None;
                while !cmsg.is_null() {
                    if (*cmsg).cmsg_level == SOL_IP && (*cmsg).cmsg_type == IP_RECVORIGDSTADDR {
                        let dst_addr: &mut sockaddr_in = &mut *(CMSG_DATA(cmsg) as *mut sockaddr_in);
                        dst_ip = Some(Ipv4Addr::from(u32::from_be(dst_addr.sin_addr.s_addr)));
                        dst_port = Some(u16::from_be(dst_addr.sin_port));

                        break;
                    }
                    cmsg = libc::CMSG_NXTHDR(&msg, cmsg);
                }
                if dst_ip.is_none() || dst_port.is_none() {
                    return Err(io::Error::new(ErrorKind::Other, "Dst ip or port not found"));
                }
                (dst_ip.unwrap(), dst_port.unwrap())
            };

            return Ok((SocketAddrV4::new(src_ip, src_port), SocketAddrV4::new(dst_ip, dst_port), buf[..received_bytes as usize].to_vec()));
        }
    }

    async fn recv_ipv6_with_destination(raw_fd: RawFd) -> io::Result<(SocketAddrV6, SocketAddrV6, Vec<u8>)> {
        let async_fd = AsyncFd::new(raw_fd)?;

        loop {
            let mut gurad = async_fd.readable().await?;

            // 缓冲区和控制消息的准备
            let mut buf = [0u8; 1024];
            let mut cmsg_buf = [0u8; 64];

            // 准备IO向量，用于recvmsg传递数据
            let iov = iovec {
                iov_base: buf.as_mut_ptr() as *mut c_void,
                iov_len: buf.len(),
            };

            // 初始化msghdr结构，用于recvmsg
            let mut msg: msghdr = unsafe { mem::zeroed() };
            msg.msg_iov = &iov as *const iovec as *mut iovec;
            msg.msg_iovlen = 1;

            // 用于接收发送方的地址
            let mut src_addr: sockaddr_in = unsafe { mem::zeroed() };
            msg.msg_name = &mut src_addr as *mut sockaddr_in as *mut c_void;
            msg.msg_namelen = mem::size_of_val(&src_addr) as socklen_t;

            // 控制消息的缓冲区
            msg.msg_control = cmsg_buf.as_mut_ptr() as *mut c_void;
            #[cfg(target_env = "musl")]
            {
                msg.msg_controllen = cmsg_buf.len() as socklen_t;
            }
            #[cfg(target_env = "gnu")]
            {
                use libc::size_t;
                msg.msg_controllen = cmsg_buf.len() as size_t;
            }

            // Blocking recvmsg
            let received_bytes = unsafe {
                recvmsg(
                    raw_fd,
                    &mut msg as *mut msghdr,
                    0,
                )
            };

            if received_bytes < 0 {
                if io::Error::last_os_error().raw_os_error() == Some(libc::EAGAIN) {
                    gurad.clear_ready();
                    continue;
                }
                // 处理错误
                return Err(io::Error::last_os_error());
            }

            // 获取源地址和端口
            let (src_ip, src_port, _flowinfo, _scope_id) = unsafe {
                let sockaddr = &*(msg.msg_name as *const sockaddr_in6);
                let dest_ip = sockaddr.sin6_addr.s6_addr;
                let dest_port = sockaddr.sin6_port;

                (std::net::Ipv6Addr::from(dest_ip), u16::from_be(dest_port), sockaddr.sin6_flowinfo, sockaddr.sin6_scope_id)
            };

            // 解析控制消息中的目的IP地址
            let (dst_ip, dst_port, flowinfo, scope_id) = unsafe {
                let mut cmsg = libc::CMSG_FIRSTHDR(&msg);
                let mut dst_ip = None;
                let mut dst_port = None;
                let mut flowinfo = None;
                let mut scope_id = None;
                while !cmsg.is_null() {
                    if (*cmsg).cmsg_level == SOL_IP && (*cmsg).cmsg_type == IP_RECVORIGDSTADDR {
                        let dst_addr: &mut sockaddr_in6 = &mut *(CMSG_DATA(cmsg) as *mut sockaddr_in6);
                        dst_ip = Some(Ipv6Addr::from(dst_addr.sin6_addr.s6_addr));
                        dst_port = Some(u16::from_be(dst_addr.sin6_port));
                        flowinfo = Some(dst_addr.sin6_flowinfo);
                        scope_id = Some(dst_addr.sin6_scope_id);

                        break;
                    }
                    cmsg = libc::CMSG_NXTHDR(&msg, cmsg);
                }
                if dst_ip.is_none() || dst_port.is_none() {
                    return Err(io::Error::new(ErrorKind::Other, "Dst ip or port not found"));
                }
                (dst_ip.unwrap(), dst_port.unwrap(), flowinfo.unwrap(), scope_id.unwrap())
            };

            return Ok((SocketAddrV6::new(src_ip, src_port, flowinfo, scope_id), SocketAddrV6::new(dst_ip, dst_port, flowinfo, scope_id), buf[..received_bytes as usize].to_vec()));
        }
    }
}