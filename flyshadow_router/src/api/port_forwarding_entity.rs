use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, Deserialize, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct PortForwardingInfo {
    pub uuid: String,
    pub client_list: Vec<ClientEntity>,
    pub port_forwarding_list: Vec<PortForwardingEntity>,
}

#[derive(Debug, Ser<PERSON><PERSON>, Deserialize, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct PortForwardingEntity {
    pub id: Option<i32>,
    pub uuid: String,
    #[serde(rename = "listenPort")]
    pub listen_port: u16,
    #[serde(rename = "targetAddr")]
    pub target_addr: String,
    #[serde(rename = "targetUuid")]
    pub target_uuid: String,
    pub enable: bool,
    pub error_message: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct ClientEntity {
    pub uuid: String,
    pub remark: Option<String>,
}
