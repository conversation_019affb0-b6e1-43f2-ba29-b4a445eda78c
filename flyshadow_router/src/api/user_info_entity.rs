use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, Serial<PERSON>, Deserialize, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct UserInfoEntity {
    #[serde(rename = "trafficSize")]
    traffic_size: i64,
    #[serde(rename = "trafficUpload")]
    traffic_upload: i64,
    #[serde(rename = "trafficDownload")]
    traffic_download: i64,
    money: f32,
    level: i32,
    #[serde(rename = "expireTime")]
    expire_time: i64,
}
