use crate::api::custom_rule_entity::CustomRuleEntity;
use crate::api::port_forwarding_entity::{ClientEntity, PortForwardingEntity};
use crate::api::subscribe_content_entity::SubscribeContentEntity;
use base64::engine::GeneralPurpose;
use base64::{engine::general_purpose, Engine as _};
use reqwest::Client;
use serde::de::DeserializeOwned;
use serde::Serialize;
use std::fmt::Debug;
use std::time::Duration;

#[derive(Clone)]
pub struct Api {
    base_url: String,
    base64engine: GeneralPurpose,
}

impl Api {
    pub fn new() -> Self {
        Api {
            base_url: "https://main.hkspeedup.com".to_string(),
            base64engine: general_purpose::STANDARD,
        }
    }

    /// 获取订阅内容
    ///
    /// @param password 密码
    /// @return 订阅内容实体或错误信息
    pub async fn get_subscribe_content(&self, password: String) -> Result<SubscribeContentEntity, String> {
        let url = format!("{}/subscribe", self.base_url);
        let response_body = self.make_get_request(&url, None, Some(&[("password", password.as_str())])).await?;
        serde_json::from_str(&response_body).map_err(|e| format!("{},{}", e, response_body))
    }

    /// 获取自定义规则列表
    ///
    /// @param password 密码
    /// @return Vec<CustomRuleEntity> 自定义规则列表
    /// @return String 错误信息
    pub async fn get_custom_rule_list(&self, password: String) -> Result<Vec<CustomRuleEntity>, String> {
        let url = format!("{}/custom/rule/list", self.base_url);
        let response_body = self.make_post_request::<Vec<CustomRuleEntity>>(&url, Some(password), None).await?;
        self.parse_response(&response_body)
    }

    /// 更新自定义规则列表
    ///
    /// @param password 密码
    /// @param custom_rule_entity_list 自定义规则实体列表
    pub async fn update_custom_rule_list(&self, password: String, custom_rule_entity_list: Vec<CustomRuleEntity>) -> Result<(), String> {
        let url = format!("{}/custom/rule/updateList", self.base_url);
        let response_body = self.make_post_request(&url, Some(password), Some(&custom_rule_entity_list)).await?;
        self.check_response_success(&response_body)
    }

    /// 保存自定义规则
    ///
    /// @param password 密码
    /// @param custom_rule_entity 自定义规则实体
    /// @return Result<String, String>
    pub async fn save_custom_rule(&self, password: String, custom_rule_entity: CustomRuleEntity) -> Result<(), String> {
        let url = format!("{}/custom/rule/save", self.base_url);
        let response_body = self.make_post_request(&url, Some(password), Some(&custom_rule_entity)).await?;
        self.check_response_success(&response_body)
    }

    /// 更新自定义规则
    ///
    /// @param password 密码
    /// @param custom_rule_entity 自定义规则实体
    /// @return Result<String, String>
    pub async fn update_custom_rule(&self, password: String, custom_rule_entity: CustomRuleEntity) -> Result<(), String> {
        let url = format!("{}/custom/rule/update", self.base_url);
        let response_body = self.make_post_request(&url, Some(password), Some(&custom_rule_entity)).await?;
        self.check_response_success(&response_body)
    }

    /// 删除自定义规则
    ///
    /// @param password 密码
    /// @param id 自定义规则ID
    pub async fn delete_custom_rule(&self, password: String, id: i64) -> Result<(), String> {
        let url = format!("{}/custom/rule/remove/{}", self.base_url, id);
        let response_body = self.make_post_request::<()>(&url, Some(password), None).await?;
        self.check_response_success(&response_body)
    }

    pub async fn get_port_forwarding_list(&self, password: String) -> Result<Vec<PortForwardingEntity>, String> {
        let url = format!("{}/port/forwarding/list", self.base_url);
        let response_body = self.make_get_request::<()>(&url, Some(password), None).await?;
        self.parse_response(&response_body)
    }

    pub async fn save_or_update_port_forwarding(&self, password: String, port_forwarding_entity: PortForwardingEntity) -> Result<(), String> {
        let url = format!("{}/port/forwarding/saveOrUpdate", self.base_url);
        let response_body = self.make_post_request::<PortForwardingEntity>(&url, Some(password), Some(&port_forwarding_entity)).await?;
        self.parse_response(&response_body)
    }
    pub async fn delete_port_forwarding(&self, password: String, id: i64) -> Result<(), String> {
        let url = format!("{}/port/forwarding/delete/{}", self.base_url, id);
        let response_body = self.make_post_request::<()>(&url, Some(password), None).await?;
        self.parse_response(&response_body)
    }

    pub async fn get_client_list(&self, password: String) -> Result<Vec<ClientEntity>, String> {
        let url = format!("{}/client/list", self.base_url);
        let response_body = self.make_get_request::<()>(&url, Some(password), None).await?;
        self.parse_response(&response_body)
    }

    pub async fn save_or_update_client(&self, password: String, client_entity: ClientEntity) -> Result<(), String> {
        let url = format!("{}/client/saveOrUpdate", self.base_url);
        let response_body = self.make_post_request::<ClientEntity>(&url, Some(password), Some(&client_entity)).await?;
        self.parse_response(&response_body)
    }
}

impl Api {
    // 私有方法，发起 GET 请求
    async fn make_get_request<T>(&self, url: &str, subscribe_password: Option<String>, query: Option<&T>) -> Result<String, String>
    where
        T: Serialize,
    {
        let client = Client::new();
        let mut builder = client
            .get(url)
            .timeout(Duration::from_secs(90))
            .header("subscribe-password", subscribe_password.map(|p| self.base64engine.encode(p.as_bytes())).unwrap_or("".to_string()));
        if let Some(query) = query {
            builder = builder.query(query);
        }
        let response = builder
            .send()
            .await
            .map_err(|e| e.to_string())?;

        response.text().await.map_err(|e| e.to_string())
    }

    // 私有方法，发起 POST 请求
    async fn make_post_request<T>(&self, url: &str, subscribe_password: Option<String>, body: Option<&T>) -> Result<String, String>
    where
        T: Serialize,
    {
        let client = Client::new();
        let mut builder = client
            .post(url)
            .timeout(Duration::from_secs(90))
            .header("subscribe-password", subscribe_password.map(|p| self.base64engine.encode(p.as_bytes())).unwrap_or("".to_string()));
        if let Some(body) = body {
            builder = builder.json(body);
        }
        let response = builder
            .send()
            .await
            .map_err(|e| e.to_string())?;

        response.text().await.map_err(|e| e.to_string())
    }

    // 解析响应
    fn parse_response<T: DeserializeOwned + Debug>(&self, body: &str) -> Result<T, String> {
        let json_object: serde_json::Value = serde_json::from_str(body).map_err(|err| err.to_string())?;
        if json_object["code"] == 200 {
            serde_json::from_value(json_object["data"].clone()).map_err(|e| e.to_string())
        } else {
            Err(json_object.to_string())
        }
    }

    // 检查响应是否成功
    fn check_response_success(&self, body: &str) -> Result<(), String> {
        let json_object: serde_json::Value = serde_json::from_str(body).map_err(|err| err.to_string())?;
        if json_object["code"] == 200 {
            Ok(())
        } else {
            Err(json_object.to_string())
        }
    }
}