use crate::api::custom_rule_entity::CustomRuleEntity;
use crate::api::node_info_entity::NodeInfoEntity;
use crate::api::user_info_entity::UserInfoEntity;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct SubscribeContentEntity {
    pub node: Vec<NodeInfoEntity>,
    pub user: UserInfoEntity,
    #[serde(rename = "customRule")]
    pub custom_rule: Vec<CustomRuleEntity>,
}

