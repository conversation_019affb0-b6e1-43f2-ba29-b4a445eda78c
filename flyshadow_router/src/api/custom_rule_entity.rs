use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct CustomRuleEntity {
    pub id: Option<i32>,
    pub domain: String,
    #[serde(default = "custom_rule_enable_default")]
    pub enable: i32,
    pub matching: i32,
    #[serde(rename = "proxyType")]
    pub proxy_type: i32,
    #[serde(rename = "directConnPriority", default = "custom_rule_direct_conn_priority_default")]
    pub direct_conn_priority: u8,
    #[serde(default = "custom_rule_sort_default")]
    pub sort: i32,
}

fn custom_rule_enable_default() -> i32 {
    1
}
fn custom_rule_sort_default() -> i32 {
    0
}
fn custom_rule_direct_conn_priority_default() -> u8 {
    0
}