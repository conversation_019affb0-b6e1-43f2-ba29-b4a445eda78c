[package]
name = "flyshadow_common"
version = "0.1.0"
edition = "2024"

[dependencies]
futures = "0"
tokio = { version = "1", features = ["full"] }
serde_derive = "1"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
openssl = { version = "0", features = ["vendored"] }
md5 = "0"
tokio-native-tls = "0"
log = "0"
lru = "0"
trust-dns-resolver = { version = "0", features = ["default", "dns-over-rustls", "dns-over-openssl", "dns-over-https-rustls"] }
simple_logger = "5"
url = "2"
libc = "0.2"
rsa = "0.9"
base64 = "0.22"
rand = "0.8"

[target.'cfg(not(target_os = "windows"))'.dependencies]
pnet = "0"

[target.'cfg(target_os = "windows")'.dependencies]
ipconfig = "0.3.1"

