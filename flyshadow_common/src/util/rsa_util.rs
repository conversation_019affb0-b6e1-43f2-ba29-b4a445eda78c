use base64::{engine::general_purpose, Engine as _};
use rand::rngs::OsRng;
use rsa::pkcs1::{DecodeRsaPrivateKey, DecodeRsaPublicKey, EncodeRsaPrivateKey, EncodeRsaPublicKey};
use rsa::pkcs8::{DecodePrivateKey, DecodePublicKey, EncodePrivateKey, EncodePublicKey};
use rsa::traits::PublicKeyParts;
use rsa::{Pkcs1v15Encrypt, RsaPrivateKey, RsaPublicKey};

/// RSA加密工具类
/// 提供RSA密钥生成、加密、解密功能
pub struct RsaUtil;

impl RsaUtil {
    /// 生成RSA密钥对
    ///
    /// # 参数
    /// * `bits` - 密钥长度，推荐2048或4096
    ///
    /// # 返回值
    /// 返回(私钥PEM, 公钥PEM)元组，失败返回错误信息
    pub fn generate_keypair(bits: usize) -> Result<(String, String), String> {
        let mut rng = OsRng;

        // 生成私钥
        let private_key = RsaPrivateKey::new(&mut rng, bits)
            .map_err(|e| format!("生成私钥失败: {}", e))?;

        // 从私钥导出公钥
        let public_key = RsaPublicKey::from(&private_key);

        // 转换为PEM格式
        let private_pem = private_key.to_pkcs8_pem(rsa::pkcs8::LineEnding::LF)
            .map_err(|e| format!("私钥转换PEM失败: {}", e))?
            .to_string();

        let public_pem = public_key.to_public_key_pem(rsa::pkcs8::LineEnding::LF)
            .map_err(|e| format!("公钥转换PEM失败: {}", e))?;

        Ok((private_pem, public_pem))
    }

    /// 使用公钥加密数据
    ///
    /// # 参数
    /// * `public_key_pem` - 公钥PEM格式字符串
    /// * `data` - 要加密的数据
    ///
    /// # 返回值
    /// 返回Base64编码的加密数据，失败返回错误信息
    pub fn encrypt_with_public_key(public_key_pem: &str, data: &[u8]) -> Result<String, String> {
        // 解析公钥
        let public_key = RsaPublicKey::from_public_key_pem(public_key_pem)
            .map_err(|e| format!("解析公钥失败: {}", e))?;

        let mut rng = OsRng;

        // 加密数据
        let encrypted_data = public_key.encrypt(&mut rng, Pkcs1v15Encrypt, data)
            .map_err(|e| format!("加密失败: {}", e))?;

        // 转换为Base64
        Ok(general_purpose::STANDARD.encode(encrypted_data))
    }

    /// 使用私钥解密数据
    ///
    /// # 参数
    /// * `private_key_pem` - 私钥PEM格式字符串
    /// * `encrypted_data_base64` - Base64编码的加密数据
    ///
    /// # 返回值
    /// 返回解密后的原始数据，失败返回错误信息
    pub fn decrypt_with_private_key(private_key_pem: &str, encrypted_data_base64: &str) -> Result<Vec<u8>, String> {
        // 解析私钥
        let private_key = RsaPrivateKey::from_pkcs8_pem(private_key_pem)
            .map_err(|e| format!("解析私钥失败: {}", e))?;

        // 解码Base64
        let encrypted_data = general_purpose::STANDARD.decode(encrypted_data_base64)
            .map_err(|e| format!("Base64解码失败: {}", e))?;

        // 解密数据
        let decrypted_data = private_key.decrypt(Pkcs1v15Encrypt, &encrypted_data)
            .map_err(|e| format!("解密失败: {}", e))?;

        Ok(decrypted_data)
    }

    /// 分块加密大数据
    /// RSA加密有长度限制，需要分块处理
    ///
    /// # 参数
    /// * `public_key_pem` - 公钥PEM格式字符串
    /// * `data` - 要加密的数据
    ///
    /// # 返回值
    /// 返回Base64编码的加密数据，失败返回错误信息
    pub fn encrypt_large_data(public_key_pem: &str, data: &[u8]) -> Result<String, String> {
        // 解析公钥
        let public_key = RsaPublicKey::from_public_key_pem(public_key_pem)
            .map_err(|e| format!("解析公钥失败: {}", e))?;

        let mut rng = OsRng;

        // 计算每块的最大大小 (密钥长度/8 - 11字节填充)
        let key_size = public_key.size();
        let chunk_size = key_size - 11;

        let mut encrypted_chunks = Vec::new();

        // 分块加密
        for chunk in data.chunks(chunk_size) {
            let encrypted_chunk = public_key.encrypt(&mut rng, Pkcs1v15Encrypt, chunk)
                .map_err(|e| format!("分块加密失败: {}", e))?;
            encrypted_chunks.push(encrypted_chunk);
        }

        // 将所有加密块连接起来
        let mut result = Vec::new();
        for chunk in encrypted_chunks {
            result.extend_from_slice(&chunk);
        }

        // 转换为Base64
        Ok(general_purpose::STANDARD.encode(result))
    }

    /// 分块解密大数据
    ///
    /// # 参数
    /// * `private_key_pem` - 私钥PEM格式字符串
    /// * `encrypted_data_base64` - Base64编码的加密数据
    ///
    /// # 返回值
    /// 返回解密后的原始数据，失败返回错误信息
    pub fn decrypt_large_data(private_key_pem: &str, encrypted_data_base64: &str) -> Result<Vec<u8>, String> {
        // 解析私钥
        let private_key = RsaPrivateKey::from_pkcs8_pem(private_key_pem)
            .map_err(|e| format!("解析私钥失败: {}", e))?;

        // 解码Base64
        let encrypted_data = general_purpose::STANDARD.decode(encrypted_data_base64)
            .map_err(|e| format!("Base64解码失败: {}", e))?;

        // 计算每个加密块的大小
        let key_size = private_key.size();

        let mut decrypted_data = Vec::new();

        // 分块解密
        for chunk in encrypted_data.chunks(key_size) {
            let decrypted_chunk = private_key.decrypt(Pkcs1v15Encrypt, chunk)
                .map_err(|e| format!("分块解密失败: {}", e))?;
            decrypted_data.extend_from_slice(&decrypted_chunk);
        }

        Ok(decrypted_data)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_keypair_generation() {
        let result = RsaUtil::generate_keypair(2048);
        assert!(result.is_ok());

        let (private_key, public_key) = result.unwrap();
        println!("private_key: {}", private_key);
        println!("public_key: {}", public_key);
        assert!(private_key.contains("-----BEGIN PRIVATE KEY-----"));
        assert!(public_key.contains("-----BEGIN PUBLIC KEY-----"));
    }

    #[test]
    fn test_encrypt_decrypt() {
        let (private_key, public_key) = RsaUtil::generate_keypair(2048).unwrap();
        let test_data = b"Hello, RSA encryption!";

        let encrypted = RsaUtil::encrypt_with_public_key(&public_key, test_data).unwrap();
        let decrypted = RsaUtil::decrypt_with_private_key(&private_key, &encrypted).unwrap();

        assert_eq!(test_data, decrypted.as_slice());
    }

    #[test]
    fn test_large_data_encrypt_decrypt() {
        let (private_key, public_key) = RsaUtil::generate_keypair(2048).unwrap();
        let test_data = b"This is a very long message that exceeds the RSA encryption limit and needs to be split into multiple chunks for encryption and decryption.";

        let encrypted = RsaUtil::encrypt_large_data(&public_key, test_data).unwrap();
        let decrypted = RsaUtil::decrypt_large_data(&private_key, &encrypted).unwrap();

        assert_eq!(test_data, decrypted.as_slice());
    }
}
