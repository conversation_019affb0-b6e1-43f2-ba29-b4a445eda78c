use crate::interface::interface_selector::InterfaceSelector;
use crate::util::dns_provider::MyTokioRuntimeProvider;
use futures::future::{join_all, select_ok};
use futures::FutureExt;
use lru::LruCache;
use serde_derive::{Deserialize, Serialize};
use std::net::{IpAddr, Ipv4Addr, Ipv6Addr, SocketAddr};
use std::num::NonZeroUsize;
use std::time::{Duration, Instant};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::TcpSocket;
use tokio::sync::RwLock;
use tokio::time::timeout;
use tokio_native_tls::{native_tls, TlsConnector};
use trust_dns_resolver::config::{NameServerConfigGroup, ResolverConfig, ResolverOpts};
use trust_dns_resolver::name_server::{ConnectionProvider, GenericConnector};
use trust_dns_resolver::{AsyncResolver, TokioAsyncResolver};

#[derive(Clone)]
struct CachedEntry {
    ip: IpAddr,
    expiry: Instant,
}
pub struct DnsUtil {
    enable_cache: bool,
    ip_domain_cache: RwLock<LruCache<String, Vec<CachedEntry>>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct DnsEntity {
    #[serde(rename = "Status")]
    pub status: i32,
    #[serde(rename = "Answer")]
    pub answer: Option<Vec<Answer>>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
struct Answer {
    pub name: Option<String>,
    #[serde(rename = "type")]
    pub a_type: Option<i32>,
    #[serde(rename = "TTL")]
    pub ttl: Option<u64>,
    pub data: Option<String>,
}

impl DnsUtil {
    pub fn new(enable_cache: bool) -> Self {
        DnsUtil {
            enable_cache,
            ip_domain_cache: RwLock::new(LruCache::new(NonZeroUsize::new(2000).unwrap())),
        }
    }

    pub async fn query_ip(&self, interface_selector: Option<InterfaceSelector>, domain: &String) -> Result<Vec<IpAddr>, String> {
        if let Ok(ip_addr) = domain.parse::<IpAddr>() {
            return Ok(vec![ip_addr]);
        }

        if self.enable_cache {
            if let Some(entry) = self.ip_domain_cache.write().await.get(domain) {
                let ip: Vec<IpAddr> = entry.iter().filter(|e| e.expiry > Instant::now())
                    .map(|e| e.ip).collect();
                if ip.len() > 0 {
                    return Ok(ip.clone());
                }
            }
        }

        let query_by_aliyun = interface_selector.clone()
            .map(|iface| iface.ipv4.is_none() && iface.ipv6.is_none() && iface.ipv4_device.is_none() && iface.ipv6_device.is_none())
            .unwrap_or(true);

        match select_ok(vec![
            self.query_by_aliyun(domain, query_by_aliyun).boxed(),
            self.query_by_tencent(interface_selector, domain).boxed(),
            // self.query_by_doh(bind_addr, bind_device_name, domain).boxed(),
        ]).await {
            Ok(result) => {
                Ok(result.0)
            }
            Err(e) => {
                Err(e)
            }
        }
    }

    async fn query_by_resolver<P>(&self, domain: &String, resolver: AsyncResolver<P>) -> Result<Vec<IpAddr>, String>
    where
        P: ConnectionProvider,
    {
        let result_vec = join_all(vec![
            async {
                resolver
                    .ipv6_lookup(domain.to_string())
                    .await
                    .map(|lookup| lookup.iter().map(|ip| IpAddr::from(ip.0)).collect::<Vec<IpAddr>>())
            }.boxed(),
            async {
                resolver
                    .ipv4_lookup(domain.to_string())
                    .await
                    .map(|lookup| lookup.iter().map(|ip| IpAddr::from(ip.0)).collect::<Vec<IpAddr>>())
            }.boxed()
        ]).await;

        let mut error = None;
        let mut ip_addr_vec = vec![];

        for r in result_vec {
            match r {
                Ok(mut vec) => {
                    if !vec.is_empty() {
                        ip_addr_vec.append(&mut vec);
                    }
                }
                Err(e) => {
                    error = Some(e);
                }
            }
        }

        if ip_addr_vec.is_empty() {
            if let Some(e) = error {
                Err(format!("resolver {} error {}", domain, e.to_string()))
            } else {
                Err("not find the host".to_string())
            }
        } else {
            Ok(ip_addr_vec)
        }
    }

    async fn query_by_aliyun(&self, domain: &String, query_by_aliyun: bool) -> Result<Vec<IpAddr>, String> {
        if !query_by_aliyun {
            return Err("Disable query by aliyun".to_string());
        }
        let resolver = TokioAsyncResolver::tokio(ResolverConfig::from_parts(
            None,
            vec![],
            NameServerConfigGroup::from_ips_clear(&[
                IpAddr::V4(Ipv4Addr::new(223, 5, 5, 5)),
                IpAddr::V4(Ipv4Addr::new(223, 6, 6, 6)),
                IpAddr::V6(Ipv6Addr::new(0x2400, 0x3200, 0, 0, 0, 0, 0, 0x0001)),
                IpAddr::V6(Ipv6Addr::new(0x2400, 0x3200, 0xbaba, 0, 0, 0, 0, 0x0001)),
            ], 53, true),
        ), ResolverOpts::default());

        self.query_by_resolver(domain, resolver).await
    }

    async fn query_by_tencent(&self, interface_selector: Option<InterfaceSelector>, domain: &String) -> Result<Vec<IpAddr>, String> {
        let (bind_addr, bind_device_name) = interface_selector
            .map(|iface| (iface.ipv4.map(|ipv4| SocketAddr::new(IpAddr::from(ipv4), 0)), iface.ipv4_device))
            .unwrap_or((None, None));

        let resolver = AsyncResolver::new(ResolverConfig::from_parts(
            None,
            vec![],
            NameServerConfigGroup::from_ips_https(&[
                IpAddr::V4(Ipv4Addr::new(1, 12, 12, 12)),
                IpAddr::V4(Ipv4Addr::new(120, 53, 53, 53)),
            ], 443, "**********".to_string(), true),
        ), ResolverOpts::default(), GenericConnector::new(MyTokioRuntimeProvider::new(bind_addr, bind_device_name)));

        self.query_by_resolver(domain, resolver).await
    }

    async fn query_by_doh(&self, bind_addr: Option<SocketAddr>, bind_device_name: Option<String>, domain: &String) -> Result<Vec<IpAddr>, String> {
        let tcp_socket = match TcpSocket::new_v4() {
            Ok(v) => { v }
            Err(e) => {
                return Err(e.to_string());
            }
        };

        match timeout(Duration::from_secs(4), async move {
            if let Some(bind_addr) = bind_addr {
                #[cfg(
                    any(target_os = "android", target_os = "fuchsia", target_os = "linux")
                )]
                {
                    if let Some(bind_device_name) = bind_device_name {
                        if bind_device_name != "".to_string() {
                            if let Err(e) = tcp_socket.bind_device(Some(bind_device_name.as_bytes())) {
                                log::error!("Socket bind device error: {}",e)
                            }
                        }
                    }
                }
                if let Err(e) = tcp_socket.bind(bind_addr) {
                    return Err(e.to_string());
                }
            }
            let tcp_stream = match tcp_socket.connect(SocketAddr::new("**********".parse().unwrap(), 443)).await {
                Ok(tcp_stream) => {
                    tcp_stream
                }
                Err(e) => {
                    return Err(e.to_string());
                }
            };
            tcp_stream.set_nodelay(true).unwrap();

            // 创建一个TLS连接器
            let connector = native_tls::TlsConnector::builder()
                .danger_accept_invalid_certs(true) // 忽略无效的SSL证书
                .build()
                .unwrap();
            let connector = TlsConnector::from(connector);

            // 升级TCP连接到TLS
            let mut stream = match connector.connect("**********", tcp_stream).await {
                Ok(stream) => { stream }
                Err(e) => {
                    return Err(e.to_string());
                }
            };
            // 发送HTTP请求
            let request = format!(
                "GET /dns-query?name={}&type=A HTTP/1.1\r\n\
         Host: **********\r\n\
         Connection: close\r\n\
         \r\n",
                domain
            );
            if let Err(e) = stream.write_all(request.as_bytes()).await {
                return Err(e.to_string());
            }

            // 读取响应
            let mut response = Vec::new();
            if let Err(e) = stream.read_to_end(&mut response).await {
                return Err(e.to_string());
            }

            let response_str = String::from_utf8_lossy(&response);

            if let Some(pos) = response_str.find("\r\n\r\n") {
                // 去除HTTP头信息，保留正文
                let json_part = &response[pos + 4..];

                return match serde_json::from_slice::<DnsEntity>(&json_part)
                    .map_err(|e| e.to_string())
                    .map(|dns_entity| dns_entity.answer)
                    .map(|answer| answer.unwrap_or(vec![]))
                    .map(|answer| answer.into_iter().filter(|a| a.a_type == Some(1) && a.data.is_some() && a.data.clone().unwrap().parse::<Ipv4Addr>().is_ok()).collect::<Vec<Answer>>())
                {
                    Ok(answer) => {
                        if answer.len() == 0 {
                            return Err("Empty answers".to_string());
                        }
                        let mut ipv4_vec = vec![];
                        let mut entry_vec = vec![];
                        for answer in answer {
                            let ipv4_addr = answer.data.clone().unwrap().parse::<IpAddr>().unwrap();
                            let cached_entry = CachedEntry {
                                ip: ipv4_addr.clone(),
                                expiry: Instant::now() + Duration::from_secs(answer.ttl.unwrap()),
                            };
                            entry_vec.push(cached_entry);
                            ipv4_vec.push(ipv4_addr);
                        }

                        if self.enable_cache {
                            self.ip_domain_cache.write().await.put(domain.to_string(), entry_vec);
                        }
                        Ok(ipv4_vec)
                    }
                    Err(e) => { Err(e) }
                };
            }

            return Err("".to_string());
        }).await {
            Ok(result) => {
                result
            }
            Err(_e) => {
                Err("Query ip timeout".to_string())
            }
        }
    }
}


mod test {
    use crate::util::dns_util::DnsUtil;

    #[tokio::test]
    async fn query_dns() {
        let dns_util = DnsUtil::new(false);
        let result = dns_util.query_ip(None, &"www.google.com".to_string()).await;
        println!("{:?}", result);
        let result = dns_util.query_ip(None, &"main.hkspeedup.com".to_string()).await;
        println!("{:?}", result);
    }
}