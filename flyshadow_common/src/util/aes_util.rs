use openssl::symm::{decrypt, encrypt, Cipher};


pub struct AesUtil;

impl AesUtil {
    /// 加密
    pub fn encrypt(pwd: &[u8], data: &[u8]) -> Result<Vec<u8>, String> {
        let cipher = Cipher::aes_256_ecb();
        match encrypt(cipher, pwd, None, data) {
            Ok(vec) => { Ok(vec) }
            Err(e) => { Err(e.to_string()) }
        }
    }

    /// 解密
    pub fn decrypt(buffer_tmp: &[u8], password_md5_byte: &[u8]) -> Result<Vec<u8>, String> {
        // 解密
        let cipher = Cipher::aes_256_ecb();
        match decrypt(cipher, password_md5_byte, None, buffer_tmp) {
            Ok(vec) => { Ok(vec) }
            Err(e) => { Err(e.to_string()) }
        }
    }
}