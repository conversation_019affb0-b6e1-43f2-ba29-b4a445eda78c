use url::Url;

#[test]
fn test() {
    use log::info;
    let _ = simple_logger::init_with_env();
    // 示例代理请求字符串
    let proxy_request = "GET http://example.com/path HTTP/1.1\r\nHost: example.com:80\r\n\r\n";
    let proxy_request1 = "GET http://example.com HTTP/1.1\r\nHost: example.com:80\r\n\r\n";
    let proxy_request2 = "GET http://example.com/ HTTP/1.1\r\nHost: example.com:80\r\n\r\n";
    let proxy_request3 = "GET http://example.com:6554/ HTTP/1.1\r\nHost: example.com:80\r\n\r\n";
    let proxy_request4 = "GET http://example.com:6654/path HTTP/1.1\r\nHost: example.com:80\r\n\r\n";
    let proxy_request5 = "POST /cgi-bin/micromsg-bin/applewatchlongpolling HTTP/1.1\r\nHost: szminorshort.weixin.qq.com\r\n\r\n";
    let proxy_request7 = "GET http://example.com:6554 HTTP/1.1\r\nHost: example.com:80\r\n\r\n";
    let proxy_request8 = "GET example.com/path HTTP/1.1\r\nHost: example.com:80\r\n\r\n";
    let proxy_request9 = "GET example.com/path HTTP/1.1\r\nHost: example.com:80\r\n\r\n";
    let proxy_request10 = "GET example.com:6655/path HTTP/1.1\r\nHost: example.com:80\r\n\r\n";
    let proxy_request11 = "GET http://*************:8888/static/js/jsencrypt.min.js?t=20221221 HTTP/1.1\r\nHost: *************:8888";
    let connect_request = "CONNECT example.com HTTP/1.1";
    let connect_request1 = "CONNECT example.com:443 HTTP/1.1";
    let connect_request2 = "CONNECT example.com:5566 HTTP/1.1";

    // log::info!("proxy_request {:?}", resolve_uri(proxy_request.as_bytes()));
    // log::info!("proxy_request1 {:?}", resolve_uri(proxy_request1.as_bytes()));
    // log::info!("proxy_request2 {:?}", resolve_uri(proxy_request2.as_bytes()));
    // log::info!("proxy_request3 {:?}", resolve_uri(proxy_request3.as_bytes()));
    // log::info!("proxy_request4 {:?}", resolve_uri(proxy_request4.as_bytes()));
    // log::info!("proxy_request5 {:?}", resolve_uri(proxy_request5.as_bytes()));
    // log::info!("proxy_request7 {:?}", resolve_uri(proxy_request7.as_bytes()));
    // log::info!("proxy_request8 {:?}", resolve_uri(proxy_request8.as_bytes()));
    // log::info!("proxy_request9 {:?}", resolve_uri(proxy_request9.as_bytes()));
    // log::info!("proxy_request10 {:?}", resolve_uri(proxy_request10.as_bytes()));
    // log::info!("proxy_request11 {:?}", resolve_uri(proxy_request11.as_bytes()));
    // log::info!("connect_request {:?}", resolve_uri(connect_request.as_bytes()));
    // log::info!("connect_request1 {:?}", resolve_uri(connect_request1.as_bytes()));
    // log::info!("connect_request2 {:?}", resolve_uri(connect_request2.as_bytes()));

    // let mut result = vec![];
    // remove_proxy_characteristic(proxy_request.as_bytes(), &mut result);
    // let mut result1 = vec![];
    // remove_proxy_characteristic(proxy_request1.as_bytes(), &mut result1);
    // let mut result2 = vec![];
    // remove_proxy_characteristic(proxy_request2.as_bytes(), &mut result2);
    // let mut result3 = vec![];
    // remove_proxy_characteristic(proxy_request3.as_bytes(), &mut result3);
    // let mut result4 = vec![];
    // remove_proxy_characteristic(proxy_request4.as_bytes(), &mut result4);
    // log::info!("remove proxy: {:?}", String::from_utf8_lossy(result.as_slice()));
    // log::info!("remove proxy: {:?}", String::from_utf8_lossy(result1.as_slice()));
    // log::info!("remove proxy: {:?}", String::from_utf8_lossy(result2.as_slice()));
    // log::info!("remove proxy: {:?}", String::from_utf8_lossy(result3.as_slice()));
    // log::info!("remove proxy: {:?}", String::from_utf8_lossy(result4.as_slice()));


    let proxy_request5 = "GET http://*************:8888/static/js/jsencrypt.min.js?t=20221221 HTTP/1.1
                            Host: *************:8888
                            Proxy-Connection: keep-alive
                            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
                            DNT: 1
                            Accept: */*
                            Referer: /static/js/jsencrypt.min.js?t=20221221 HTTP/1.1
                            Host: *************:8888
                            Proxy-Connection: keep-alive
                            User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
                            DNT: 1
                            Accept: */*
                            Referer: http://*************:8888/kason
                            Accept-Encoding: gzip, deflate
                            Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
                            Cookie: config-tab=baseConfig; refresh_software_list=; order=id%20desc; serverType=nginx; pro_end=-1; ltd_end=-1; memSize=976; bt_user_info=%7B%22status%22%3Atrue%2C%22msg%22%3A%22%u83B7%u53D6%u6210%u529F%21%22%2C%22data%22%3A%7
                            B%22username%22%3A%22136****3009%22%7D%7D; backup_path=/www/backup; db_page_model=redis; 44c365fde28895a28fe0fb764e883280=5801205e-4f77-43e6-bba6-8e6065707201.XfvK57xlxq02KvbmyniQ9D1TT-c";
    let mut result5 = vec![];
    remove_proxy_characteristic(proxy_request5.as_bytes(), &mut result5);
    info!("remove proxy: {}", String::from_utf8_lossy(result5.as_slice()));

    // let proxy_request5 = "GET /ws HTTP/1.1
    // Host: ************:65431
    // Connection: Upgrade
    // Pragma: no-cache
    // Cache-Control: no-cache
    // User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
    // Upgrade: websocket
    // Origin: http://************:65431
    // Sec-WebSocket-Version: 13
    // Accept-Encoding: gzip, deflate
    // Accept-Language: zh-CN,zh;q=0.9,en;q=0.8
    // Cookie: sidebarStatus=1; forward_panel_token=6776dc245ce664dbbf754d2184e3381d; cu={%22token%22:%226776dc245ce664dbbf754d2184e3381d%22%2C%22username%22:%22admin%22%2C%22userId%22:1%2C%22expireTime%22:1706718940912%2C%22createTime%22:1706717140920%2C%22updateTime%22:null}; cuid=1; nezha-dashboard-vp=%242a%2410%24EurXgIKXRRDohH8Zz0HKsuYgl0uNdon4rFmpfuC4ujy7Xk05Iq3AC; nezha-dashboard=Lp7IR8DtMA2Ikeb6yw2TpDRm4Gm32Gw3
    // Sec-WebSocket-Key: RW1S3jdE+/PEYRidBlaljg==
    // Sec-WebSocket-Extensions: permessage-deflate; client_max_window_bits";
    // let mut result5 = vec![];
    // remove_proxy_characteristic(proxy_request5.as_bytes(), &mut result5);
    // info!("remove proxy: {}", String::from_utf8_lossy(result5.as_slice()));

    // let header_data = hex::decode("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").unwrap();
    // let mut result6 = vec![];
    // let result = remove_proxy_characteristic(&header_data, &mut result6);
    // info!("remove result:{}，\n str: {}",result,hex::encode(result6));
}

#[derive(PartialEq, Debug)]
pub enum HttpMethod {
    Connect,
    Http,
    Unknown,
}


#[derive(PartialEq, Debug)]
pub enum RemoveProxyStatus {
    None,
    Continue,
    SetNone,
}

#[derive(PartialEq, Debug)]
pub enum LineBreakStatus {
    None,
    Continue,
}


pub fn remove_proxy_characteristic(header_data: &[u8], result: &mut Vec<u8>) -> bool {
    let get_b = b"GET";
    let post_b = b"POST";
    let put_b = b"PUT";
    let patch_b = b"PATCH";
    let delete_b = b"DELETE";
    let head_b = b"HEAD";
    let options_b = b"OPTIONS";
    let line_break_bytes = b'\n';
    let proxy_connection_bytes = b"Proxy-Connection";
    let proxy_authorization_bytes = b"Proxy-Authorization";

    let mut continue_first_line = false;
    // 检查请求头
    let len = header_data.len();
    if header_data.starts_with(get_b) || header_data.starts_with(post_b) || header_data.starts_with(put_b) || header_data.starts_with(patch_b) ||
        header_data.starts_with(delete_b) || header_data.starts_with(head_b) || header_data.starts_with(options_b) {
        for index in 0..header_data.len() {
            if &header_data[index] == &line_break_bytes {
                let first_line = String::from_utf8_lossy(&header_data[0..index]);
                if first_line.contains("HTTP/") {
                    // 解析URL
                    let mut parts: Vec<&str> = first_line.split_whitespace().collect();
                    let url = parts.get(1).unwrap_or(&"");
                    if let Ok(url) = Url::parse(url) {
                        let path = format!("{}{}", url.path(), url.query().map(|str| format!("?{}", str)).unwrap_or_default());
                        parts[1] = path.as_str();
                        result.extend_from_slice(parts.join(" ").as_bytes());
                        result.push(b'\r');
                        result.push(line_break_bytes);
                        continue_first_line = true;
                    }

                    break;
                }

                return false;
            }
        }
    }

    let mut remove_proxy_status = RemoveProxyStatus::None;
    let mut line_break_status = LineBreakStatus::None;
    let mut line_break_count = 0;
    for index in 0..len {
        // 跳换行符
        if line_break_status == LineBreakStatus::Continue {
            line_break_status = LineBreakStatus::None;
            // continue;
        }
        // 跳过第一行
        if continue_first_line {
            if &header_data[index] == &line_break_bytes {
                continue_first_line = false;
                line_break_status = LineBreakStatus::Continue;
            }
            continue;
        }
        // 判断代理请求头
        if len - index > 16 && &header_data[index..index + 16] == proxy_connection_bytes {
            remove_proxy_status = RemoveProxyStatus::Continue;
        }
        // 判断代理授权请求头
        if len - index > 19 && &header_data[index..index + 19] == proxy_authorization_bytes {
            remove_proxy_status = RemoveProxyStatus::Continue;
        }
        // 下一行
        if &header_data[index] == &line_break_bytes {
            if remove_proxy_status == RemoveProxyStatus::Continue {
                line_break_status = LineBreakStatus::Continue;
                remove_proxy_status = RemoveProxyStatus::SetNone;
            }
            line_break_count = line_break_count + 1;
        } else {
            line_break_count = 0;
        }
        if line_break_count == 2 {
            if result.len() > 0 {
                result.extend_from_slice(&header_data[index..]);
            }
            break;
        }
        // 如果存在代理和代理授权请求头
        if remove_proxy_status != RemoveProxyStatus::None {
            if remove_proxy_status == RemoveProxyStatus::SetNone {
                remove_proxy_status = RemoveProxyStatus::None;
            }
            continue;
        }
        result.push(header_data[index]);
    }

    if result.len() == 0 {
        false
    } else {
        true
    }
}

/// 解析Uri
pub fn resolve_uri(header_data: &[u8]) -> (String, u16, HttpMethod) {
    let mut first_line = true;
    let mut http_method = HttpMethod::Unknown;
    let mut port = 0;
    for line in String::from_utf8_lossy(header_data).lines() {
        if first_line {
            first_line = false;
            if line.starts_with("CONNECT") {
                http_method = HttpMethod::Connect;
                port = 443;
            } else {
                http_method = HttpMethod::Http;
                port = 80;
            }

            let mut url_str = line.split_whitespace().nth(1).unwrap_or_default().to_string();
            if http_method == HttpMethod::Connect && !url_str.starts_with("http") {
                url_str = format!("https://{}", url_str);
            } else if !url_str.starts_with("/") && !url_str.starts_with("http") {
                url_str = format!("http://{}", url_str);
            }

            // 解析 URL
            let parsed_url = if let Ok(url) = Url::parse(url_str.as_str()) {
                url
            } else { continue };

            // 获取域名
            let domain = parsed_url.host_str().unwrap_or_default();

            // 获取端口号，如果未指定，则返回默认端口
            port = parsed_url.port_or_known_default().unwrap_or(port);

            return (domain.to_string(),
                    port,
                    http_method);
        } else {
            if line.is_empty() {
                return (String::new(), 0, HttpMethod::Unknown);
            }
            if line.starts_with("Host:") {
                // 去除 "Host: " 前缀
                let host_str = line.trim_start_matches("Host: ").trim();

                // 分割域名和端口号
                let mut parts = host_str.split(':');
                let domain = parts.next().unwrap();
                let port = parts.next().unwrap_or(&*port.to_string()).parse::<u16>().unwrap_or(port);

                return (domain.to_string(), port, http_method);
            }
        }
    }

    ("".to_string(), 0, HttpMethod::Unknown)
}
