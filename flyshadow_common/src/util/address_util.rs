pub struct AddressUtil;

impl AddressUtil {
    /// 将给定的地址字符串拆分为IP地址和端口号的元组。
    ///
    /// # 参数
    ///
    /// * `address` - 一个包含地址和端口（以冒号分隔）的字符串的引用。
    ///
    /// # 返回值
    ///
    /// * `Option<(String, u16)>` - 如果输入格式有效，返回包含地址和端口的元组，否则返回None。
    pub fn split_address_port(address: &String) -> Option<(String, u16)> {
        // 检查地址是否包含冒号
        if !address.contains(":") {
            return None;
        }

        // 基于冒号拆分地址为多个部分
        let parts: Vec<&str> = address.split(':').collect();

        // 如果拆分后的部分数量不等于2，则返回None
        if parts.len() != 2 {
            return None;
        }

        // 获取地址部分
        let addr = parts[0].to_string();

        // 尝试将端口部分解析为u16类型
        let port = parts[1].parse::<u16>();

        // 根据解析结果返回相应的元组或None
        match port {
            Ok(p) => Some((addr, p)),
            Err(_) => None,
        }
    }
}