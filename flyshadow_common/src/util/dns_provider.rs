use std::future::Future;
use std::io;
use std::net::SocketAddr;
use std::pin::Pin;
use tokio::net::{TcpSocket, TcpStream, UdpSocket};
use trust_dns_resolver::name_server::RuntimeProvider;
use trust_dns_resolver::proto::iocompat::AsyncIoTokioAsStd;
use trust_dns_resolver::proto::TokioTime;
use trust_dns_resolver::TokioHandle;

#[derive(<PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct MyTokioRuntimeProvider {
    handle: TokioHandle,
    bind_addr: Option<SocketAddr>,
    bind_device_name: Option<String>,
}

impl MyTokioRuntimeProvider {
    /// Create a Tokio runtime
    pub fn new(bind_addr: Option<SocketAddr>, bind_device_name: Option<String>) -> Self {
        MyTokioRuntimeProvider {
            handle: TokioHandle::default(),
            bind_addr,
            bind_device_name,
        }
    }
}


impl RuntimeProvider for MyTokioRuntimeProvider {
    type Handle = TokioHandle;
    type Timer = TokioTime;
    type Udp = UdpSocket;
    type Tcp = AsyncIoTokioAsStd<TcpStream>;

    fn create_handle(&self) -> Self::Handle {
        self.handle.clone()
    }

    fn connect_tcp(
        &self,
        server_addr: SocketAddr,
    ) -> Pin<Box<dyn Send + Future<Output=io::Result<Self::Tcp>>>> {
        let bind_device_name = self.bind_device_name.clone();
        let bind_addr = self.bind_addr.clone();
        Box::pin(async move {
            if let Some(device_name) = bind_device_name {
                let tcp_socket = TcpSocket::new_v4()?;
                #[cfg(
                    any(target_os = "android", target_os = "fuchsia", target_os = "linux")
                )]
                {
                    tcp_socket.bind_device(Some(device_name.as_bytes()))?;
                }
                tcp_socket.connect(server_addr)
                    .await
                    .map(AsyncIoTokioAsStd)
            } else if let Some(bind_addr) = bind_addr {
                let tcp_socket = TcpSocket::new_v4()?;
                tcp_socket.bind(bind_addr)?;
                tcp_socket.connect(server_addr)
                    .await
                    .map(AsyncIoTokioAsStd)
            } else {
                TcpStream::connect(server_addr)
                    .await
                    .map(AsyncIoTokioAsStd)
            }
        })
    }

    fn bind_udp(
        &self,
        local_addr: SocketAddr,
        _server_addr: SocketAddr,
    ) -> Pin<Box<dyn Send + Future<Output=io::Result<Self::Udp>>>> {
        let bind_device_name = self.bind_device_name.clone();
        let bind_addr = self.bind_addr.clone();
        Box::pin(async move {
            if let Some(device_name) = bind_device_name {
                let udp_socket = UdpSocket::bind(local_addr).await?;
                #[cfg(
                    any(target_os = "android", target_os = "fuchsia", target_os = "linux")
                )]
                {
                    udp_socket.bind_device(Some(device_name.as_bytes()))?;
                }
                Ok(udp_socket)
            } else if let Some(bind_addr) = bind_addr {
                Ok(UdpSocket::bind(bind_addr).await?)
            } else {
                UdpSocket::bind(local_addr).await
            }
        })
    }
}