pub mod interface_selector;

#[cfg(any(target_os = "windows"))]
pub mod win_selector;
#[cfg(any(target_os = "windows"))]
pub use win_selector::*;

#[cfg(any(target_os = "macos"))]
pub mod mac_selector;
#[cfg(any(target_os = "macos"))]
pub use mac_selector::*;

#[cfg(any(target_os = "linux"))]
pub mod linux_selector;
#[cfg(any(target_os = "linux"))]
pub use linux_selector::*;

#[cfg(not(any(target_os = "windows", target_os = "linux", target_os = "macos")))]
pub mod unsupport_selector;
#[cfg(not(any(target_os = "windows", target_os = "linux", target_os = "macos")))]
pub use unsupport_selector::*;
