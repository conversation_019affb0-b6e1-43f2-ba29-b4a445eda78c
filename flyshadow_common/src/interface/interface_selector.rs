use log::error;
use std::io::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use std::mem::size_of_val;
use std::net::{Ipv4Addr, Ipv6Addr, SocketAddr, SocketAddrV4, SocketAddrV6};
use tokio::net::TcpSocket;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum IfaceSelectType {
    IP,
    DEVICE,
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct InterfaceSelector {
    pub ipv4: Option<Ipv4Addr>,
    pub ipv4_device: Option<String>,
    pub ipv4_device_index: Option<u32>,
    pub ipv6: Option<Ipv6Addr>,
    pub ipv6_device: Option<String>,
    pub ipv6_device_index: Option<u32>,
}

impl InterfaceSelector {
    /// 绑定IPV6网卡
    pub fn tcp_bind_ipv6(interface_selector: InterfaceSelector, tcp_socket: &TcpSocket) -> Result<(), Error> {
        if let Some(device_name) = interface_selector.ipv6_device {
            #[cfg(any(target_os = "linux"))]
            {
                use libc::{setsockopt, socklen_t, IPV6_UNICAST_HOPS, SOL_IPV6};
                use std::os::fd::AsRawFd;
                if let Err(e) = tcp_socket.bind_device(Some(device_name.as_bytes())) {
                    error!("IPv6 Socket bind device error: {}",e)
                }
                if let Some(device_index) = interface_selector.ipv6_device_index {
                    let result = unsafe {
                        setsockopt(tcp_socket.as_raw_fd(),
                                   SOL_IPV6,
                                   IPV6_UNICAST_HOPS,
                                   &device_index as *const _ as *const libc::c_void,
                                   size_of_val(&device_index) as socklen_t)
                    };
                    if result != 0 {
                        error!("IPv6 Socket bind device index[{}] error",device_index)
                    }
                }
            }
        } else if let Some(ipv6addr) = interface_selector.ipv6 {
            tcp_socket.bind(SocketAddr::from(SocketAddrV6::new(ipv6addr, 0, 0, 0)))?;
        } else {
            return Err(Error::new(ErrorKind::Other, "Ipv6 is not supported"));
        }
        Ok(())
    }

    /// 绑定IPV4网卡
    pub fn tcp_bind_ipv4(interface_selector: InterfaceSelector, tcp_socket: &TcpSocket) -> Result<(), Error> {
        if let Some(device_name) = interface_selector.ipv4_device {
            #[cfg(
                any(target_os = "linux")
            )]
            tcp_socket.bind_device(Some(device_name.as_bytes()))?;
        } else if let Some(ipv4addr) = interface_selector.ipv4 {
            tcp_socket.bind(SocketAddr::from(SocketAddrV4::new(ipv4addr, 0)))?;
        } else {
            error!("IPv4 Socket bind error,no ipv4 device");
        }
        Ok(())
    }
}