use super::interface_selector::*;
use futures::future::select_ok;
use log::{info, warn};
use pnet::ipnetwork::IpNetwork;
use std::net::{Ipv4Addr, Ipv6Addr, SocketAddr, SocketAddrV4, SocketAddrV6};
use std::time::Duration;
use tokio::net::TcpSocket;
use tokio::time::{sleep, timeout};

impl InterfaceSelector {
    pub async fn select_iface(iface_select_type: IfaceSelectType, ipv6_enable: bool) -> Result<InterfaceSelector, String> {
        let interfaces = pnet::datalink::interfaces();

        info!("Selected interfaces: {:?}", interfaces);

        let mut ipv4_connect_fn_vec = vec![];
        let mut ipv6_connect_fn_vec = vec![];

        // 遍历并打印每个接口的信息
        for interface in interfaces {
            for x in interface.ips {
                match x {
                    IpNetwork::V4(ipv4_network) => {
                        ipv4_connect_fn_vec.push(Box::pin(Self::create_ipv4_connect_fn(iface_select_type.clone(),
                                                                                       interface.name.clone(),
                                                                                       interface.index,
                                                                                       ipv4_network.ip().clone(),
                                                                                       "*******:443".parse().unwrap())));
                        ipv4_connect_fn_vec.push(Box::pin(Self::create_ipv4_connect_fn(iface_select_type.clone(),
                                                                                       interface.name.clone(),
                                                                                       interface.index,
                                                                                       ipv4_network.ip().clone(),
                                                                                       "*******:443".parse().unwrap())));
                        ipv4_connect_fn_vec.push(Box::pin(Self::create_ipv4_connect_fn(iface_select_type.clone(),
                                                                                       interface.name.clone(),
                                                                                       interface.index,
                                                                                       ipv4_network.ip().clone(),
                                                                                       "*********:443".parse().unwrap())));
                        ipv4_connect_fn_vec.push(Box::pin(Self::create_ipv4_connect_fn(iface_select_type.clone(),
                                                                                       interface.name.clone(),
                                                                                       interface.index,
                                                                                       ipv4_network.ip().clone(),
                                                                                       "*********:443".parse().unwrap())));
                    }
                    IpNetwork::V6(ipv6_network) => {
                        if !ipv6_enable
                        {
                            continue;
                        }
                        ipv6_connect_fn_vec.push(Box::pin(Self::create_ipv6_connect_fn(iface_select_type.clone(),
                                                                                       interface.name.clone(),
                                                                                       interface.index,
                                                                                       ipv6_network.ip().clone(),
                                                                                       SocketAddrV6::new(Ipv6Addr::new(2606, 4700, 4700, 0, 0, 0, 0, 1001), 443, 0, 0).into())));
                        ipv6_connect_fn_vec.push(Box::pin(Self::create_ipv6_connect_fn(iface_select_type.clone(),
                                                                                       interface.name.clone(),
                                                                                       interface.index,
                                                                                       ipv6_network.ip().clone(),
                                                                                       SocketAddrV6::new(Ipv6Addr::new(2606, 4700, 4700, 0, 0, 0, 0, 1111), 443, 0, 0).into())));
                        ipv6_connect_fn_vec.push(Box::pin(Self::create_ipv6_connect_fn(iface_select_type.clone(),
                                                                                       interface.name.clone(),
                                                                                       interface.index,
                                                                                       ipv6_network.ip().clone(),
                                                                                       SocketAddrV6::new(Ipv6Addr::new(2400, 3200, 0, 0, 0, 0, 0, 1), 443, 0, 0).into())));
                        ipv6_connect_fn_vec.push(Box::pin(Self::create_ipv6_connect_fn(iface_select_type.clone(),
                                                                                       interface.name.clone(),
                                                                                       interface.index,
                                                                                       ipv6_network.ip().clone(),
                                                                                       SocketAddrV6::new(Ipv6Addr::new(2400, 3200, 0xbaba, 0, 0, 0, 0, 1), 443, 0, 0).into())));
                    }
                }
            }
        }

        if ipv4_connect_fn_vec.is_empty() {
            return Err("none ipv4 interface".to_string());
        }
        let ipv4_result = select_ok(ipv4_connect_fn_vec).await.map_err(|e| format!("select ipv4 iface error: {}", e))?.0;

        let ipv6_result = if ipv6_connect_fn_vec.is_empty() {
            warn!("none ipv6 interface");
            (None, None, None)
        } else {
            match select_ok(ipv6_connect_fn_vec).await {
                Ok(r) => { r.0 }
                Err(_) => {
                    warn!("select ipv6 iface error");
                    (None, None, None)
                }
            }
        };

        let selector = InterfaceSelector {
            ipv4: ipv4_result.1,
            ipv4_device: ipv4_result.0,
            ipv4_device_index: ipv4_result.2,
            ipv6: ipv6_result.1,
            ipv6_device: ipv6_result.0,
            ipv6_device_index: ipv6_result.2,
        };
        info!("select interface: {:?}",selector);
        Ok(selector)
    }

    async fn create_ipv4_connect_fn(iface_select_type: IfaceSelectType, name: String, index: u32, ipv4addr: Ipv4Addr, connect_addr: SocketAddr) -> Result<(Option<String>, Option<Ipv4Addr>, Option<u32>), String> {
        if name.starts_with("utun") ||
            ipv4addr == "***********".parse::<Ipv4Addr>().map_err(|e| format!("*********** parse error: {}", e))? {
            return Err("".to_string());
        }
        if !(name.starts_with("en") || name.starts_with("wlan") || name.starts_with("eth") || name.starts_with("wlp") || name.starts_with("apc") || name.starts_with("br")) {
            sleep(Duration::from_millis(500)).await;
        }
        let socket = TcpSocket::new_v4().map_err(|e| format!("create ipv4 socket error: {}", e))?;
        match iface_select_type {
            IfaceSelectType::IP => {
                socket.bind(SocketAddr::from(SocketAddrV4::new(ipv4addr.clone(), 0))).map_err(|e| format!("ipv4 bind socket error: {}", e))?;
            }
            IfaceSelectType::DEVICE => {
                #[cfg(
                    any(target_os = "linux")
                )]
                {
                    socket.bind_device(Some(name.as_bytes())).map_err(|e| format!("ipv4 bind device [{}] error: {}", name, e))?;
                }
            }
        }


        timeout(Duration::from_secs(3), async move {
            socket.connect(connect_addr).await.map_err(|e| format!("connect error: {}", e))
        }).await.map_err(|e| format!("connect timeout error: {}", e))?.map_err(|e| format!("connect error: {}", e))?;

        match iface_select_type {
            IfaceSelectType::IP => { Ok((None, Some(ipv4addr.clone()), None)) }
            IfaceSelectType::DEVICE => { Ok((Some(name.clone()), None, Some(index))) }
        }
    }
    async fn create_ipv6_connect_fn(iface_select_type: IfaceSelectType, name: String, index: u32, ipv6addr: Ipv6Addr, connect_addr: SocketAddr) -> Result<(Option<String>, Option<Ipv6Addr>, Option<u32>), String> {
        if name.starts_with("utun") {
            return Err("".to_string());
        }
        let socket = TcpSocket::new_v6().map_err(|e| format!("create ipv6 socket error: {}", e))?;

        match iface_select_type {
            IfaceSelectType::IP => {
                socket.bind(SocketAddr::from(SocketAddrV6::new(ipv6addr.clone(), 0, 0, 0))).map_err(|e| format!("ipv6 bind socket error: {}", e))?;
            }
            IfaceSelectType::DEVICE => {
                #[cfg(
                    any(target_os = "linux")
                )]
                {
                    socket.bind_device(Some(name.as_bytes())).map_err(|e| format!("ipv6 bind device [{}] error: {}", name, e))?;
                }
            }
        }

        timeout(Duration::from_secs(3), async move {
            socket.connect(connect_addr).await.map_err(|e| format!("connect error: {}", e))
        }).await.map_err(|e| format!("connect timeout error: {}", e))?.map_err(|e| format!("connect error: {}", e))?;

        match iface_select_type {
            IfaceSelectType::IP => { Ok((None, Some(ipv6addr.clone()), None)) }
            IfaceSelectType::DEVICE => { Ok((Some(name.clone()), None, Some(index))) }
        }
    }
}