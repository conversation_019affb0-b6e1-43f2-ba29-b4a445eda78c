use openssl::symm::{decrypt, encrypt, Cipher};

use crate::tunnel::tunnel_package::TunnelPackage;

/// 加密
pub fn encrypt_package(pwd: &[u8], data: &[u8]) -> Result<Vec<u8>, String> {
    let cipher = Cipher::aes_256_ecb();
    let final_result = match encrypt(cipher, pwd, None, data) {
        Ok(vec) => { vec }
        Err(e) => { return Err(e.to_string()); }
    };

    Ok(final_result)
}

/// 解密
pub fn decrypt_package(buffer_tmp: &[u8], password_md5_byte: &[u8]) -> Result<Option<TunnelPackage>, String> {
    // 解密
    let cipher = Cipher::aes_256_ecb();
    let mut result = match decrypt(cipher, password_md5_byte, None, buffer_tmp) {
        Ok(vec) => { vec }
        Err(e) => { return Err(e.to_string()); }
    };

    // 转成结构体
    match TunnelPackage::from_byte_array(&mut result) {
        Ok(tunnel_package) => {
            if tunnel_package.is_none() {
                return Err("decode package error".to_string());
            }
            Ok(tunnel_package)
        }
        Err(e) => {
            Err(e.to_string())
        }
    }
}

mod test {
    use crate::tunnel::package_encryption::{decrypt_package, encrypt_package};
    use crate::tunnel::tunnel_package::{PackageCmd, PackageProtocol, TunnelPackage};

    #[test]
    fn test() {
        let md5_pwd = md5::compute("123".as_bytes());
        let pwd = format!("{:x}", md5_pwd);

        let mut package = TunnelPackage::new(
            PackageCmd::TData,
            PackageProtocol::TCP,
            Some("source_addr".to_string()),
            Some("target_addr".to_string()),
            Some(vec![1, 2, 3]),
        );

        let mut vec1 = vec![];
        package.to_byte_array(&mut vec1);

        let mut vec2 = encrypt_package(pwd.as_bytes(), &vec1).unwrap();

        let option = decrypt_package(&mut vec2, pwd.as_bytes()).unwrap();
        println!("{:?}", option);
    }
}