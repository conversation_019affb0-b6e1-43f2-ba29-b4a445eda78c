use serde_derive::{Deserialize, Serialize};

const MAX_ADDRESS_LEN: usize = 1024;
const MAX_DATA_LEN: usize = 1024 * 1024;
const HEADER_MAGIC: [u8; 2] = [0x0f, 0x2f];

/// 隧道数据包
#[derive(Debug, PartialEq, Clone)]
pub struct TunnelPackage {
    pub cmd: PackageCmd,
    pub protocol: PackageProtocol,
    pub source_address: Option<String>,
    pub target_address: Option<String>,
    pub data: Option<Vec<u8>>,
}

#[derive(Debug, Copy, Clone, PartialEq, Serialize, Deserialize)]
pub enum PackageProtocol {
    TCP = 0x01,
    UDP = 0x02,
    NativeUdp = 0x03,
    NONE = 0xf0,
}

impl PackageProtocol {
    const fn from_protocol(protocol: u8) -> PackageProtocol {
        match protocol {
            0x01 => PackageProtocol::TCP,
            0x02 => PackageProtocol::UDP,
            0x03 => PackageProtocol::NativeUdp,
            _ => PackageProtocol::NONE,
        }
    }

    const fn as_byte(&self) -> u8 {
        *self as u8
    }
}

#[derive(Debug, Copy, Clone, PartialEq)]
pub enum PackageCmd {
    Login = 0x01,
    NewConnect = 0x03,
    CloseConnect = 0x04,
    TData = 0x05,
    PING = 0x06,
    CNewConnect = 0x07,
    CCloseConnect = 0x08,
    CTdata = 0x09,
    LoginSuccess = 0x41,
    LoginFail = 0x42,
    ProtocolError = 0x43,
    PONG = 0x44,
    NONE = 0xf0,
}

impl PackageCmd {
    const fn from_cmd(cmd: u8) -> PackageCmd {
        match cmd {
            0x01 => PackageCmd::Login,
            0x03 => PackageCmd::NewConnect,
            0x04 => PackageCmd::CloseConnect,
            0x05 => PackageCmd::TData,
            0x06 => PackageCmd::PING,
            0x07 => PackageCmd::CNewConnect,
            0x08 => PackageCmd::CCloseConnect,
            0x09 => PackageCmd::CTdata,
            0x41 => PackageCmd::LoginSuccess,
            0x42 => PackageCmd::LoginFail,
            0x43 => PackageCmd::ProtocolError,
            0x44 => PackageCmd::PONG,
            _ => PackageCmd::NONE,
        }
    }

    const fn as_byte(&self) -> u8 {
        *self as u8
    }
}

impl TunnelPackage {
    pub fn new(
        cmd: PackageCmd,
        protocol: PackageProtocol,
        source_address: Option<String>,
        target_address: Option<String>,
        data: Option<Vec<u8>>,
    ) -> TunnelPackage {
        TunnelPackage {
            cmd,
            protocol,
            source_address,
            target_address,
            data,
        }
    }
}

impl TunnelPackage {
    pub fn to_byte_array(&self, vec: &mut Vec<u8>) {
        // 预分配容量以避免多次重新分配
        vec.reserve(12); // 基础头部长度

        vec.extend_from_slice(&[HEADER_MAGIC[0], HEADER_MAGIC[1], self.cmd.as_byte(), self.protocol.as_byte()]);

        if let Some(addr) = &self.source_address {
            vec.extend_from_slice(&(addr.len() as u32).to_le_bytes());
            vec.extend_from_slice(addr.as_bytes());
        } else {
            vec.extend_from_slice(&[0u8; 4]);
        }

        if let Some(addr) = &self.target_address {
            vec.extend_from_slice(&(addr.len() as u32).to_le_bytes());
            vec.extend_from_slice(addr.as_bytes());
        } else {
            vec.extend_from_slice(&[0u8; 4]);
        }

        if let Some(data) = &self.data {
            vec.extend_from_slice(&(data.len() as u32).to_le_bytes());
            vec.extend_from_slice(data);
        } else {
            vec.extend_from_slice(&[0u8; 4]);
        }
    }

    pub fn from_byte_array(temp_buffer: &mut Vec<u8>) -> Result<Option<TunnelPackage>, String> {
        const BASE_LEN: usize = 4;
        const HEADER_SIZE: usize = BASE_LEN + 4; // 基础头部(4) + 第一个长度字段(4)

        if temp_buffer.len() < HEADER_SIZE {
            return Ok(None);
        }

        // 使用 slice pattern 简化头部检查
        if let [0x0f, 0x2f, cmd_byte, protocol_byte, rest @ ..] = temp_buffer.as_slice() {
            let cmd = PackageCmd::from_cmd(*cmd_byte);
            let protocol = PackageProtocol::from_protocol(*protocol_byte);

            let mut index = BASE_LEN;

            // 读取 source_address
            let source_address_len = u32::from_le_bytes([
                rest[0], rest[1], rest[2], rest[3]
            ]) as usize;

            // 检查长度限制
            if source_address_len > MAX_ADDRESS_LEN {
                return Err("Source address too long".to_string());
            }

            index += 4;
            if temp_buffer.len() < (index + source_address_len + 4) {
                return Ok(None);
            }

            let source_address = if source_address_len == 0 {
                None
            } else {
                match String::from_utf8(temp_buffer[index..index + source_address_len].to_vec()) {
                    Ok(addr) => Some(addr),
                    Err(_) => return Err("Invalid source address encoding".to_string()),
                }
            };
            index += source_address_len;

            // 读取 target_address
            let target_address_len = u32::from_le_bytes([
                temp_buffer[index],
                temp_buffer[index + 1],
                temp_buffer[index + 2],
                temp_buffer[index + 3],
            ]) as usize;

            if target_address_len > MAX_ADDRESS_LEN {
                return Err("Target address too long".to_string());
            }

            index += 4;
            if temp_buffer.len() < (index + target_address_len + 4) {
                return Ok(None);
            }

            let target_address = if target_address_len == 0 {
                None
            } else {
                match String::from_utf8(temp_buffer[index..index + target_address_len].to_vec()) {
                    Ok(addr) => Some(addr),
                    Err(_) => return Err("Invalid target address encoding".to_string()),
                }
            };
            index += target_address_len;

            // 读取数据
            let data_len = u32::from_le_bytes([
                temp_buffer[index],
                temp_buffer[index + 1],
                temp_buffer[index + 2],
                temp_buffer[index + 3],
            ]) as usize;

            if data_len > MAX_DATA_LEN {
                return Err("Data too long".to_string());
            }

            index += 4;
            if temp_buffer.len() < (index + data_len) {
                return Ok(None);
            }

            let data = if data_len == 0 {
                None
            } else {
                Some(temp_buffer[index..index + data_len].to_vec())
            };

            let total_len = index + data_len;

            // 创建包
            let tunnel_package = TunnelPackage {
                cmd,
                protocol,
                source_address,
                target_address,
                data,
            };

            // 移除已处理的数据
            temp_buffer.drain(..total_len);

            // 优化内存使用
            if temp_buffer.capacity() > temp_buffer.len() * 2 {
                temp_buffer.shrink_to_fit();
            }

            Ok(Some(tunnel_package))
        } else {
            Err("Invalid header".to_string())
        }
    }
}
