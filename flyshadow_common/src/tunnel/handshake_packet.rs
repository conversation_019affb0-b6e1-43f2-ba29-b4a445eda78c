pub struct HandshakePacket {
    data: Vec<u8>,
}
impl HandshakePacket {
    /// 是否包含握手包
    pub fn contain_handshake_packet(data: &mut Vec<u8>) -> Option<HandshakePacket> {
        if data.len() >= 5 && data[0..4] == [0x0A, 0, 0, 0] {
            let data: Vec<u8> = data.drain(0..5).collect();
            Some(HandshakePacket { data })
        } else {
            None
        }
    }

    /// 创建握手包
    /// 第五位是功能位
    pub fn create_handshake_packet() -> Vec<u8> {
        return vec![0x0A, 0, 0, 0, 0];
    }
}
