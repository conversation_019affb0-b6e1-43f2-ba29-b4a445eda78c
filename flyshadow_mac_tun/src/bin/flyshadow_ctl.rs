use anyhow::Result;
use clap::{Parser, Subcommand};
use flyshadow_mac_tun::command_handler::{Command, CommandResponse};
use std::io::{<PERSON><PERSON><PERSON><PERSON>, BufReader, Write};
use std::os::unix::net::UnixStream;

#[derive(Parser)]
#[command(name = "flyshadow_ctl")]
#[command(about = "Control utility for flyshadow_mac_tun")]
struct Cli {
    /// Socket path to connect to
    #[arg(short, long, default_value = "/tmp/flyshadow_mac_tun_cmd.sock")]
    socket: String,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Shutdown the application gracefully
    Shutdown,
    /// Get application status
    Status,
    /// Reload configuration
    Reload,
    /// Get statistics
    Stats,
}

fn main() -> Result<()> {
    let cli = Cli::parse();

    let command = match cli.command {
        Commands::Shutdown => Command::Shutdown,
        Commands::Status => Command::Status,
        Commands::Reload => Command::Reload,
        Commands::Stats => Command::Stats,
    };

    send_command(&cli.socket, command)?;
    Ok(())
}

fn send_command(socket_path: &str, command: Command) -> Result<()> {
    let mut stream = UnixStream::connect(socket_path)?;
    
    // Send command
    let command_json = serde_json::to_string(&command)?;
    stream.write_all(command_json.as_bytes())?;
    stream.write_all(b"\n")?;
    stream.flush()?;

    // Read response
    let mut reader = BufReader::new(&stream);
    let mut response_line = String::new();
    reader.read_line(&mut response_line)?;

    let response: CommandResponse = serde_json::from_str(&response_line)?;
    
    match response {
        CommandResponse::Success(msg) => {
            println!("✓ {}", msg);
        }
        CommandResponse::Error(err) => {
            eprintln!("✗ Error: {}", err);
            std::process::exit(1);
        }
        CommandResponse::Status { running, uptime, connections } => {
            println!("Status:");
            println!("  Running: {}", running);
            println!("  Uptime: {} seconds", uptime);
            println!("  Connections: {}", connections);
        }
        CommandResponse::Stats { bytes_sent, bytes_received, packets_sent, packets_received } => {
            println!("Statistics:");
            println!("  Bytes sent: {}", bytes_sent);
            println!("  Bytes received: {}", bytes_received);
            println!("  Packets sent: {}", packets_sent);
            println!("  Packets received: {}", packets_received);
        }
    }

    Ok(())
}
