mod config;
mod tun_device;
mod network;
mod socket;

use config::Config;
use network::{get_default_gateway, NetworkManager};
use socket::SocketManager;
use tun_device::{TunDevice, TunDeviceManager};

use anyhow::Result;
use log::{debug, error, info};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::spawn;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();
    info!("Starting flyshadow_mac_tun");

    // Load configuration
    let config = Config::load()?;
    info!("Configuration loaded successfully");

    // Get default gateway
    let default_gateway = get_default_gateway()
        .ok_or_else(|| anyhow::anyhow!("No default gateway found"))?;
    info!("Default gateway: {}", default_gateway);

    // Create TUN device
    let tun_manager = TunDeviceManager::new(config.tun.clone());
    let device = tun_manager.create_device().await?;
    let tun_device = TunDevice::new(device);
    let (mut device_reader, mut device_writer) = tun_device.split();

    // Setup network routing
    let network_manager = NetworkManager::new(config.network.clone());
    network_manager.setup_routing(&default_gateway)?;

    // Connect to socket
    let socket_manager = SocketManager::new(config.socket.clone());
    let socket_connection = socket_manager.connect().await?;
    let (mut socket_reader, mut socket_writer) = socket_connection.split();

    // Spawn task to handle device -> socket communication
    spawn(async move {
        if let Err(e) = handle_device_to_socket(device_reader, socket_writer).await {
            error!("Device to socket handler failed: {}", e);
        }
    });

    info!("Successfully connected to server, starting packet forwarding");

    // Handle socket -> device communication in main thread
    if let Err(e) = handle_socket_to_device(socket_reader, device_writer).await {
        error!("Socket to device handler failed: {}", e);
    }

    Ok(())
}

/// Handle packets from TUN device to socket
async fn handle_device_to_socket(
    mut device_reader: impl AsyncReadExt + Unpin,
    mut socket_writer: socket::SocketWriter,
) -> Result<()> {
    let mut buf = vec![0u8; 8096]; // Use a reasonable buffer size

    loop {
        match device_reader.read(&mut buf).await {
            Ok(0) => {
                info!("Device reader reached EOF");
                break;
            }
            Ok(n) => {
                debug!("Read {} bytes from device", n);
                let packet = buf[..n].to_vec();

                // Send to socket
                if let Err(e) = socket_writer.write_packet(&packet).await {
                    error!("Failed to write packet to socket: {}", e);
                    break;
                }
            }
            Err(e) => {
                error!("Failed to read from device: {}", e);
                break;
            }
        }
    }

    Ok(())
}

/// Handle packets from socket to TUN device
async fn handle_socket_to_device(
    mut socket_reader: socket::SocketReader,
    mut device_writer: impl AsyncWriteExt + Unpin,
) -> Result<()> {
    loop {
        // Read packet from socket
        let packet = match socket_reader.read_packet().await {
            Ok(packet) => packet,
            Err(e) => {
                error!("Failed to read packet from socket: {}", e);
                break;
            }
        };

        debug!("Read {} bytes from socket", packet.len());

        // Write to device
        if let Err(e) = device_writer.write_all(&packet).await {
            error!("Failed to write packet to device: {}", e);
            break;
        }
    }

    Ok(())
}


