use crate::config::SocketConfig;
use anyhow::Result;
use log::{debug, error, info};
use tokio::io::{AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};
use tokio::net::UnixStream;

/// Socket communication manager
pub struct SocketManager {
    config: SocketConfig,
}

impl SocketManager {
    /// Create a new socket manager
    pub fn new(config: SocketConfig) -> Self {
        Self { config }
    }

    /// Connect to the Unix socket
    pub async fn connect(&self) -> Result<SocketConnection> {
        info!("Connecting to socket: {}", self.config.path);

        let stream = UnixStream::connect(&self.config.path).await
            .map_err(|e| anyhow::anyhow!("Failed to connect to socket {}: {}", self.config.path, e))?;

        info!("Successfully connected to socket");

        Ok(SocketConnection::new(stream, self.config.buffer_size))
    }
}

/// Socket connection wrapper
pub struct SocketConnection {
    stream: UnixStream,
    buffer_size: usize,
}

impl SocketConnection {
    /// Create a new socket connection
    pub fn new(stream: UnixStream, buffer_size: usize) -> Self {
        Self { stream, buffer_size }
    }

    /// Split the connection into reader and writer
    pub fn split(self) -> (SocketReader, SocketWriter) {
        let (read_half, write_half) = tokio::io::split(self.stream);
        (
            SocketReader::new(read_half, self.buffer_size),
            SocketWriter::new(write_half),
        )
    }
}

/// Socket reader for receiving packets
pub struct SocketReader {
    reader: tokio::io::ReadHalf<UnixStream>,
    buffer_size: usize,
}

impl SocketReader {
    /// Create a new socket reader
    pub fn new(reader: tokio::io::ReadHalf<UnixStream>, buffer_size: usize) -> Self {
        Self { reader, buffer_size }
    }

    /// Read a packet with length header
    pub async fn read_packet(&mut self) -> Result<Vec<u8>> {
        // Read 4-byte length header
        let packet_len = self.reader.read_u32().await
            .map_err(|e| anyhow::anyhow!("Failed to read packet length header: {}", e))? as usize;

        debug!("Reading packet of {} bytes", packet_len);

        if packet_len > self.buffer_size * 10 {
            return Err(anyhow::anyhow!("Packet size {} exceeds maximum allowed size", packet_len));
        }

        // Read packet data
        let mut packet_buf = vec![0u8; packet_len];
        self.reader.read_exact(&mut packet_buf).await
            .map_err(|e| anyhow::anyhow!("Failed to read packet data: {}", e))?;

        debug!("Successfully read packet of {} bytes", packet_len);
        Ok(packet_buf)
    }
}

/// Socket writer for sending packets
pub struct SocketWriter {
    writer: tokio::io::WriteHalf<UnixStream>,
}

impl SocketWriter {
    /// Create a new socket writer
    pub fn new(writer: tokio::io::WriteHalf<UnixStream>) -> Self {
        Self { writer }
    }

    /// Write a packet with length header
    pub async fn write_packet(&mut self, data: &[u8]) -> Result<()> {
        debug!("Writing packet of {} bytes", data.len());

        // Write length header
        self.writer.write_u32(data.len() as u32).await
            .map_err(|e| anyhow::anyhow!("Failed to write packet length header: {}", e))?;

        // Write packet data
        self.writer.write_all(data).await
            .map_err(|e| anyhow::anyhow!("Failed to write packet data: {}", e))?;

        debug!("Successfully wrote packet of {} bytes", data.len());
        Ok(())
    }
}

/// Socket communication errors
#[derive(Debug, thiserror::Error)]
pub enum SocketError {
    #[error("Connection failed: {0}")]
    ConnectionFailed(String),
    #[error("Read failed: {0}")]
    ReadFailed(String),
    #[error("Write failed: {0}")]
    WriteFailed(String),
    #[error("Invalid packet size: {0}")]
    InvalidPacketSize(usize),
}
