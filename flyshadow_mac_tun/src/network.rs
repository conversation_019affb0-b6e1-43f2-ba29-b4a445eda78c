use crate::config::NetworkConfig;
use anyhow::Result;
use log::{error, info, warn};
use std::fs;
use std::process::Command;

/// Network routing manager
pub struct NetworkManager {
    config: NetworkConfig,
}

impl NetworkManager {
    /// Create a new network manager
    pub fn new(config: NetworkConfig) -> Self {
        Self { config }
    }

    /// Setup all network routing and DNS configuration
    pub fn setup_routing(&self, real_gateway: &str) -> Result<()> {
        info!("Setting up network routing with gateway: {}", real_gateway);

        // Setup TUN routes
        self.setup_tun_routes()?;

        // Setup local routes
        self.setup_local_routes(real_gateway)?;

        // Setup DNS
        self.setup_dns_resolver()?;

        // Flush DNS cache
        self.flush_dns_cache()?;

        info!("Network routing setup completed successfully");
        Ok(())
    }

    /// Setup routes that go through the TUN interface
    fn setup_tun_routes(&self) -> Result<()> {
        info!("Setting up TUN routes");

        // Add TUN network route
        self.add_route("10.172.0.0/16", &self.config.dns_server.to_string(), true)?;

        // Add global routes through TUN
        for route in &self.config.tun_routes {
            self.add_route(route, &self.config.dns_server.to_string(), true)?;
        }

        Ok(())
    }

    /// Setup routes that go through the real gateway
    fn setup_local_routes(&self, real_gateway: &str) -> Result<()> {
        info!("Setting up local routes via gateway: {}", real_gateway);

        for route in &self.config.local_routes {
            self.add_route(route, real_gateway, false)?;
        }

        Ok(())
    }

    /// Add a single route entry
    fn add_route(&self, dest: &str, via: &str, is_interface: bool) -> Result<()> {
        let args = ["-n", "add", "-net", dest, via];

        let status = Command::new("route")
            .args(&args)
            .status()
            .map_err(|e| anyhow::anyhow!("Failed to execute route command: {}", e))?;

        if status.success() {
            info!(
                "Added route to {} via {} (interface: {})",
                dest, via, is_interface
            );
        } else {
            return Err(anyhow::anyhow!(
                "Failed to add route to {} via {} (interface: {})",
                dest, via, is_interface
            ));
        }

        Ok(())
    }

    /// Setup DNS resolver configuration
    fn setup_dns_resolver(&self) -> Result<()> {
        const RESOLV_CONF_PATH: &str = "/etc/resolv.conf";
        let target_nameserver = format!("nameserver {}", self.config.dns_server);

        info!("Setting up DNS resolver: {}", target_nameserver);

        // Read existing resolv.conf content
        let content = match fs::read_to_string(RESOLV_CONF_PATH) {
            Ok(content) => content,
            Err(e) => {
                warn!("Failed to read {}: {}", RESOLV_CONF_PATH, e);
                String::new()
            }
        };

        let lines: Vec<&str> = content.lines().collect();

        // Check if first line is already our target nameserver
        if let Some(first_line) = lines.first() {
            if first_line.trim() == target_nameserver {
                info!("DNS resolver already configured correctly");
                return Ok(());
            }
        }

        // Build new content with our nameserver first
        let mut new_content = String::new();
        new_content.push_str(&target_nameserver);
        new_content.push('\n');

        // Add existing content (excluding our nameserver if it exists elsewhere)
        for line in &lines {
            if line.trim() != target_nameserver {
                new_content.push_str(line);
                new_content.push('\n');
            }
        }

        // Write new content
        fs::write(RESOLV_CONF_PATH, new_content)
            .map_err(|e| anyhow::anyhow!("Failed to write to {}: {}", RESOLV_CONF_PATH, e))?;

        info!("Successfully updated {} with nameserver {}", RESOLV_CONF_PATH, self.config.dns_server);
        Ok(())
    }

    /// Flush DNS cache
    pub fn flush_dns_cache(&self) -> Result<()> {
        info!("Flushing DNS cache");

        let status = Command::new("dscacheutil")
            .args(&["-flushcache"])
            .status()
            .map_err(|e| anyhow::anyhow!("Failed to execute dscacheutil command: {}", e))?;

        if status.success() {
            info!("DNS cache flushed successfully");
        } else {
            return Err(anyhow::anyhow!("dscacheutil flushcache command failed"));
        }

        Ok(())
    }

    /// Cleanup all network routing and DNS configuration
    pub fn cleanup_routing(&self, real_gateway: &str) -> Result<()> {
        info!("Cleaning up network routing");

        // Remove TUN routes
        self.cleanup_tun_routes()?;

        // Remove local routes
        self.cleanup_local_routes(real_gateway)?;

        // Restore original DNS configuration (optional)
        self.restore_dns_resolver()?;

        info!("Network routing cleanup completed successfully");
        Ok(())
    }

    /// Remove routes that go through the TUN interface
    fn cleanup_tun_routes(&self) -> Result<()> {
        info!("Cleaning up TUN routes");

        // Remove TUN network route
        self.remove_route("10.172.0.0/16")?;

        // Remove global routes through TUN
        for route in &self.config.tun_routes {
            self.remove_route(route)?;
        }

        Ok(())
    }

    /// Remove routes that go through the real gateway
    fn cleanup_local_routes(&self, real_gateway: &str) -> Result<()> {
        info!("Cleaning up local routes via gateway: {}", real_gateway);

        for route in &self.config.local_routes {
            self.remove_route(route)?;
        }

        Ok(())
    }

    /// Remove a single route entry
    fn remove_route(&self, dest: &str) -> Result<()> {
        let args = ["-n", "delete", "-net", dest];

        let status = Command::new("route")
            .args(&args)
            .status()
            .map_err(|e| anyhow::anyhow!("Failed to execute route command: {}", e))?;

        if status.success() {
            info!("Removed route to {}", dest);
        } else {
            warn!("Failed to remove route to {} (may not exist)", dest);
        }

        Ok(())
    }

    /// Restore original DNS resolver configuration
    fn restore_dns_resolver(&self) -> Result<()> {
        info!("Restoring original DNS resolver configuration");

        // This is a simplified restoration - in a production environment,
        // you might want to backup and restore the original resolv.conf
        const RESOLV_CONF_PATH: &str = "/etc/resolv.conf";
        let target_nameserver = format!("nameserver {}", self.config.dns_server);

        // Read current content
        let content = match fs::read_to_string(RESOLV_CONF_PATH) {
            Ok(content) => content,
            Err(e) => {
                warn!("Failed to read {}: {}", RESOLV_CONF_PATH, e);
                return Ok(());
            }
        };

        // Remove our nameserver from the file
        let lines: Vec<&str> = content.lines().collect();
        let mut new_content = String::new();

        for line in &lines {
            if line.trim() != target_nameserver {
                new_content.push_str(line);
                new_content.push('\n');
            }
        }

        // Write back the content without our nameserver
        if let Err(e) = fs::write(RESOLV_CONF_PATH, new_content) {
            warn!("Failed to restore {}: {}", RESOLV_CONF_PATH, e);
        } else {
            info!("Successfully restored original DNS configuration");
        }

        Ok(())
    }
}

/// Get the default gateway from the system
pub fn get_default_gateway() -> Option<String> {
    let output = Command::new("route")
        .args(&["-n", "get", "default"])
        .output()
        .ok()?;

    let stdout = String::from_utf8_lossy(&output.stdout);
    for line in stdout.lines() {
        if line.trim_start().starts_with("gateway:") {
            return Some(line.trim().split_whitespace().nth(1)?.to_string());
        }
    }
    None
}
