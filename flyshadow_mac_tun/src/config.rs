use serde::{Deserialize, Serialize};
use std::net::Ipv4Addr;

/// Application configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub tun: TunConfig,
    pub encryption: EncryptionConfig,
    pub network: NetworkConfig,
    pub socket: SocketConfig,
}

/// TUN device configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TunConfig {
    pub address: Ipv4Addr,
    pub netmask: Ipv4Addr,
    pub destination: Ipv4Addr,
}

/// Encryption configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptionConfig {
    pub key: [u8; 32],
    pub nonce: [u8; 12],
}

/// Network configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct NetworkConfig {
    pub dns_server: Ipv4Addr,
    pub tun_routes: Vec<String>,
    pub local_routes: Vec<String>,
}

/// Socket configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SocketConfig {
    pub path: String,
    pub buffer_size: usize,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            tun: TunConfig {
                address: Ipv4Addr::new(10, 172, 50, 2),
                netmask: Ipv4Addr::new(255, 255, 255, 0),
                destination: Ipv4Addr::new(10, 172, 50, 1),
            },
            encryption: EncryptionConfig {
                key: *b"01234567890123456789012345678901",
                nonce: *b"flyshadow123",
            },
            network: NetworkConfig {
                dns_server: Ipv4Addr::new(10, 172, 50, 2),
                tun_routes: vec![
                    "*******/8".to_string(),
                    "*******/7".to_string(),
                    "*******/6".to_string(),
                    "*******/5".to_string(),
                    "********/4".to_string(),
                    "3*******/3".to_string(),
                    "6*******/2".to_string(),
                    "12*******/1".to_string(),
                ],
                local_routes: vec![
                    "*********/8".to_string(),      // 本地回环
                    "10.0.0.0/8".to_string(),       // 私有网段 A
                    "***********/16".to_string(),   // 私有网段 C
                    "**********/12".to_string(),    // 私有网段 B
                ],
            },
            socket: SocketConfig {
                path: "/tmp/flyshadow_mac_tun.sock".to_string(),
                buffer_size: 1024 * 1024,
            },
        }
    }
}

impl Config {
    /// Load configuration from environment variables or use defaults
    pub fn load() -> anyhow::Result<Self> {
        // For now, just return default config
        // In the future, this could load from file or environment variables
        Ok(Self::default())
    }
}
