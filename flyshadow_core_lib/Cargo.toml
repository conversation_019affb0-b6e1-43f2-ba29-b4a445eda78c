[package]
name = "flyshadow_core_lib"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "flyshadow_core_lib"
crate-type = ["cdylib", "staticlib"]

[dependencies]
flyshadow_common = { path = "../flyshadow_common" }
tunnel = { path = "../tunnel" }
tokio = { version = "1.38.0", features = ["full"] }
trust-dns-resolver = "0.23.2"

android_logger = "0.13.3"
log = "0.4.22"
simple_logger = "4.3.3"


[target.'cfg(target_os = "windows")'.dependencies]
wintun = "0.5"
winroute = "0.2.0"

[target.'cfg(target_os = "ios")'.dependencies]
oslog = "0.2.0"


