use std::ffi::{CStr, CString};
use std::mem::forget;
use std::os::raw::c_char;
use std::sync::Arc;

use tokio::runtime::Runtime;

use tunnel::context::context::TunnelContext;
use tunnel::proxy::proxy::Proxy;

/// 新建代理对象
#[unsafe(no_mangle)]
pub extern "C" fn new_proxy(context_ptr: i64) -> i64 {
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    let proxy = Proxy::new(context_clone);

    forget(tc);
    Box::into_raw(Box::new(proxy)) as i64
}


/// 启动代理
#[unsafe(no_mangle)]
pub extern "C" fn start_proxy(rt: i64, p: i64, port: u16) -> *mut c_char {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let mut p = unsafe { Box::from_raw(p as *mut Proxy) };

    let result = rt.block_on(async move {
        let result = match p.start(port).await {
            Ok(_) => { "".to_string() }
            Err(e) => {
                e.to_string()
            }
        };
        forget(p);
        result
    });
    forget(rt);
    CString::new(result).unwrap().into_raw()
}


/// 启动代理 指定IP
#[unsafe(no_mangle)]
pub extern "C" fn start_proxy_ip(rt: i64, p: i64, ip: *const c_char, port: u16) -> *mut c_char {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let mut p = unsafe { Box::from_raw(p as *mut Proxy) };

    let domain = unsafe { CStr::from_ptr(ip).to_string_lossy() };

    let result = rt.block_on(async move {
        let result = match p.start_ip(domain.to_string(), port).await {
            Ok(_) => { "".to_string() }
            Err(e) => {
                e.to_string()
            }
        };
        forget(p);
        result
    });
    forget(rt);
    CString::new(result).unwrap().into_raw()
}

/// 停止代理
#[unsafe(no_mangle)]
pub extern "C" fn stop_proxy(rt: i64, p: i64) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let mut p = unsafe { Box::from_raw(p as *mut Proxy) };
    rt.block_on(async move {
        p.stop().await;
        forget(p);
    });
    forget(rt);
}