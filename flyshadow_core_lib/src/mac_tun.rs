use flyshadow_common::interface::interface_selector::{IfaceSelectType, InterfaceSelector};
use log::{debug, error, info, warn};
use std::ffi::CString;
use std::fmt;
use std::io::Error as IoError;
use std::mem::forget;
use std::os::raw::c_char;
use std::path::Path;
use std::process::Command;
use std::sync::Arc;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio::net::UnixListener;
use tokio::runtime::Runtime;
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::task::JoinHandle;
use tunnel::context::context::TunnelContext;
use tunnel::tun::tun::Tun;

// Configuration constants
const SOCKET_PATH: &str = "/tmp/flyshadow_mac_tun.sock";

// Custom error types
#[derive(Debug)]
pub enum TunServerError {
    Io(IoError),
    Runtime(String),
    InterfaceSelection(String),
    SocketBinding(String),
    TaskSpawn(String),
}

impl fmt::Display for TunServerError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            TunServerError::Io(err) => write!(f, "IO error: {}", err),
            TunServerError::Runtime(msg) => write!(f, "Runtime error: {}", msg),
            TunServerError::InterfaceSelection(msg) => write!(f, "Interface selection error: {}", msg),
            TunServerError::SocketBinding(msg) => write!(f, "Socket binding error: {}", msg),
            TunServerError::TaskSpawn(msg) => write!(f, "Task spawn error: {}", msg),
        }
    }
}

impl std::error::Error for TunServerError {}

impl From<IoError> for TunServerError {
    fn from(err: IoError) -> Self {
        TunServerError::Io(err)
    }
}

type Result<T> = std::result::Result<T, TunServerError>;

// Task management module
mod task_manager {
    use log::{error, info};
    use std::sync::Arc;
    use tokio::sync::RwLock;
    use tokio::task::JoinHandle;

    pub struct TaskManager {
        tasks: Arc<RwLock<Vec<JoinHandle<()>>>>,
    }

    impl TaskManager {
        pub fn new() -> Self {
            Self {
                tasks: Arc::new(RwLock::new(Vec::new())),
            }
        }

        pub async fn spawn_task<F>(&self, task: F)
        where
            F: std::future::Future<Output=()> + Send + 'static,
        {
            let handle = tokio::spawn(task);
            self.tasks.write().await.push(handle);
        }

        pub async fn add_handle(&self, handle: JoinHandle<()>) {
            self.tasks.write().await.push(handle);
        }

        pub async fn shutdown_all(&self) {
            let mut tasks = self.tasks.write().await;
            info!("Shutting down {} tasks", tasks.len());

            for task in tasks.iter() {
                task.abort();
            }

            tasks.clear();
            info!("All tasks shut down successfully");
        }

        pub async fn task_count(&self) -> usize {
            self.tasks.read().await.len()
        }
    }

    impl Default for TaskManager {
        fn default() -> Self {
            Self::new()
        }
    }
}

// Socket server module
mod socket_server {
    use super::*;
    use log::{error, info};

    pub struct SocketServer {
        listener: UnixListener,
        tun: Arc<Tun>,
    }

    impl SocketServer {
        pub async fn new(tun: Arc<Tun>) -> Result<Self> {
            // 清理旧 socket 文件
            if Path::new(SOCKET_PATH).exists() {
                tokio::fs::remove_file(SOCKET_PATH).await?;
            }

            let listener = UnixListener::bind(SOCKET_PATH)?;
            info!("Socket server bound to {}", SOCKET_PATH);

            Ok(Self { listener, tun })
        }

        pub async fn run(&self) {
            loop {
                let (socket, addr) = match self.listener.accept().await {
                    Ok((socket, addr)) => (socket, addr),
                    Err(e) => {
                        error!("Failed to accept connection: {}", e);
                        continue;
                    }
                };

                info!("New client connected: {:?}", addr);
                let (read_half, write_half) = tokio::io::split(socket);

                let tun_read = self.tun.clone();
                let tun_write = self.tun.clone();

                // Spawn write task
                tokio::spawn(Self::handle_write_task(write_half, tun_read));

                // Spawn read task
                tokio::spawn(Self::handle_read_task(read_half, tun_write));
            }
        }

        async fn handle_write_task(
            mut write_half: tokio::io::WriteHalf<tokio::net::UnixStream>,
            tun: Arc<Tun>,
        ) {
            loop {
                let data = tun.get_tun_data().await;

                if let Err(e) = write_half.write_u32(data.len() as u32).await {
                    error!("Failed to write header: {}", e);
                    break;
                }

                if let Err(e) = write_half.write_all(&data).await {
                    error!("Failed to write to client: {}", e);
                    break;
                }
            }
        }

        async fn handle_read_task(
            mut read_half: tokio::io::ReadHalf<tokio::net::UnixStream>,
            tun: Arc<Tun>,
        ) {
            loop {
                // 读取 4 字节长度头
                let packet_len = match read_half.read_u32().await {
                    Ok(packet_len) => packet_len as usize,
                    Err(e) => {
                        error!("Failed to read header: {}", e);
                        break;
                    }
                };

                let mut packet_buf = vec![0u8; packet_len];
                if let Err(e) = read_half.read_exact(&mut packet_buf).await {
                    error!("Failed to read full packet: {}", e);
                    break;
                }

                tun.handle_tun_data(packet_buf).await;
            }
        }
    }
}

/// TUN server environment that manages the lifecycle of a TUN interface server
///
/// This structure encapsulates all the components needed to run a TUN server on macOS,
/// including the Tokio runtime, tunnel context, TUN interface, and task management.
///
/// # Examples
///
/// ```rust,no_run
/// use std::sync::Arc;
/// use tunnel::context::context::TunnelContext;
///
/// let context = Arc::new(TunnelContext::new().await);
/// let env = TunServerEnv::new(context);
///
/// // Start the server
/// env.start_tun_server().expect("Failed to start TUN server");
///
/// // Stop the server when done
/// env.stop_tun_server();
/// ```
pub struct TunServerEnv {
    /// Tokio runtime for async operations
    runtime: Runtime,
    /// Shared tunnel context containing configuration and state
    tunnel_context: Arc<TunnelContext>,
    /// TUN interface for packet processing
    tun: Arc<Tun>,
    /// Task manager for handling async tasks
    task_manager: task_manager::TaskManager,
}

impl TunServerEnv {
    /// Creates a new TUN server environment
    ///
    /// # Arguments
    ///
    /// * `tunnel_context` - Shared tunnel context containing configuration and state
    ///
    /// # Returns
    ///
    /// A new `TunServerEnv` instance ready to start serving
    ///
    /// # Panics
    ///
    /// Panics if the Tokio runtime cannot be created or if TUN initialization fails
    pub fn new(tunnel_context: Arc<TunnelContext>) -> TunServerEnv {
        let runtime = tokio::runtime::Builder::new_multi_thread()
            .enable_all()
            .build()
            .expect("Failed to create Tokio runtime");

        let context = tunnel_context.clone();
        let tun = runtime.block_on(async move { Tun::new(context).await });

        TunServerEnv {
            runtime,
            tunnel_context,
            tun: Arc::new(tun.unwrap()),
            task_manager: task_manager::TaskManager::new(),
        }
    }

    /// Starts the TUN server
    ///
    /// This method performs the following operations:
    /// 1. Sets up network interface selection
    /// 2. Creates and binds a Unix domain socket
    /// 3. Spawns tasks to handle client connections
    /// 4. Manages data flow between clients and the TUN interface
    ///
    /// # Returns
    ///
    /// * `Ok(())` - Server started successfully
    /// * `Err(TunServerError)` - Server failed to start
    ///
    /// # Errors
    ///
    /// This function will return an error if:
    /// * Interface selection fails
    /// * Socket binding fails
    /// * Task spawning fails
    pub fn start_tun_server(&self) -> Result<()> {
        let tunnel_context = self.tunnel_context.clone();
        self.runtime.block_on(async {
            if let Ok(interface_selector) = InterfaceSelector::select_iface(IfaceSelectType::IP, tunnel_context.get_ipv6_enable().await).await {
                tunnel_context.set_local_interface(interface_selector).await;
            }

            // 清理旧 socket 文件
            if Path::new(SOCKET_PATH).exists() {
                tokio::fs::remove_file(SOCKET_PATH).await?;
            }

            let listener = UnixListener::bind(SOCKET_PATH)?;

            let tun = self.tun.clone();

            let task_manager = &self.task_manager;

            task_manager.spawn_task(async move {
                loop {
                    let (socket, addr) = if let Ok((socket, addr)) = listener.accept().await {
                        (socket, addr)
                    } else {
                        return;
                    };
                    let (mut read_half, mut write_half) = tokio::io::split(socket);
                    info!("New client connected: {:?}", addr);

                    let tun = tun.clone();
                    let tun1 = tun.clone();

                    // 创建写任务
                    let write_task = spawn(async move {
                        loop {
                            let data = tun.get_tun_data().await;
                            if let Err(e) = write_half.write_u32(data.len() as u32).await {
                                error!("Failed to write header: {}", e);
                                break;
                            }
                            if let Err(e) = write_half.write_all(&data).await {
                                error!("Failed to write to client: {}", e);
                                break;
                            }
                        }
                    });

                    // 创建读任务
                    let read_task = spawn(async move {
                        loop {
                            // 读取 4 字节长度头
                            let packet_len = match read_half.read_u32().await {
                                Ok(packet_len) => packet_len as usize,
                                Err(e) => {
                                    error!("Failed to read header: {}", e);
                                    break;
                                }
                            };

                            let mut packet_buf = vec![0u8; packet_len];
                            if let Err(e) = read_half.read_exact(&mut packet_buf).await {
                                error!("Failed to read full packet: {}", e);
                                break;
                            }

                            if let Err(e) = tun1.handle_tun_data(packet_buf).await {
                                error!("Failed to handle packet: {}", e);
                                break;
                            }
                        }
                    });

                    // Note: Tasks are automatically managed by tokio::spawn
                    // The write_task and read_task will be cleaned up when the connection closes
                }
            }).await;

            Ok(())
        })
    }

    /// Stops the TUN server and cleans up resources
    ///
    /// This method performs the following cleanup operations:
    /// 1. Shuts down all running tasks
    /// 2. Flushes the DNS cache
    /// 3. Releases system resources
    ///
    /// # Note
    ///
    /// This method blocks until all tasks are properly shut down
    pub fn stop_tun_server(&self) {
        info!("Stopping TUN server");
        self.runtime.block_on(async {
            self.task_manager.shutdown_all().await;
        });
        Self::flush_dns_cache();
        info!("TUN server stopped successfully");
    }


    /// Flushes the macOS DNS cache
    ///
    /// This method executes the `dscacheutil -flushcache` command to clear
    /// the system DNS cache. This is useful when changing network configurations
    /// to ensure DNS resolution uses the new settings.
    ///
    /// # Note
    ///
    /// Errors from the command execution are silently ignored as DNS cache
    /// flushing is not critical for TUN server operation.
    fn flush_dns_cache() {
        debug!("Flushing DNS cache");
        match Command::new("dscacheutil")
            .args(&["-flushcache"])
            .status()
        {
            Ok(status) if status.success() => {
                debug!("DNS cache flushed successfully");
            }
            Ok(status) => {
                warn!("DNS cache flush command failed with status: {}", status);
            }
            Err(e) => {
                warn!("Failed to execute DNS cache flush command: {}", e);
            }
        }
    }
}

// C FFI interface with improved error handling and safety
mod ffi {
    use super::*;
    use std::panic;

    /// Creates a new TUN server environment from a tunnel context pointer
    ///
    /// # Safety
    /// The context_ptr must be a valid pointer to an Arc<TunnelContext>
    /// Returns 0 on failure, otherwise returns a valid pointer to TunServerEnv
    #[unsafe(no_mangle)]
    pub extern "C" fn new_tun_server_env(context_ptr: i64) -> i64 {
        let result = panic::catch_unwind(|| {
            if context_ptr == 0 {
                error!("Invalid context pointer: null pointer");
                return 0;
            }

            let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
            let env = TunServerEnv::new(tc.as_ref().clone());
            forget(tc);

            let env_ptr = Box::into_raw(Box::new(env)) as i64;
            info!("Created TUN server environment: {}", env_ptr);
            env_ptr
        });

        match result {
            Ok(ptr) => ptr,
            Err(_) => {
                error!("Panic occurred in new_tun_server_env");
                0
            }
        }
    }

    /// Starts the TUN server
    ///
    /// # Safety
    /// The env_ptr must be a valid pointer to a TunServerEnv
    /// Returns a C string with error message (empty string on success)
    /// The caller is responsible for freeing the returned string
    #[unsafe(no_mangle)]
    pub extern "C" fn start_tun_server(env_ptr: i64) -> *mut c_char {
        let result = panic::catch_unwind(|| {
            if env_ptr == 0 {
                error!("Invalid environment pointer: null pointer");
                return CString::new("Invalid environment pointer").unwrap().into_raw();
            }

            let env = unsafe { Box::from_raw(env_ptr as *mut TunServerEnv) };

            let result = match env.start_tun_server() {
                Ok(_) => {
                    info!("TUN server started successfully");
                    CString::new("").unwrap().into_raw()
                },
                Err(e) => {
                    error!("Failed to start TUN server: {}", e);
                    CString::new(e.to_string()).unwrap().into_raw()
                },
            };

            forget(env);
            result
        });

        match result {
            Ok(ptr) => ptr,
            Err(_) => {
                error!("Panic occurred in start_tun_server");
                CString::new("Internal error: panic occurred").unwrap().into_raw()
            }
        }
    }

    /// Stops the TUN server
    ///
    /// # Safety
    /// The env_ptr must be a valid pointer to a TunServerEnv
    #[unsafe(no_mangle)]
    pub extern "C" fn stop_tun_server(env_ptr: i64) {
        let result = panic::catch_unwind(|| {
            if env_ptr == 0 {
                error!("Invalid environment pointer: null pointer");
                return;
            }

            let env = unsafe { Box::from_raw(env_ptr as *mut TunServerEnv) };
            info!("Stopping TUN server");
            env.stop_tun_server();
            info!("TUN server stopped successfully");
            forget(env);
        });

        if result.is_err() {
            error!("Panic occurred in stop_tun_server");
        }
    }

    /// Frees a C string returned by other FFI functions
    ///
    /// # Safety
    /// The ptr must be a valid pointer returned by one of the FFI functions
    #[unsafe(no_mangle)]
    pub extern "C" fn free_c_string(ptr: *mut c_char) {
        if !ptr.is_null() {
            unsafe {
                let _ = CString::from_raw(ptr);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;


    #[test]
    fn test_task_manager_creation() {
        let task_manager = task_manager::TaskManager::new();
        // Test that task manager is created successfully
        // We can't easily test the internal state without making fields public
        // but we can test that it doesn't panic on creation
    }

    #[test]
    fn test_error_display() {
        let io_error = std::io::Error::new(std::io::ErrorKind::NotFound, "File not found");
        let tun_error = TunServerError::Io(io_error);

        let error_string = format!("{}", tun_error);
        assert!(error_string.contains("IO error"));
        assert!(error_string.contains("File not found"));
    }

    #[test]
    fn test_error_from_io_error() {
        let io_error = std::io::Error::new(std::io::ErrorKind::PermissionDenied, "Permission denied");
        let tun_error: TunServerError = io_error.into();

        match tun_error {
            TunServerError::Io(err) => {
                assert_eq!(err.kind(), std::io::ErrorKind::PermissionDenied);
            }
            _ => panic!("Expected TunServerError::Io variant"),
        }
    }

    #[test]
    fn test_constants() {
        assert_eq!(SOCKET_PATH, "/tmp/flyshadow_mac_tun.sock");
    }

    #[tokio::test]
    async fn test_task_manager_task_count() {
        let task_manager = task_manager::TaskManager::new();
        assert_eq!(task_manager.task_count().await, 0);

        // Spawn a simple task
        task_manager.spawn_task(async {
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }).await;

        assert_eq!(task_manager.task_count().await, 1);
    }

    #[tokio::test]
    async fn test_task_manager_shutdown() {
        let task_manager = task_manager::TaskManager::new();

        // Spawn a long-running task
        task_manager.spawn_task(async {
            tokio::time::sleep(tokio::time::Duration::from_secs(10)).await;
        }).await;

        assert_eq!(task_manager.task_count().await, 1);

        // Shutdown all tasks
        task_manager.shutdown_all().await;
        assert_eq!(task_manager.task_count().await, 0);
    }
}
