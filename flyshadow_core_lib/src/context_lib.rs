use std::ffi::{CStr, CString};
use std::mem::forget;
use std::os::raw::c_char;
use std::sync::Arc;
use tokio::runtime::{Builder, Runtime};

use tunnel::context::context::TunnelContext;

/// 新建Tokio运行环境
/// 返回运行环境指针
#[unsafe(no_mangle)]
pub extern "C" fn new_runtime() -> i64 {
    // {
    //     use android_logger::Config;
    //     use log::LevelFilter;
    //     android_logger::init_once(
    //         Config::default().with_max_level(LevelFilter::Debug),
    //     );
    // }
    // {
    //     use simple_logger::SimpleLogger;
    //     use log::LevelFilter;
    //     SimpleLogger::new()
    //         .with_level(LevelFilter::Off)
    //         .with_module_level("tunnel", LevelFilter::Debug)
    //         .with_module_level("context", LevelFilter::Debug)
    //         .init()
    //         .unwrap();
    // }
    // {
    //     use log::LevelFilter;
    //     use oslog::OsLogger;
    //     OsLogger::new("com.hkspeedup.FlyShadow")
    //         .level_filter(LevelFilter::Info)
    //         .category_level_filter("Settings", LevelFilter::Trace)
    //         .init()
    //         .unwrap();
    // }

    let runtime = Builder::new_multi_thread().enable_all()
        .build().unwrap();
    Box::into_raw(Box::new(runtime)) as i64
}

/// 新建隧道上下文
/// 返回隧道上下文指针地址
#[unsafe(no_mangle)]
pub extern "C" fn new_tunnel_context(rt: i64) -> i64 {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };

    let result = rt.block_on(async {
        let tunnel_context = Arc::new(TunnelContext::new().await);
        let raw = Box::into_raw(Box::new(tunnel_context));
        raw as i64
    });

    forget(rt);
    result
}


/// 设置域名规则
#[unsafe(no_mangle)]
pub extern "C" fn set_domain_rule_obj(rt: i64, context_ptr: i64, domain: *const c_char, matching: i32, proxy_type: i32, direct_conn_priority: u8) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        let domain = unsafe { CStr::from_ptr(domain).to_string_lossy() };
        context_clone.set_domain_rule_obj(&domain.to_string(), matching, proxy_type, direct_conn_priority).await;
    });

    forget(tc);
    forget(rt);
}


/// 设置域名规则
#[unsafe(no_mangle)]
pub extern "C" fn clear_domain_rule(rt: i64, context_ptr: i64) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone.clear_domain_rule().await;
    });

    forget(tc);
    forget(rt);
}

/// 设置 GEO IP内容
#[unsafe(no_mangle)]
pub extern "C" fn set_geoip(rt: i64, context_ptr: i64, key: *const c_char, input: *const u8, input_size: usize) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    let input_slice: &[u8] = unsafe { std::slice::from_raw_parts(input, input_size) };
    let data = input_slice.to_vec();

    rt.block_on(async {
        let key = unsafe { CStr::from_ptr(key).to_string_lossy() };
        context_clone.set_geoip(key.to_string(), data).await;
    });

    forget(tc);
    forget(rt);
}

/// 关闭连接
#[unsafe(no_mangle)]
pub extern "C" fn close_connect(rt: i64, context_ptr: i64, key: *const c_char) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        let key = unsafe { CStr::from_ptr(key).to_string_lossy() };
        context_clone.close_connect(key.to_string()).await;
    });

    forget(tc);
    forget(rt);
}

/// 设置代理类型
#[unsafe(no_mangle)]
pub extern "C" fn set_proxy_type(rt: i64, context_ptr: i64, type_index: i32) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone.set_proxy_type(type_index).await;
    });

    forget(tc);
    forget(rt);
}

/// 设置UDP代理类型
#[unsafe(no_mangle)]
pub extern "C" fn set_udp_proxy_type(rt: i64, context_ptr: i64, type_index: i32) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone.set_udp_proxy_type(type_index).await;
    });

    forget(tc);
    forget(rt);
}

/// 设置dnsIP缓存的开关
#[unsafe(no_mangle)]
pub extern "C" fn set_fake_ip(rt: i64, context_ptr: i64, enable: bool) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone.set_fake_ip(enable).await;
    });

    forget(tc);
    forget(rt);
}

/// 设置本地网卡IP
#[unsafe(no_mangle)]
pub extern "C" fn set_tun_mode_enable(rt: i64, context_ptr: i64, enable: bool) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone.set_tun_mode_enable(enable).await;
    });

    forget(tc);
    forget(rt);
}

/// 设置IPv6是否启用
#[unsafe(no_mangle)]
pub extern "C" fn set_ipv6_enable(rt: i64, context_ptr: i64, enable: bool) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone.set_ipv6_enable(enable).await;
    });

    forget(tc);
    forget(rt);
}

/// 查询隧道映射信息JSON
#[unsafe(no_mangle)]
pub extern "C" fn get_tunnel_mapper_info_json(rt: i64, context_ptr: i64) -> *mut c_char {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    let result = rt.block_on(async move {
        context_clone.get_tunnel_mapper_info_json().await
    });

    forget(tc);
    forget(rt);

    CString::new(result).unwrap().into_raw()
}


/// 设置原生UDP开关
#[unsafe(no_mangle)]
pub extern "C" fn set_native_udp(rt: i64, context_ptr: i64, enable: bool) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone.set_native_udp(enable).await;
    });

    forget(tc);
    forget(rt);
}

/// 设置直连优先参数
#[unsafe(no_mangle)]
pub extern "C" fn set_direct_conn_priority(rt: i64, context_ptr: i64, enable: bool, timeout: u64) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone.set_direct_conn_priority(enable).await;
        context_clone.set_direct_conn_priority_timeout(timeout).await;
    });

    forget(tc);
    forget(rt);
}


/// 设置使用内置DNS
#[unsafe(no_mangle)]
pub extern "C" fn set_use_build_in_dns(rt: i64, context_ptr: i64, enable: bool) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone.set_use_build_in_dns(enable).await;
    });

    forget(tc);
    forget(rt);
}