use std::ffi::{CStr, CString};
use std::mem::forget;
use std::os::raw::c_char;
use std::sync::Arc;
use tokio::runtime::Runtime;
use tunnel::context::context::TunnelContext;

/// 设置客户端uuid
#[unsafe(no_mangle)]
pub extern "C" fn set_client_uuid(rt: i64, context_ptr: i64, uuid: *const c_char) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        let uuid = unsafe { CStr::from_ptr(uuid).to_string_lossy() };
        context_clone.set_client_uuid(uuid.to_string()).await;
    });

    forget(tc);
    forget(rt);
}

/// 添加端口转发
#[unsafe(no_mangle)]
pub extern "C" fn add_port_forwarding(
    rt: i64,
    context_ptr: i64,
    port: u16,
    target_addr: *const c_char,
    uuid: *const c_char,
) -> *mut c_char {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    let result = rt.block_on(async {
        let uuid = unsafe { CStr::from_ptr(uuid).to_string_lossy() };
        let target_addr = unsafe { CStr::from_ptr(target_addr).to_string_lossy() };
        if let Err(e) = context_clone
            .add_port_forwarding(port, target_addr.to_string(), uuid.to_string())
            .await {
            e
        } else { "".to_string() }
    });

    forget(tc);
    forget(rt);

    CString::new(result).unwrap().into_raw()
}


/// 删除端口转发
#[unsafe(no_mangle)]
pub extern "C" fn remove_port_forwarding(
    rt: i64,
    context_ptr: i64,
    port: u16,
) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone
            .remove_port_forwarding(port)
            .await;
    });

    forget(tc);
    forget(rt);
}


/// 清空端口转发
#[unsafe(no_mangle)]
pub extern "C" fn clear_port_forwarding(
    rt: i64,
    context_ptr: i64,
) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async {
        context_clone
            .clear_port_forwarding()
            .await;
    });

    forget(tc);
    forget(rt);
}
