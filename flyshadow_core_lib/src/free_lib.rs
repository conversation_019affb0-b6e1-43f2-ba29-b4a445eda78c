use std::ffi::CString;
use std::os::raw::c_char;

use tunnel::proxy::proxy::Proxy;

/// 释放字符串指针
#[unsafe(no_mangle)]
pub extern "C" fn free_string(ptr: *mut c_char) {
    unsafe {
        if ptr.is_null() {
            return;
        }
        let _ = CString::from_raw(ptr);
    }
}

/// 释放代理结构体指针
#[unsafe(no_mangle)]
pub extern "C" fn free_proxy(p: i64) {
    let _p = unsafe { Box::from_raw(p as *mut Proxy) };
}

/// 释放从TUN获取的数据
#[unsafe(no_mangle)]
pub extern "C" fn free_tun_data(x: *mut u8) {
    let _ = unsafe { Box::from_raw(x) };
}
