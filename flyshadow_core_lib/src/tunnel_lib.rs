use std::ffi::CStr;
use std::mem::forget;
use std::os::raw::c_char;
use std::sync::Arc;

use tokio::runtime::Runtime;

use tunnel::context::context::TunnelContext;
use tunnel::tunnel::tunnel::Tunnel;

/// 连接隧道
/// 返回错误的字符串
#[unsafe(no_mangle)]
pub extern "C" fn connect_tunnel(rt: i64, context_ptr: i64, host: *const c_char, port: u32, password: *const c_char) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };

    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async move {
        let host = unsafe { CStr::from_ptr(host).to_string_lossy() };
        let password = unsafe { CStr::from_ptr(password).to_string_lossy() };
        context_clone.connect_tunnel(host.to_string(), port as u16, password.to_string()).await;
    });
    forget(tc);
    forget(rt);
}

/// 关闭隧道连接
#[unsafe(no_mangle)]
pub extern "C" fn close_tunnel(rt: i64, context_ptr: i64) {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };

    let context_clone = Arc::clone(tc.as_ref());

    rt.block_on(async move {
        context_clone.close_tunnel().await;
    });

    forget(tc);
    forget(rt);
}

/// 获取隧道上传流量
/// 返回上传流量值
#[unsafe(no_mangle)]
pub extern "C" fn get_tunnel_upload(rt: i64, context_ptr: i64) -> i64 {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };
    let context_clone = Arc::clone(tc.as_ref());

    let result = rt.block_on(async move {
        context_clone.get_tunnel_upload().await
    });

    forget(tc);
    forget(rt);
    result
}

/// 获取隧道下载流量
/// 返回下载流量值
#[unsafe(no_mangle)]
pub extern "C" fn get_tunnel_download(rt: i64, context_ptr: i64) -> i64 {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };

    let context_clone = Arc::clone(tc.as_ref());

    let result = rt.block_on(async move {
        context_clone.get_tunnel_download().await
    });

    forget(tc);
    forget(rt);
    result
}

/// 获取隧道Ping值
/// 返回隧道Ping值
#[unsafe(no_mangle)]
pub extern "C" fn get_tunnel_ping_delay(rt: i64, context_ptr: i64) -> i32 {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };

    let context_clone = Arc::clone(tc.as_ref());

    let result = rt.block_on(async move {
        context_clone.get_tunnel_ping_delay().await
    });

    forget(tc);
    forget(rt);
    result
}

/// 获取隧道状态
#[unsafe(no_mangle)]
pub extern "C" fn get_tunnel_status(rt: i64, context_ptr: i64) -> i32 {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };
    let tc = unsafe { Box::from_raw(context_ptr as *mut Arc<TunnelContext>) };

    let context_clone = Arc::clone(tc.as_ref());

    let result = rt.block_on(async move {
        context_clone.get_tunnel_status().await
    });

    forget(tc);
    forget(rt);
    result
}

/// 测试隧道延迟
#[unsafe(no_mangle)]
pub extern "C" fn test_tunnel_delay(rt: i64, connect_info: *const c_char) -> i64 {
    let rt = unsafe { Box::from_raw(rt as *mut Runtime) };

    let ping_delay = rt.block_on(async {
        let host = unsafe { CStr::from_ptr(connect_info).to_string_lossy() };
        Tunnel::test_ping(host.to_string()).await
    });

    forget(rt);

    ping_delay
}