# Flyshadow Mac TUN 优雅关闭指南

本指南介绍了如何优雅地关闭被Java程序通过Runtime执行的flyshadow_mac_tun程序。

## 概述

flyshadow_mac_tun是一个TUN设备管理程序，它需要进行网络路由配置、DNS设置等系统级操作。优雅关闭对于确保系统状态正确恢复非常重要。

## 实现的优雅关闭功能

### 1. 信号处理（推荐方案）

程序现在支持以下信号：
- `SIGTERM` (15) - 标准终止信号
- `SIGINT` (2) - 中断信号 (Ctrl+C)

当收到这些信号时，程序会：
1. 停止所有数据处理任务
2. 清理网络路由配置
3. 恢复DNS设置
4. 刷新DNS缓存
5. 释放TUN设备资源

### 2. 资源清理

程序在关闭时会自动执行以下清理操作：
- 删除添加的网络路由
- 恢复原始DNS配置
- 刷新系统DNS缓存
- 关闭网络连接
- 释放TUN设备

## Java端使用方法

### 方案1：使用Process.destroy()（推荐）

```java
// 启动进程
ProcessBuilder pb = new ProcessBuilder("./flyshadow_mac_tun/target/release/flyshadow_mac_tun");
Process process = pb.start();

// 优雅关闭
process.destroy(); // 发送SIGTERM
boolean exited = process.waitFor(10, TimeUnit.SECONDS);

if (!exited) {
    process.destroyForcibly(); // 强制关闭
}
```

### 方案2：使用JVM关闭钩子

```java
Runtime.getRuntime().addShutdownHook(new Thread(() -> {
    if (process.isAlive()) {
        process.destroy();
        try {
            process.waitFor(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            process.destroyForcibly();
        }
    }
}));
```

## 完整示例

参考提供的Java示例文件：

1. **GracefulShutdownExample.java** - 基本的信号处理方案
2. **AlternativeShutdownMethods.java** - 多种关闭方法的综合示例

## 构建和测试

### 构建程序

```bash
cd flyshadow_mac_tun
cargo build --release
```

### 测试优雅关闭

```bash
# 使用提供的测试脚本
chmod +x test_graceful_shutdown.sh
./test_graceful_shutdown.sh
```

### 手动测试

```bash
# 启动程序
./flyshadow_mac_tun/target/release/flyshadow_mac_tun &
PID=$!

# 发送SIGTERM信号
kill -TERM $PID

# 或发送SIGINT信号
kill -INT $PID
```

## 日志输出

程序在关闭过程中会输出详细的日志信息：

```
[INFO] Received SIGTERM, initiating graceful shutdown
[INFO] Shutdown signal received, cleaning up...
[INFO] Starting resource cleanup
[INFO] Cleaning up network routing
[INFO] Cleaning up TUN routes
[INFO] Removed route to 10.172.0.0/16
[INFO] Successfully restored original DNS configuration
[INFO] DNS cache flushed successfully
[INFO] Resource cleanup completed
[INFO] Graceful shutdown completed
```

## 故障排除

### 常见问题

1. **权限不足**
   - 确保程序有足够权限修改网络配置
   - 可能需要sudo权限

2. **路由清理失败**
   - 检查路由是否已经被删除
   - 查看系统日志获取详细错误信息

3. **DNS恢复失败**
   - 手动检查/etc/resolv.conf文件
   - 可能需要手动恢复DNS配置

### 调试

启用详细日志：
```bash
RUST_LOG=debug ./flyshadow_mac_tun/target/release/flyshadow_mac_tun
```

## 最佳实践

1. **总是使用优雅关闭**：避免直接kill -9，这会导致资源泄露
2. **设置合理的超时时间**：给程序足够时间完成清理工作
3. **监控日志输出**：确保清理操作成功完成
4. **测试关闭流程**：在生产环境使用前充分测试
5. **备份配置**：在修改系统配置前进行备份

## 扩展功能

如果需要更复杂的控制机制，可以考虑：

1. **HTTP控制接口**：通过HTTP API控制程序
2. **Unix Domain Socket**：通过本地socket接收控制命令
3. **文件监控**：通过监控特定文件来触发关闭
4. **配置热重载**：支持运行时重新加载配置

这些扩展功能的示例代码可以在`alternative_shutdown_methods.rs`中找到。
