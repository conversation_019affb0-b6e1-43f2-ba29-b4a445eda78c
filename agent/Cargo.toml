[package]
name = "agent"
version = "0.1.0"
edition = "2024"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
flyshadow_common = { path = "../flyshadow_common" }

uuid = { version = "1.10.0", features = ["v4"] }
reqwest = { version = "0.11.27", features = ["serde_json", "json"] }
tokio = { version = "1.38.0", features = ["full"] }
md5 = "0.7.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
fern = "0.6"
log = "0.4"
chrono = "0.4"
sysinfo = "0.30.13"
socket2 = "0.5.7"
clap = { version = "4", features = ["derive"] }
warp = "0.3"
futures-util = "0.3.30"
rsa = "0.8"
base64 = "0.21"
