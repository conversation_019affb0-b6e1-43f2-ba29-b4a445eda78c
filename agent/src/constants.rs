/// Agent通信方法常量定义
///
/// 这些常量定义了Agent与服务端通信时使用的方法编号，
/// 用于替代原有的魔法数字，提高代码可读性和维护性。

/// 获取节点信息响应
/// 用于请求和响应节点的基本配置信息
pub const METHOD_GET_NODE_INFO: i32 = 1;

/// 获取用户列表响应  
/// 用于请求和响应节点的用户列表信息
pub const METHOD_GET_USER_LIST: i32 = 2;

/// 上传用户在线信息响应
/// 用于上报用户的在线状态和IP地址信息
pub const METHOD_UPLOAD_USER_ONLINE: i32 = 3;

/// 上传用户流量响应
/// 用于上报用户的上传下载流量统计信息
pub const METHOD_UPLOAD_USER_TRAFFIC: i32 = 4;

/// 上传节点状态响应
/// 用于上报节点的系统状态信息（CPU、内存、磁盘等）
pub const METHOD_UPLOAD_NODE_STATUS: i32 = 5;
