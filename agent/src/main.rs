use crate::config::Config;
use crate::context::agent_connector_mapper::AgentConnectorMapper;
use crate::context::context::AgentContext;
use chrono::Local;
use clap::{arg, ArgMatches, Command};
use context::agent_connector::AgentConnector;
use fern::FormatCallback;
use log::Record;
use serde::de::Unexpected::Option;
use std::fmt::Arguments;
use std::fs::{File, OpenOptions};
use std::io::{BufRead, BufReader, Read, Write};
use std::process::{Command as StdCommand, Stdio};
use std::sync::Arc;
use std::{env, fs, process};
use tokio::sync::oneshot::{channel, Sender};
use tokio::sync::RwLock;

mod context;
mod server;
mod route_table;
mod config;
mod test;
mod server_mapper;
mod constants;

fn init_log(log_path: &String) {
    // 配置日志级别
    let level = log::LevelFilter::Info;

    // 设置日志格式
    let format = |out: FormatCallback, message: &Arguments, record: &Record| {
        out.finish(format_args!(
            "[{}] [{} {}] {}",
            Local::now().format("%Y-%m-%d %H:%M:%S"),
            record.level(),
            record.target(),
            message
        ))
    };


    let stdout_log = fern::Dispatch::new()
        .level(level)
        .format(format)
        .chain(std::io::stdout());

    // 配置 fern 日志
    let file_log = fern::Dispatch::new()
        .level(level)
        .format(format)
        .chain(OpenOptions::new()
            .create(true)
            .append(true)
            .open(log_path)
            .expect("Failed to open log file"));

    let mut root_dispatch = fern::Dispatch::new()
        .chain(file_log)
        .chain(stdout_log);

    // 初始化日志记录器
    root_dispatch.apply().expect("Failed to set up logging");
}

const VERSION: &str = include_str!("../static/version");

#[tokio::main]
async fn main() {
    let matches = Command::new("FlyShadow Agent Service")
        .version(VERSION)
        .about("Manage FlyShadow Agent Service")
        .subcommand(
            Command::new("run")
                .arg(arg!(-c --config <CONFIG> "Optionally sets a config file to use"))
                .arg(arg!(-l --log <LOG> "Optionally sets a log file to use"))
                .about("Run the FlyShadow Agent service"),
        )
        .subcommand(
            Command::new("start")
                .about("Start the FlyShadow Agent service"),
        )
        .subcommand(
            Command::new("restart")
                .about("Restart the FlyShadow Agent service"),
        )
        .subcommand(
            Command::new("stop")
                .about("Stop the FlyShadow Agent service"),
        )
        .subcommand(
            Command::new("install")
                .about("Install FlyShadow Agent service as a system service"),
        )
        .subcommand(
            Command::new("log")
                .arg(arg!(-l --log <LOG> "Optionally sets a log file to use"))
                .about("View the log file"),
        )
        .get_matches();

    match matches.subcommand() {
        Some(("run", arg)) => {
            run_service(arg).await
        }
        Some(("restart", _)) => {
            restart_service()
        }
        Some(("start", _)) => {
            start_service()
        }
        Some(("stop", _)) => {
            stop_service()
        }
        Some(("install", _)) => {
            install_service()
        }
        Some(("log", arg)) => {
            view_log(arg).await
        }
        _ => eprintln!("Invalid command. Use 'run', 'restart', 'start', 'stop', 'log', or 'install'."),
    }
}

async fn run_service(arg: &ArgMatches) {
    let default_log_path = "/var/log/flyshadow.log".to_string();
    let log_path: &String = arg.get_one("log").unwrap_or(&default_log_path);
    init_log(log_path);

    let default_path = "/etc/flyshadow/config.yaml".to_string();
    let config_path: &String = arg.get_one("config").unwrap_or(&default_path);

    let mut file = File::open(config_path).expect(format!("Config file not exist in folder {}", config_path).as_str());
    let mut contents = String::new();
    file.read_to_string(&mut contents).expect("Read config file error");

    // 解析YAML内容
    let config: Config = serde_yaml::from_str(&contents).expect("Parse Config file error");

    let password = config.password.expect("Password not defined in config.yaml");
        
    let node_id = config.node_id.expect("Node ID not defined in config.yaml");
    if node_id.len() == 0 {
        log::error!("Node ID not defined in config.yaml");
        process::exit(1);
    }

    let (running_channel_sender, running_channel_receiver) = channel::<()>();
    let running_channel_sender = Arc::new(RwLock::new(Some(running_channel_sender)));

    //Agent映射
    let mapper = Arc::new(AgentConnectorMapper::new());

    // 服务端公钥 (这里需要替换为实际的服务端公钥)
    let server_public_key = r#"-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1z0PjlW2NUJN4PstlqPf
55IK8Wxi5aKky+Z9FRdoD/6qvlRO+YCU7K5TNuKWXCOttHtLh0NBLffLuIUoVSw0
PrVOPfq12Moubd5/0iqhwfDLSeB57Gkaba3u/sK0+V5IiIyyjUHv/Q4orSf4x4nz
Q7qLQN5TlC+OkTbCSpKDDgmYvuNH18s1fcHrorSaH/KixYZPjJ0xDGCjcsLXVXH9
QUV5kN5utCMQQNAJAOkXrPC6yGr4cgeU3Roemy9GIn03VlRIJHIY1GbwFt/gApVX
byBIvyPBuLLjscMVg9T7bwo4/jQRWftNIUwId6iZzDm7Mw2czOfAr0fwzuZkOC6Y
kQIDAQAB
-----END PUBLIC KEY-----"#.to_string();

    // 服务端连接器
    let agent_connector = match AgentConnector::new(
        "https://api.hkspeedup.com/api/proxy/agent".to_string(),
        password,
        server_public_key,
        running_channel_sender,
        mapper.clone(),
    ).await {
        Ok(connector) => Arc::new(connector),
        Err(e) => {
            log::error!("创建Agent连接器失败: {}", e);
            process::exit(1);
        }
    };

    for id in node_id {
        log::info!("Starting agent with Node Id: {}", id);
        let agent_context = AgentContext::run(id, agent_connector.clone()).await;
        mapper.put(id, agent_context).await;
    };

    // 无限循环，确保主线程保持运行
    let _ = running_channel_receiver.await;
}


fn restart_service() {
    let output = StdCommand::new("systemctl")
        .arg("restart")
        .arg("flyshadow.service")
        .output()
        .expect("Failed to start service");
    println!("Restart Service: {}", String::from_utf8_lossy(&output.stdout));
}

fn start_service() {
    let output = StdCommand::new("systemctl")
        .arg("start")
        .arg("flyshadow.service")
        .output()
        .expect("Failed to start service");
    println!("Start Service: {}", String::from_utf8_lossy(&output.stdout));
}

fn stop_service() {
    let output = StdCommand::new("systemctl")
        .arg("stop")
        .arg("flyshadow.service")
        .output()
        .expect("Failed to stop service");
    println!("Stop Service: {}", String::from_utf8_lossy(&output.stdout));
}

fn install_service() {
    let target_dir = "/etc/flyshadow";
    let service_file_path = format!("/etc/systemd/system/flyshadow.service");

    // 获取当前二进制文件的路径
    let current_exe = env::current_exe().expect("Failed to get current executable path");
    let binary_path = current_exe.to_str().expect("Failed to convert path to str");

    // 创建目标目录（如果不存在）
    fs::create_dir_all(target_dir).expect("Failed to create target directory");

    // 复制当前二进制文件到目标目录
    let target_binary_path = format!("{}/agent", target_dir);
    fs::copy(binary_path, &target_binary_path).expect("Failed to copy binary file");

    // 创建服务文件内容
    let service_file_content = format!(
        r#"
[Unit]
Description=FlyShadow Agent Service
After=network.target

[Service]
ExecStart={} run
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
"#,
        target_binary_path
    );

    // 写入服务文件
    let mut file = File::create(&service_file_path).expect("Failed to create service file");
    file.write_all(service_file_content.as_bytes())
        .expect("Failed to write to service file");

    // 重新加载 systemd 以识别新的服务
    let output = StdCommand::new("systemctl")
        .arg("daemon-reload")
        .output()
        .expect("Failed to reload systemd");
    println!("{}", String::from_utf8_lossy(&output.stdout));

    // 启用服务
    let output = StdCommand::new("systemctl")
        .arg("enable")
        .arg("flyshadow.service")
        .output()
        .expect("Failed to enable service");
    println!("Install Service: {}", String::from_utf8_lossy(&output.stdout));
}

async fn view_log(arg: &ArgMatches) {
    let default_log_path = "/var/log/flyshadow.log".to_string();
    let log_path: &String = arg.get_one("log").unwrap_or(&default_log_path);

    // 启动 tail 命令
    let process = StdCommand::new("tail")
        .arg("-f")
        .arg(log_path)
        .stdin(Stdio::null())   // 不需要提供输入
        .stdout(Stdio::piped()) // 需要读取输出
        .spawn()
        .expect(format!("Read {} log error", log_path).as_str());

    let stdout = process.stdout.expect("Failed to capture stdout");

    // 创建一个 BufReader 来逐行读取输出
    let reader = BufReader::new(stdout);

    // 遍历输出的每一行并打印到控制台
    for line in reader.lines() {
        match line {
            Ok(content) => println!("{}", content),
            Err(e) => eprintln!("Error reading line: {}", e),
        }
    }
}