mod test {
    use crate::context::agent_connector::AgentConnector;
    use crate::context::agent_connector_mapper::AgentConnectorMapper;
    use crate::context::context::AgentContext;
    use crate::{init_log, VERSION};
    use chrono::Local;
    use fern::FormatCallback;
    use log::{Level, Record};
    use serde_json::json;
    use std::fmt::Arguments;
    use std::sync::Arc;
    use std::time::{Duration, SystemTime, UNIX_EPOCH};
    use tokio::sync::oneshot::channel;
    use tokio::sync::RwLock;

    #[tokio::test]
    async fn test_version() {
        let json = json!({
            "method":5,
            "data":{
                "password":1,
                "nodeId":1,
                "cpu": 1,
                "mem": 1,
                "net": "".to_string(), // Network usage can be added here
                "disk": 1,
                "uptime":1,
                "version:": VERSION,
            }
        });
        println!("{}", json);
    }

    #[tokio::test]
    async fn run() {
        // 配置日志级别
        let level = log::LevelFilter::Info;

        // 设置日志格式
        let format = |out: FormatCallback, message: &Arguments, record: &Record| {
            out.finish(format_args!(
                "[{}] [{} {}] {}",
                Local::now().format("%Y-%m-%d %H:%M:%S"),
                record.level(),
                record.target(),
                message
            ))
        };


        let stdout_log = fern::Dispatch::new()
            .level(level)
            .format(format)
            .chain(std::io::stdout());

        let mut root_dispatch = fern::Dispatch::new()
            .chain(stdout_log);

        // 初始化日志记录器
        root_dispatch.apply().expect("Failed to set up logging");

        let id = 54;
        log::info!("Starting agent with Node Id: {}", id);

        let (running_channel_sender, running_channel_receiver) = channel::<()>();
        let running_channel_sender = Arc::new(RwLock::new(Some(running_channel_sender)));

        //Agent映射
        let mapper = Arc::new(AgentConnectorMapper::new());

        // 服务端公钥 (这里需要替换为实际的服务端公钥)
        let server_public_key = r#"-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1z0PjlW2NUJN4PstlqPf
55IK8Wxi5aKky+Z9FRdoD/6qvlRO+YCU7K5TNuKWXCOttHtLh0NBLffLuIUoVSw0
PrVOPfq12Moubd5/0iqhwfDLSeB57Gkaba3u/sK0+V5IiIyyjUHv/Q4orSf4x4nz
Q7qLQN5TlC+OkTbCSpKDDgmYvuNH18s1fcHrorSaH/KixYZPjJ0xDGCjcsLXVXH9
QUV5kN5utCMQQNAJAOkXrPC6yGr4cgeU3Roemy9GIn03VlRIJHIY1GbwFt/gApVX
byBIvyPBuLLjscMVg9T7bwo4/jQRWftNIUwId6iZzDm7Mw2czOfAr0fwzuZkOC6Y
kQIDAQAB
-----END PUBLIC KEY-----"#.to_string();

        // 服务端连接器
        let agent_connector = match AgentConnector::new(
            // "http://localhost:8077/api/proxy/agent".to_string(),
            "https://api.hkspeedup.com/api/proxy/agent".to_string(),
            "127698dd-b3a5-11".to_string(),
            server_public_key,
            running_channel_sender,
            mapper.clone(),
        ).await {
            Ok(connector) => Arc::new(connector),
            Err(e) => {
                log::error!("创建Agent连接器失败: {}", e);
                return;
            }
        };


        let agent_context = AgentContext::run(id, agent_connector).await;
        mapper.put(id, agent_context).await;
        // 无限循环，确保主线程保持运行
        let _ = running_channel_receiver.await;
    }
}
