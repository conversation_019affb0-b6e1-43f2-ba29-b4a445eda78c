use std::collections::HashSet;
use tokio::sync::RwLock;

// 远程用户结构体，包含用户ID、密码、MD5密码、上传下载流量和在线IP列表
pub struct RemoteUser {
    pub uid: i32,
    pub passwd: String,
    pub passwd_md5: String,
    pub upload: RwLock<i64>,
    pub download: RwLock<i64>,
    pub online_ip: RwLock<HashSet<String>>,
    pub uuid: RwLock<HashSet<String>>,

}

impl RemoteUser {
    // 创建新的远程用户
    pub fn new(uid: i32, passwd: String) -> RemoteUser {
        let md5_pwd = md5::compute(passwd.as_bytes());
        RemoteUser {
            uid,
            passwd,
            passwd_md5: format!("{:x}", md5_pwd),
            upload: RwLock::new(0),
            download: RwLock::new(0),
            online_ip: RwLock::new(HashSet::new()),
            uuid: RwLock::new(HashSet::new()),
        }
    }
}