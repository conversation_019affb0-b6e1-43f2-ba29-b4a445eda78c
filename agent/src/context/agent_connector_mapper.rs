use crate::context::context::AgentContext;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

pub struct AgentConnectorMapper {
    map: RwLock<HashMap<i32, Arc<AgentContext>>>,
}

impl AgentConnectorMapper {
    pub fn new() -> AgentConnectorMapper {
        AgentConnectorMapper {
            map: RwLock::new(HashMap::new()),
        }
    }

    pub async fn put(&self, node_id: i32, agent_context: Arc<AgentContext>) {
        self.map.write().await.insert(node_id, agent_context);
    }

    pub async fn get(&self, node_id: i32) -> Option<Arc<AgentContext>> {
        self.map.read().await.get(&node_id).cloned()
    }
}
