use crate::constants::{METHOD_UPLOAD_NODE_STATUS, METHOD_UPLOAD_USER_ONLINE, METHOD_UPLOAD_USER_TRAFFIC};
use crate::context::agent_connector::AgentConnector;
use crate::context::vo::node_info_vo::NodeInfoVo;
use crate::context::vo::remote_user::RemoteUser;
use crate::context::vo::user_online_vo::UserOnlineVo;
use crate::context::vo::user_traffic_vo::UserTrafficVo;
use crate::context::vo::user_vo::UserVo;
use crate::route_table::route_context::RouteContext;
use crate::server::server::AgentServer;
use crate::server_mapper::server_mapper::ServerMapper;
use crate::VERSION;
use log::info;
use serde_json::json;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use sysinfo::{Disks, System};
use tokio::spawn;
use tokio::sync::RwLock;
use tokio::time::sleep;

/// Agent上下文结构体
///
/// 这是Agent的核心上下文管理器，负责管理节点的所有状态信息，包括：
/// - 节点标识和配置信息
/// - 远程用户管理和认证
/// - 路由表和服务器映射
/// - 与控制服务器的连接管理
/// - 系统状态监控和上报
pub struct AgentContext {
    /// 节点唯一标识ID
    node_id: i32,
    /// 远程用户列表，使用读写锁保证并发安全
    /// Key: 用户ID, Value: 远程用户信息
    remote_user_list: RwLock<HashMap<i32, RemoteUser>>,
    /// 路由上下文，管理网络路由规则
    pub route_context: RouteContext,
    /// 服务器映射器，管理代理服务器配置
    pub server_mapper: ServerMapper,
    /// 原生UDP功能启用标志 (0=禁用, 1=启用)
    native_udp_enable: RwLock<u8>,
    /// Agent服务器实例，处理客户端连接
    agent_server: RwLock<Option<AgentServer>>,
    /// Agent连接器，负责与控制服务器通信
    agent_connector: Arc<AgentConnector>,
}

impl AgentContext {
    /// 设置用户登录状态
    ///
    /// 管理用户的在线状态，记录用户的登录IP地址
    ///
    /// # 参数
    /// * `uid` - 用户ID
    /// * `login` - 登录状态，true表示登录，false表示登出
    /// * `ip` - 用户的IP地址
    /// * `uuid` - 用户的UUID
    pub async fn set_user_login(&self, uid: &i32, login: bool, ip: String, uuid: String) {
        if let Some(user) = self.remote_user_list.read().await.get(uid) {
            {
                let mut online_ips = user.online_ip.write().await;
                if login {
                    online_ips.insert(ip);
                } else {
                    online_ips.remove(&ip);
                }
            }

            {
                let mut uuid_set = user.uuid.write().await;
                if login {
                    uuid_set.insert(uuid);
                } else {
                    uuid_set.remove(&uuid);
                }
            }
        }
    }

    /// 获取所有远程用户的MD5密码列表
    ///
    /// 用于身份验证，返回所有用户的MD5加密密码
    ///
    /// # 返回值
    /// 包含所有用户MD5密码的向量
    pub async fn get_remote_user_passwd_md5_list(&self) -> Vec<String> {
        let remote_user_list = self.remote_user_list.read().await;
        remote_user_list
            .values()
            .map(|user| user.passwd_md5.clone())
            .collect()
    }

    /// 根据MD5密码获取远程用户信息
    ///
    /// 通过MD5密码查找对应的用户信息，用于用户认证
    ///
    /// # 参数
    /// * `passwd_md5` - MD5加密的密码
    ///
    /// # 返回值
    /// 元组 (用户ID, MD5密码)，如果未找到返回 (-1, "")
    pub async fn get_remote_user_info(&self, passwd_md5: &str) -> (i32, String) {
        let remote_user_list = self.remote_user_list.read().await;
        for user in remote_user_list.values() {
            if user.passwd_md5 == passwd_md5 {
                return (user.uid, user.passwd_md5.clone());
            }
        }
        (-1, String::new())
    }

    /// 增加用户上传流量统计
    ///
    /// 记录用户的上传数据量，用于流量统计和计费
    ///
    /// # 参数
    /// * `uid` - 用户ID
    /// * `bytes` - 上传的字节数
    pub async fn add_user_upload(&self, uid: &i32, bytes: usize) {
        if let Some(user) = self.remote_user_list.read().await.get(uid) {
            *user.upload.write().await += bytes as i64;
        }
    }

    /// 增加用户下载流量统计
    ///
    /// 记录用户的下载数据量，用于流量统计和计费
    ///
    /// # 参数
    /// * `uid` - 用户ID
    /// * `bytes` - 下载的字节数
    pub async fn add_user_download(&self, uid: &i32, bytes: usize) {
        if let Some(user) = self.remote_user_list.read().await.get(uid) {
            *user.download.write().await += bytes as i64;
        }
    }
}

impl AgentContext {
    /// 创建并启动Agent上下文
    ///
    /// 这是Agent的主要入口点，负责初始化所有组件并启动服务
    ///
    /// # 参数
    /// * `node_id` - 节点唯一标识ID
    /// * `agent_connector` - Agent连接器，用于与控制服务器通信
    ///
    /// # 返回值
    /// 返回初始化完成的Agent上下文实例
    pub async fn run(
        node_id: i32,
        agent_connector: Arc<AgentConnector>,
    ) -> Arc<AgentContext> {
        // 创建Agent上下文实例
        let agent_context = Arc::new(AgentContext {
            node_id,
            remote_user_list: RwLock::new(HashMap::new()),
            route_context: RouteContext::new(),
            server_mapper: ServerMapper::new(),
            native_udp_enable: RwLock::new(0), // 默认禁用原生UDP
            agent_server: RwLock::new(None),
            agent_connector,
        });

        // 初始化Agent服务器
        let server = AgentServer::new(agent_context.clone());
        *agent_context.agent_server.write().await = Some(server);

        // 启动与控制服务器的连接和数据上报任务
        agent_context.start_connector(agent_context.clone()).await;

        agent_context
    }

    /// 启动连接器和数据上报任务
    ///
    /// 创建后台任务，定期向控制服务器上报节点状态、用户信息和流量数据
    ///
    /// # 参数
    /// * `agent_context` - Agent上下文的Arc引用
    async fn start_connector(&self, agent_context: Arc<AgentContext>) {
        let node_id = self.node_id;
        let agent_connector = self.agent_connector.clone();

        spawn(async move {
            loop {
                // 执行一轮数据上报
                {
                    // 获取节点基础信息
                    agent_connector.send_get_info0(node_id).await;

                    // 上报用户在线状态
                    Self::upload_user_online(
                        node_id,
                        agent_context.clone(),
                        agent_connector.clone(),
                    ).await;

                    // 上报用户流量统计
                    Self::upload_user_traffic(
                        node_id,
                        agent_context.clone(),
                        agent_connector.clone(),
                    ).await;

                    // 上报节点系统状态
                    Self::upload_node_status(node_id, agent_connector.clone()).await;
                }

                // 等待60秒后进行下一轮上报
                sleep(Duration::from_secs(60)).await;
            }
        });
    }

    /// 设置原生UDP功能启用状态
    ///
    /// # 参数
    /// * `enable` - 启用标志，0表示禁用，1表示启用
    pub async fn set_native_udp_enable(&self, enable: u8) {
        *self.native_udp_enable.write().await = enable;
    }

    /// 获取原生UDP功能启用状态
    ///
    /// # 返回值
    /// true表示启用，false表示禁用
    pub async fn get_native_udp_enable(&self) -> bool {
        *self.native_udp_enable.read().await == 1
    }

    /// 启动Agent代理服务器
    ///
    /// 根据节点配置信息启动代理服务器，监听指定端口处理客户端连接
    ///
    /// # 参数
    /// * `node_info_vo` - 节点配置信息，包含端口和UDP设置
    pub async fn start_agent_server(&self, node_info_vo: NodeInfoVo) {
        let enable_native_udp = node_info_vo.native_udp.unwrap_or_default();

        // 更新UDP配置
        self.set_native_udp_enable(enable_native_udp).await;

        // 启动服务器
        if let Some(server) = self.agent_server.read().await.as_ref() {
            match server.start(node_info_vo.port).await {
                Ok(_) => {
                    info!(
                        "NodeID:{} Agent代理服务器启动成功 - 端口: {}, 原生UDP: {}",
                        self.node_id,
                        node_info_vo.port,
                        if enable_native_udp == 1 { "启用" } else { "禁用" }
                    );
                }
                Err(e) => {
                    // 需要添加error导入
                    eprintln!(
                        "NodeID:{} Agent代理服务器启动失败 - 端口: {}, 错误: {}",
                        self.node_id,
                        node_info_vo.port,
                        e
                    );
                }
            }
        }
    }

    /// 关闭Agent代理服务器
    ///
    /// 优雅地关闭代理服务器，释放端口和资源
    pub async fn close_agent_server(&self) {
        if let Some(server) = self.agent_server.read().await.as_ref() {
            server.close().await;
            info!("NodeID:{} Agent代理服务器已关闭", self.node_id);
        }
    }

    /// 更新本地用户列表
    ///
    /// 根据从控制服务器获取的用户列表更新本地缓存，包括添加新用户、
    /// 移除已删除用户和更新密码变更的用户
    ///
    /// # 参数
    /// * `user_list` - 从服务器获取的最新用户列表
    pub async fn update_local_user_list(&self, user_list: Vec<UserVo>) {
        let mut remote_user_list = self.remote_user_list.write().await;
        let mut added_count = 0;
        let mut updated_count = 0;

        if user_list.is_empty() {
            // 如果服务器返回空列表，清空本地用户列表
            let old_count = remote_user_list.len();
            remote_user_list.clear();
            info!("NodeID:{} 用户列表已清空，移除了 {} 个用户", self.node_id, old_count);
            return;
        }

        // 找出需要移除的用户（本地存在但服务器列表中不存在）
        let users_to_remove: Vec<i32> = remote_user_list
            .keys()
            .filter(|&&uid| !user_list.iter().any(|user| user.uid == uid))
            .cloned()
            .collect();

        // 移除不存在的用户
        let removed_count = users_to_remove.len();
        for uid in users_to_remove {
            remote_user_list.remove(&uid);
        }

        // 添加或更新用户
        for user_vo in &user_list {
            match remote_user_list.get(&user_vo.uid) {
                Some(existing_user) => {
                    // 用户已存在，检查密码是否变更
                    if user_vo.passwd != existing_user.passwd {
                        remote_user_list.insert(
                            user_vo.uid,
                            RemoteUser::new(user_vo.uid, user_vo.passwd.clone()),
                        );
                        updated_count += 1;
                    }
                }
                None => {
                    // 新用户，直接添加
                    remote_user_list.insert(
                        user_vo.uid,
                        RemoteUser::new(user_vo.uid, user_vo.passwd.clone()),
                    );
                    added_count += 1;
                }
            }
        }

        info!(
            "NodeID:{} 用户列表更新完成 - 移除: {}, 新增: {}, 更新: {}, 总计: {}",
            self.node_id,
            removed_count,
            added_count,
            updated_count,
            user_list.len()
        );
    }

    /// 上报节点系统状态
    ///
    /// 收集并上报节点的系统资源使用情况，包括CPU、内存、磁盘使用率等
    ///
    /// # 参数
    /// * `node_id` - 节点ID
    /// * `agent_connector` - Agent连接器，用于发送数据
    async fn upload_node_status(
        node_id: i32,
        agent_connector: Arc<AgentConnector>
    ) {
        // 初始化系统信息收集器
        let mut system = System::new_all();
        system.refresh_all();

        // 获取CPU使用率
        let cpu_usage = system.global_cpu_info().cpu_usage();

        // 计算内存使用率
        let memory_usage = if system.total_memory() > 0 {
            (system.used_memory() as f32 / system.total_memory() as f32) * 100.0
        } else {
            0.0
        };

        // 计算磁盘使用率（取第一个磁盘）
        let disk_usage = Disks::new_with_refreshed_list()
            .first()
            .map_or(0.0, |disk| {
                if disk.total_space() > 0 {
                    (1.0 - disk.available_space() as f32 / disk.total_space() as f32) * 100.0
                } else {
                    0.0
                }
            });

        // 获取系统运行时间
        let uptime = System::uptime();
        let version = VERSION;

        info!(
            "NodeID:{} 系统状态 - CPU: {:.1}%, 内存: {:.1}%, 磁盘: {:.1}%, 运行时间: {}s, 版本: {}",
            node_id, cpu_usage, memory_usage, disk_usage, uptime, version
        );

        // 构造状态数据JSON
        let status_data = json!({
            "method": METHOD_UPLOAD_NODE_STATUS,
            "data": {
                "password": "",
                "nodeId": node_id,
                "cpu": format!("{:.1}%", cpu_usage),
                "mem": format!("{:.1}%", memory_usage),
                "net": "", // 网络使用率，可以后续添加
                "disk": format!("{:.1}%", disk_usage),
                "uptime": uptime,
                "version": version,
            }
        });

        // 发送状态数据到控制服务器
        agent_connector.send_json(status_data).await;
    }

    /// 上报用户流量统计数据
    ///
    /// 收集所有用户的流量使用情况并上报到控制服务器，
    /// 上报后会重置用户的流量计数器
    ///
    /// # 参数
    /// * `node_id` - 节点ID
    /// * `agent_context` - Agent上下文
    /// * `agent_connector` - Agent连接器
    async fn upload_user_traffic(
        node_id: i32,
        agent_context: Arc<AgentContext>,
        agent_connector: Arc<AgentConnector>
    ) {
        let mut traffic_data_list = Vec::new();
        let remote_user_list = agent_context.remote_user_list.read().await;

        // 收集所有用户的流量数据（不立即重置计数器）
        for remote_user in remote_user_list.values() {
            // 获取用户流量计数器（不重置）
            let upload_bytes = {
                let upload = remote_user.upload.read().await;
                *upload
            };

            let download_bytes = {
                let download = remote_user.download.read().await;
                *download
            };

            // 只有当用户有流量使用时才添加到上报列表
            if upload_bytes > 0 || download_bytes > 0 {
                traffic_data_list.push(UserTrafficVo {
                    uid: remote_user.uid,
                    upload: upload_bytes,
                    download: download_bytes,
                    uuid: remote_user.uuid.read().await.clone(),
                });
            }
        }

        // 如果有流量数据则上报
        if !traffic_data_list.is_empty() {
            let traffic_count = traffic_data_list.len();
            let total_upload: i64 = traffic_data_list.iter().map(|t| t.upload).sum();
            let total_download: i64 = traffic_data_list.iter().map(|t| t.download).sum();

            // 序列化流量数据
            match serde_json::to_value(&traffic_data_list) {
                Ok(data) => {
                    // 尝试上报流量数据
                    match agent_connector.send_message(node_id, METHOD_UPLOAD_USER_TRAFFIC, data).await {
                        Ok(_) => {
                            // 上报成功，重置所有用户的流量计数器
                            for remote_user in remote_user_list.values() {
                                *remote_user.upload.write().await = 0;
                                *remote_user.download.write().await = 0;
                            }
                            info!(
                                "NodeID:{} 用户流量数据上报成功 - 用户数: {}, 总上传: {}字节, 总下载: {}字节",
                                node_id, traffic_count, total_upload, total_download
                            );
                        }
                        Err(e) => {
                            eprintln!("NodeID:{} 流量数据上报失败: {}", node_id, e);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("NodeID:{} 流量数据序列化失败: {}", node_id, e);
                }
            }
        } else {
            info!("NodeID:{} 无用户流量数据需要上报", node_id);
        }
    }

    /// 上报用户在线状态数据
    ///
    /// 收集所有在线用户的IP地址信息并上报到控制服务器，
    /// 用于监控用户连接状态和统计在线用户数量
    ///
    /// # 参数
    /// * `node_id` - 节点ID
    /// * `agent_context` - Agent上下文
    /// * `agent_connector` - Agent连接器
    async fn upload_user_online(
        node_id: i32,
        agent_context: Arc<AgentContext>,
        agent_connector: Arc<AgentConnector>
    ) {
        let mut online_data_list = Vec::new();
        let remote_user_list = agent_context.remote_user_list.read().await;

        // 收集所有在线用户的IP信息
        for remote_user in remote_user_list.values() {
            let online_ips = remote_user.online_ip.read().await;

            // 为每个在线IP创建一条记录
            for ip in online_ips.iter() {
                online_data_list.push(UserOnlineVo {
                    uid: remote_user.uid,
                    ip: ip.clone(),
                });
            }
        }

        // 如果有在线用户数据则上报
        if !online_data_list.is_empty() {
            let online_count = online_data_list.len();
            let unique_users = online_data_list
                .iter()
                .map(|data| data.uid)
                .collect::<std::collections::HashSet<_>>()
                .len();

            // 序列化在线数据
            match serde_json::to_value(&online_data_list) {
                Ok(data) => {
                    match agent_connector.send_message(node_id, METHOD_UPLOAD_USER_ONLINE, data).await {
                        Ok(_) => {
                            info!(
                                "NodeID:{} 用户在线状态上报成功 - 在线连接数: {}, 在线用户数: {}",
                                node_id, online_count, unique_users
                            );
                        }
                        Err(e) => {
                            eprintln!("NodeID:{} 用户在线状态上报失败: {}", node_id, e);
                        }
                    }
                }
                Err(e) => {
                    eprintln!("NodeID:{} 在线状态数据序列化失败: {}", node_id, e);
                }
            }
        } else {
            info!("NodeID:{} 无在线用户数据需要上报", node_id);
        }
    }
}
