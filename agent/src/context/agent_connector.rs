use crate::constants::{METHOD_GET_NODE_INFO, METHOD_GET_USER_LIST, METHOD_UPLOAD_NODE_STATUS, METHOD_UPLOAD_USER_ONLINE, METHOD_UPLOAD_USER_TRAFFIC};
use crate::context::agent_connector_mapper::AgentConnectorMapper;
use crate::context::context::AgentContext;
use flyshadow_common::util::rsa_util::RsaUtil;
use log::{error, info, warn};
use reqwest::Client;
use serde_json::{json, Value};
use std::sync::atomic::AtomicU64;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::oneshot::Sender;
use tokio::sync::RwLock;

/// HTTP Agent连接器结构体
/// 负责与服务端建立HTTP连接，处理消息收发和连接管理
pub struct AgentConnector {
    /// HTTP服务器URL
    http_url: String,
    /// 认证密码
    password: String,
    /// 本地私钥，用于解密接收到的数据
    private_key: String,
    /// 本地公钥，用于加密发送的数据
    public_key: String,
    /// 服务端公钥，用于加密发送的数据
    server_public_key: String,
    /// HTTP客户端
    http_client: Client,
    /// 连接错误次数计数器，用于重连策略
    connect_error_times: Arc<AtomicU64>,
    /// 运行状态发送器，用于通知程序停止
    running_sender: Arc<RwLock<Option<Sender<()>>>>,
    /// Agent连接器映射器，管理多个Agent上下文
    agent_connector_mapper: Arc<AgentConnectorMapper>,
    /// 第一次错误发生的时间戳（秒）
    first_error_time: Arc<RwLock<Option<u64>>>,
    /// 最后一次成功的时间戳（秒）
    last_success_time: Arc<RwLock<u64>>,
}

impl AgentConnector {
    /// 创建新的Agent连接器实例
    ///
    /// # 参数
    /// * `http_url` - HTTP服务器URL
    /// * `password` - 认证密码
    /// * `server_public_key` - 服务端公钥，用于加密发送数据
    /// * `running_sender` - 程序运行状态控制器
    /// * `agent_connector_mapper` - Agent映射器
    ///
    /// # 返回值
    /// 返回配置好的AgentConnector实例
    pub async fn new(
        http_url: String,
        password: String,
        server_public_key: String,
        running_sender: Arc<RwLock<Option<Sender<()>>>>,
        agent_connector_mapper: Arc<AgentConnectorMapper>,
    ) -> Result<Self, String> {
        // 生成本地RSA密钥对
        let (private_key, public_key) = RsaUtil::generate_keypair(2048)
            .map_err(|e| format!("生成RSA密钥对失败: {}", e))?;

        // 创建HTTP客户端
        let http_client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .map_err(|e| format!("创建HTTP客户端失败: {}", e))?;

        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let connector = AgentConnector {
            http_url,
            password,
            private_key,
            public_key,
            server_public_key,
            http_client,
            connect_error_times: Arc::new(AtomicU64::new(0)),
            running_sender,
            agent_connector_mapper,
            first_error_time: Arc::new(RwLock::new(None)),
            last_success_time: Arc::new(RwLock::new(current_time)),
        };

        Ok(connector)
    }

    /// 发送获取节点信息的请求
    /// 包括获取节点基本信息和节点用户列表
    ///
    /// # 参数
    /// * `node_id` - 节点ID
    pub async fn send_get_info0(&self, node_id: i32) {
        // 发送获取节点信息请求
        let node_info_request = json!({
            "method": METHOD_GET_NODE_INFO,
            "data": {
                "password": self.password.clone(),
                "nodeId": node_id,
            }
        });

        if let Err(e) = self.send_encrypted_message(node_info_request).await {
            error!("发送获取节点信息请求失败: {}", e);
            return;
        }

        // 发送获取节点用户列表请求
        let user_list_request = json!({
            "method": METHOD_GET_USER_LIST,
            "data": {
                "password": self.password.clone(),
                "nodeId": node_id,
            }
        });

        if let Err(e) = self.send_encrypted_message(user_list_request).await {
            error!("发送获取节点用户列表请求失败: {}", e);
        }
    }

    /// 发送加密消息的通用方法
    ///
    /// # 参数
    /// * `json_data` - 要发送的JSON数据
    ///
    /// # 返回值
    /// 发送结果，成功返回Ok(())，失败返回错误信息
    async fn send_encrypted_message(&self, mut json_data: Value) -> Result<(), String> {
        json_data.as_object_mut().unwrap().insert("publicKey".to_string(), Value::String(self.public_key.clone()));

        // 序列化JSON数据
        let json_string = json_data.to_string();

        // 使用服务端公钥加密数据
        let encrypted_data = match RsaUtil::encrypt_large_data(&self.server_public_key, json_string.as_bytes()) {
            Ok(data) => data,
            Err(e) => {
                let error_msg = format!("消息加密失败: {}", e);
                self.record_error().await;
                return Err(error_msg);
            }
        };

        // 构造请求体
        let request_body = json!({
            "data": encrypted_data,
        });

        // 发送POST请求
        let response = match self.http_client
            .post(&self.http_url)
            .json(&request_body)
            .header("timestamp", chrono::Utc::now().timestamp())
            .header("key", "761d2bdf-f6b4-4811-bd1d-102fce7aa476")
            .send()
            .await {
            Ok(resp) => resp,
            Err(e) => {
                let error_msg = format!("发送HTTP请求失败: {}", e.without_url());
                self.record_error().await;
                return Err(error_msg);
            }
        };

        if !response.status().is_success() {
            let error_msg = format!("服务器返回错误状态: {}", response.status());
            self.record_error().await;
            return Err(error_msg);
        }

        // 解析响应数据
        let response_data: Value = match response.json().await {
            Ok(data) => data,
            Err(e) => {
                let error_msg = format!("解析响应JSON失败: {}", e);
                self.record_error().await;
                return Err(error_msg);
            }
        };

        if response_data["code"].as_i64().unwrap_or(-1) != 200 {
            let error_msg = format!("服务器返回错误代码: {}, 错误信息: {}", response_data["code"], response_data["message"]);
            self.record_error().await;
            return Err(error_msg);
        }

        // 到这里说明请求成功，记录成功状态
        self.record_success().await;

        // 检查是否有消息
        if let Some(data) = response_data["data"].as_object() {
            if let Some(encrypted_data) = data["data"].as_str() {
                // 解密消息
                match RsaUtil::decrypt_large_data(&self.private_key, encrypted_data) {
                    Ok(decrypted_data) => {
                        // 处理解密后的消息
                        Self::handle_message(decrypted_data, self.agent_connector_mapper.clone()).await;
                    }
                    Err(e) => {
                        error!("消息解密失败: {}", e);
                    }
                }
            }
        }

        Ok(())
    }

    /// 发送带有节点ID和方法的消息
    ///
    /// # 参数
    /// * `node_id` - 节点ID
    /// * `method` - 方法编号
    /// * `data` - 消息数据
    ///
    /// # 返回值
    /// 发送结果，成功返回Ok(())，失败返回错误信息
    pub async fn send_message(&self, node_id: i32, method: i32, data: Value) -> Result<(), String> {
        let json_data = json!({
            "method": method,
            "data": {
                "password": self.password.clone(),
                "nodeId": node_id,
                "data": data
            }
        });

        self.send_encrypted_message(json_data).await
            .map_err(|e| {
                error!("发送消息失败 (节点ID: {}, 方法: {}): {}", node_id, method, e);
                e
            })
    }

    /// 发送JSON数据，自动填充密码字段
    ///
    /// # 参数
    /// * `json` - 要发送的JSON数据，会自动在data.password字段填充认证密码
    pub async fn send_json(&self, mut json: Value) {
        // 自动填充密码字段
        if let Some(data) = json.get_mut("data") {
            if let Some(data_obj) = data.as_object_mut() {
                data_obj.insert("password".to_string(), Value::String(self.password.clone()));
            }
        }

        if let Err(e) = self.send_encrypted_message(json).await {
            error!("发送数据失败: {}", e);
        }
    }

    /// 处理接收到的消息
    ///
    /// # 参数
    /// * `data` - 解密后的消息数据
    /// * `agent_connector_mapper` - Agent映射器
    async fn handle_message(data: Vec<u8>, agent_connector_mapper: Arc<AgentConnectorMapper>) {
        // 解析JSON数据
        let message_data: Value = match serde_json::from_slice(&data) {
            Ok(data) => data,
            Err(e) => {
                error!("解析消息失败: {}", e);
                return;
            }
        };

        // 获取节点ID
        let node_id = match message_data["nodeId"].as_i64() {
            Some(id) => id as i32,
            None => {
                error!("节点ID错误");
                return;
            }
        };

        // 获取对应的Agent上下文
        let agent_context = match agent_connector_mapper.get(node_id).await {
            Some(context) => context,
            None => {
                error!("未找到节点ID为{}的Agent上下文", node_id);
                return;
            }
        };

        // 获取方法编号
        let method = message_data["method"].as_i64().unwrap_or(0);

        match method {
            method if method == METHOD_GET_NODE_INFO as i64 => {
                Self::handle_node_info_response(message_data, agent_context).await
            },
            method if method == METHOD_GET_USER_LIST as i64 => {
                Self::handle_user_list_response(message_data, agent_context).await
            },
            method if method == METHOD_UPLOAD_USER_ONLINE as i64 => {
                Self::handle_upload_response(message_data, node_id, "用户在线状态").await
            },
            method if method == METHOD_UPLOAD_USER_TRAFFIC as i64 => {
                Self::handle_upload_response(message_data, node_id, "用户流量数据").await
            },
            method if method == METHOD_UPLOAD_NODE_STATUS as i64 => {
                Self::handle_upload_response(message_data, node_id, "节点状态").await
            },
            _ => {
                warn!("收到未知方法编号的消息: {}", method);
            }
        }
    }

    /// 处理节点信息响应
    async fn handle_node_info_response(data: Value, agent_context: Arc<AgentContext>) {
        if let Some(error_message) = data["message"].as_str() {
            error!("获取节点信息失败: {}", error_message);
            agent_context.close_agent_server().await;
        } else if let Some(node_info_data) = data.get("data") {
            match serde_json::from_value::<crate::context::vo::node_info_vo::NodeInfoVo>(
                node_info_data.clone()
            ) {
                Ok(node_info) => {
                    agent_context.start_agent_server(node_info).await;
                }
                Err(e) => {
                    error!("解析节点信息失败: {}", e);
                }
            }
        } else {
            error!("节点信息响应格式错误");
        }
    }

    /// 处理用户列表响应
    async fn handle_user_list_response(data: Value, agent_context: Arc<AgentContext>) {
        if let Some(error_message) = data["message"].as_str() {
            error!("获取节点用户列表失败: {}", error_message);
            agent_context.update_local_user_list(vec![]).await;
        } else if let Some(user_array) = data["data"].as_array() {
            let user_list: Vec<crate::context::vo::user_vo::UserVo> = user_array
                .iter()
                .filter_map(|item| {
                    serde_json::from_value::<crate::context::vo::user_vo::UserVo>(item.clone()).ok()
                })
                .collect();

            agent_context.update_local_user_list(user_list).await;
        } else {
            warn!("用户列表响应格式错误，清空本地用户列表");
            agent_context.update_local_user_list(vec![]).await;
        }
    }

    /// 处理上传数据的响应
    ///
    /// # 参数
    /// * `data` - 响应数据
    /// * `node_id` - 节点ID
    /// * `data_type` - 数据类型描述
    async fn handle_upload_response(data: Value, node_id: i32, data_type: &str) {
        if let Some(error_message) = data["message"].as_str() {
            if !error_message.is_empty() {
                error!("NodeID:{} {}上报失败: {}", node_id, data_type, error_message);
            } else {
                info!("NodeID:{} {}上报成功", node_id, data_type);
            }
        }
    }

    /// 记录错误并检查是否需要停止程序
    ///
    /// 当发生错误时调用此方法记录错误时间，如果连续错误超过一天则停止程序
    async fn record_error(&self) {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        let mut first_error_time = self.first_error_time.write().await;

        // 如果是第一次错误，记录时间
        if first_error_time.is_none() {
            *first_error_time = Some(current_time);
            warn!("开始记录错误时间: {}", current_time);
            return;
        }

        // 检查是否连续错误超过一天（86400秒）
        if let Some(first_time) = *first_error_time {
            let error_duration = current_time - first_time;
            if error_duration >= 86400 {
                error!("连续错误超过一天（{}秒），程序即将停止", error_duration);
                self.stop_program().await;
            }
        }
    }

    /// 记录成功并清空错误记录
    ///
    /// 当操作成功时调用此方法清空错误记录
    async fn record_success(&self) {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        // 更新最后成功时间
        *self.last_success_time.write().await = current_time;

        // 清空错误记录
        let mut first_error_time = self.first_error_time.write().await;
        if first_error_time.is_some() {
            info!("操作成功，清空错误记录");
            *first_error_time = None;
        }
    }

    /// 停止程序
    ///
    /// 通过running_sender发送停止信号来停止程序
    async fn stop_program(&self) {
        let mut sender_guard = self.running_sender.write().await;
        if let Some(sender) = sender_guard.take() {
            if let Err(_) = sender.send(()) {
                error!("发送程序停止信号失败");
            } else {
                info!("已发送程序停止信号");
            }
        } else {
            warn!("程序停止信号发送器已被使用或不可用");
        }
    }
}
